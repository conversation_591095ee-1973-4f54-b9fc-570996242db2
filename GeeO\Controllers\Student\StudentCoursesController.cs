﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GeeO.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Extensions;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Data.Dto;
using GeeO.Services;
using Microsoft.Extensions.Logging;
using GeeO.Common.Exceptions;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class StudentCoursesController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly IExcelService _excelService;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;

        public StudentCoursesController(GeeODbContext context,
                                        IStudentPaymentService studentPaymentService,
                                        IExcelService excelService,
                                        ILogger<StudentCoursesController> logger,
                                        IMapper mapper)
        {
            _context = context;
            _studentPaymentService = studentPaymentService;
            _excelService = excelService;
            _logger = logger;
            _mapper = mapper;
        }

        // GET: api/StudentCourses/GetStudentPaymentInfo/5
        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<StudentPaymentInfo>> GetStudentPaymentInfo(string studentId)
        {
            try
            {
                var studentPayments = await _studentPaymentService.GetStudentPayment(studentId);

                var totalSessions = await _studentPaymentService.GetNumberOfSessions(studentId);

                var totalStudiedSessions = await _studentPaymentService.GetNumberOfStudiedSessions(studentId);

                var totalAbsences = await _studentPaymentService.GetNumberOfAbsences(studentId);

                var totalRemainSessions = totalSessions < totalStudiedSessions ? 0 : totalSessions - totalStudiedSessions;

                var endDateOfsession = await _studentPaymentService.CalculateEndDateAsync(studentId, totalRemainSessions);

                var totalFrequentAbsences = await _studentPaymentService.NumberOfFrequentAbsencesAsync(studentId);

                var lastAttendanceDate = await _studentPaymentService.GetLastAttendanceDateAsync(studentId);

                var studentPaymentInfo = new StudentPaymentInfo()
                {
                    TotalSessions = totalSessions,
                    TotalStudiedSessions = totalStudiedSessions,
                    TotalRemainSessions = totalRemainSessions,
                    TotalAbsences = totalAbsences,
                    TotalFrequentAbsences = totalFrequentAbsences,
                    StudentPaymentList = studentPayments,
                    EndDateOfSession = DateTimeUtils.FormatDate(endDateOfsession),
                    LastAttendanceDate = DateTimeUtils.FormatDate(lastAttendanceDate),
                    PaymentRemaining = (int)(((float)(from pmt in studentPayments select pmt.Amount.HasValue ? pmt.Amount.Value : 0).Sum() / totalSessions) * totalRemainSessions)
                };

                return studentPaymentInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetStudentPaymentInfo with id: {studentId} has error: {ex.Message} - {ex.StackTrace}");
                return BadRequest(ex.Message);
            }
        }

        // GET: api/StudentCourses/GetStudentPaymentList/5
        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<IEnumerable<StudentCourse>>> GetStudentPaymentList(string studentId)
        {
            var studentPayments = _context.StudentCourse
                                    .Where(x => x.StudentId == studentId)
                                    .OrderByDescending(x => x.StartDate);

            return await studentPayments.ToListAsync().ConfigureAwait(false);
        }

        // POST: api/StudentCourses/GetListWithFilter
        [HttpPost("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<StudentCourseGrid>>> GetListWithFilter(string userId, PaymnentInfoFilterParams filterParams)
        {
            var paymentsInfo = await _studentPaymentService.FilterStudentPaymentsAsync(userId, filterParams).ConfigureAwait(false);
            return paymentsInfo.ToList();
        }

        // GET: api/students/campus/1234567890
        [HttpGet("campus/{campusId}")]
        public async Task<ActionResult<IEnumerable<StudentRemainSessionReport>>> GetStudentPaymentByCampus(string campusId)
        {
            var remainSessionReports = await _studentPaymentService.GetStudentRemainSesson(campusId).ConfigureAwait(false);
            return remainSessionReports.ToList();
        }

        [HttpPost("export/{userId}")]
        public async Task<ActionResult<IEnumerable<StudentCourseGrid>>> ExportPaymentInfo(string userId, PaymnentInfoFilterParams filterParams)
        {
            string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            string fileName = $"Payment - List_Payment_{DateTimeUtils.FormatDate(DateTime.Now)}.xlsx";
            var paymentsInfo = await _studentPaymentService.FilterStudentPaymentsAsync(userId, filterParams);
            var memory = await _excelService.ExportPaymentInfo(paymentsInfo);

            return File(memory, contentType, fileName);
        }

        // GET: api/StudentCourses
        [HttpGet]
        public async Task<ActionResult<IEnumerable<StudentCourseGrid>>> GetStudentCourse()
        {
            DateTime nowDate = DateTime.Now;
            DateTime nullDate = nowDate.AddYears(-50);

            var data = from sc in _context.StudentCourse
                       from std in _context.Student.Where(std => std.SuspendDate == null && std.TerminateDate == null && sc.StudentId == std.Id)
                       from cs in _context.ClassStudent.Where(cs => std.Id == cs.StudentId).DefaultIfEmpty()
                       from cls in _context.ClassCourse.Where(cls => cs.ClassId == cls.Id).DefaultIfEmpty()
                       from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today).DefaultIfEmpty()
                       select new StudentCourseGrid()
                       {
                           Id = std.Id,
                           SubId = sc.Id,
                           RouteId = cls.Id,
                           Name = sc.Name,
                           NumberOfSessions = sc.NumberOfSession,
                           StudiedSessions = cls.ClassLessons.Where(x => x.EndTime < nowDate && x.StartTime > sc.StartDate).Count(),
                           StartDate = sc.StartDate ?? nullDate,
                           EndDate = sc.EndDate ?? nullDate,
                           ClassCourse = cls.Name,
                           Student = std.StudentName,
                           StudentType = EnumExtensionMethods.GetDescription(cs.StudentType ?? ClassType.Unknown)
                       };

            return await data.OrderByDescending(x => x.StartDate).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/StudentCourses/5
        [HttpGet("{id}")]
        public async Task<ActionResult<StudentCourse>> GetStudentCourse(string id)
        {
            var studentCourse = await _context.StudentCourse.FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
            if (studentCourse == null)
            {
                return NotFound();
            }
            return studentCourse;
        }

        // PUT: api/StudentCourses/5/6
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPut("{id}/{userId}")]
        public async Task<IActionResult> PutStudentCourse(string id, string userId, StudentCourse studentCourse)
        {
            try
            {
                var entity = await _context.StudentCourse.FindAsync(id);
                if (entity == null)
                {
                    throw new NotFoundException("Record not found.");
                }

                // Track the original payments
                var existingPayments = await _context.InstallmentPayments.AsNoTracking().Where(x => x.StudentCourseId == id).ToListAsync();

                // Original total payment
                var totalPayment = existingPayments.Sum(x => x.Amount) + entity.Amount;

                var newTotalPayment = studentCourse.InstallmentPayments.Sum(x => x.Amount) + studentCourse.Amount;

                bool isAmountChanged = !totalPayment.Equals(newTotalPayment);
                bool isNumOfSessionChanged = !entity.NumberOfSession.Equals(studentCourse.NumberOfSession);

                studentCourse.ModifiedDate = DateTime.Now;
                studentCourse.ModifiedBy = userId;

                var paymentsToRemove = existingPayments
                    .Where(existingPayment => !studentCourse.InstallmentPayments
                    .Any(newPayment => newPayment.Id == existingPayment.Id))
                    .ToList();

                // Remove payments not in the new list
                foreach (var payment in paymentsToRemove)
                {
                    _context.InstallmentPayments.Remove(payment);
                }

                // Handle updates and additions for Installment Payments
                foreach (var newPayment in studentCourse.InstallmentPayments)
                {
                    if (newPayment.Id != null)
                    {
                        var paymentToUpdate = existingPayments.FirstOrDefault(x => x.Id == newPayment.Id);
                        if (paymentToUpdate != null)
                        {
                            newPayment.CreatedAt = paymentToUpdate.CreatedAt;
                            newPayment.CreatedBy = paymentToUpdate.CreatedBy;
                            _mapper.Map(newPayment, paymentToUpdate);
                        }
                    }
                    else
                    {
                        _context.InstallmentPayments.Add(newPayment);
                    }
                }

                _mapper.Map(studentCourse, entity);

                // Handle for StudentLessonLogData
                if (isAmountChanged)
                {
                    await _studentPaymentService.RecalculateAndUpdateFeesByCurrentPayment(
                        entity.StudentId,
                        entity.Id,
                        newTotalPayment ?? 0,
                        entity.NumberOfSession
                    );
                }

                if (isNumOfSessionChanged)
                {
                    await _studentPaymentService.RecalculateAndUpdateStudentFeesAndPayments(entity.StudentId);
                }
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"PutStudentCourse - {ex.Message} - {ex.StackTrace}");
                throw;
            }

            return NoContent();
        }

        // POST: api/StudentCourses/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPost("{userId}")]
        public async Task<ActionResult<StudentCourse>> PostStudentCourse(string userId, StudentCourse studentCourse)
        {
            if (studentCourse == null)
            {
                return BadRequest();
            }

            studentCourse.CreatedDate = DateTime.Now;
            studentCourse.CreatedBy = userId;
            _context.StudentCourse.Add(studentCourse);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStudentCourse", new { id = studentCourse.Id }, studentCourse);
        }

        // DELETE: api/StudentCourses/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<StudentCourse>> DeleteStudentCourse(string id)
        {
            var studentCourse = await _context.StudentCourse.FindAsync(id);
            if (studentCourse == null)
            {
                return NotFound();
            }

            _context.StudentCourse.Remove(studentCourse);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return studentCourse;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<StudentCourse>> GetStudentCourseByStudentId(string studentId)
        {
            var studentCourse = await _context.StudentCourse
                                            .Where(x => x.StudentId == studentId)
                                            .OrderByDescending(x => x.StartDate)
                                            .FirstOrDefaultAsync().ConfigureAwait(false);

            if (studentCourse == null)
            {
                return NotFound();
            }

            return studentCourse;
        }
    }
}
