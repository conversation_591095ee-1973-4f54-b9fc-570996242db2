﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class StudyLevelsController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly ILessonPlanService _lessonPlanService;

        public StudyLevelsController(GeeODbContext context, ILessonPlanService lessonPlanService)
        {
            _context = context;
            _lessonPlanService = lessonPlanService;
        }

        // GET: api/StudyLevels
        [HttpGet]
        public async Task<ActionResult<IEnumerable<StudyLevel>>> GetStudyLevels()
        {
            return await _lessonPlanService.GetStudyLevels().ConfigureAwait(false);
        }

        // GET: api/StudyLevels/5
        [HttpGet("{id}")]
        public async Task<ActionResult<StudyLevel>> GetStudyLevel(string id)
        {
            var studyLevel = await _context.StudyLevel.FindAsync(id);

            if (studyLevel == null)
            {
                return NotFound();
            }

            return studyLevel;
        }

        // GET: api/StudyLevels/exam/5
        [HttpGet("exam/{id}")]
        public async Task<ActionResult<List<ExamResultForm>>> GetExamStudyLevel(string id)
        {
            var exams = await _context.ExamResultForm.Where(x => x.LevelId == id).ToListAsync();
            return exams.OrderBy(x => x.ExamType).ToList();
        }

        [HttpPut("exam/{id}")]
        public async Task<IActionResult> PutExamStudyLevel(string id, List<ExamResultForm> exams)
        {
            foreach (var item in exams)
            {
                var currentExam = await _context.ExamResultForm.FindAsync(item.Id);
                if (currentExam != null)
                {
                    currentExam.ExamType = item.ExamType;
                    currentExam.ExamName = item.ExamName;
                    currentExam.ExamFormJson = item.ExamFormJson;
                    currentExam.ModifiedDate = DateTime.Now;
                    currentExam.ModifiedBy = "sysadmin";

                    _context.Entry(currentExam).State = EntityState.Modified;
                }
                else
                {
                    var newExam = new ExamResultForm
                    {
                        LevelId = id,
                        ExamType = item.ExamType,
                        ExamName = item.ExamName,
                        ExamFormJson = item.ExamFormJson,
                        CreatedDate = DateTime.Now,
                        CreatedBy = "sysadmin"
                    };

                    _context.ExamResultForm.Add(newExam);
                }

                await _context.SaveChangesAsync().ConfigureAwait(false);
            }

            return Ok();
        }

        [HttpDelete("exam/{id}")]
        public async Task<IActionResult> DeleteExamStudyLevel(string id)
        {
            var entity = await _context.ExamResultForm.FindAsync(id);
            if (entity != null)
            {
                _context.ExamResultForm.Remove(entity);
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }

            return Ok();
        }

        // PUT: api/StudyLevels/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutStudyLevel(string id, StudyLevel studyLevel)
        {
            if (id != studyLevel.Id)
            {
                return BadRequest();
            }

            _context.Entry(studyLevel).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StudyLevelExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: 
        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> DuplicateStudyLevel(string id)
        {
            try
            {
                var studyLevel = await _lessonPlanService.DuplicateStudyLevel(id);
                if (studyLevel == null)
                {
                    return NotFound();
                }
                return Ok(new { Status = StatusCodes.Status200OK, Data = studyLevel });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        // POST: api/StudyLevels
        [HttpPost]
        public async Task<ActionResult<StudyLevel>> PostStudyLevel(StudyLevel studyLevel)
        {
            _context.StudyLevel.Add(studyLevel);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStudyLevel", new { id = studyLevel.Id }, studyLevel);
        }

        // DELETE: api/StudyLevels/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<StudyLevel>> DeleteStudyLevel(string id)
        {
            var studyLevel = await _context.StudyLevel.FindAsync(id);
            if (studyLevel == null)
            {
                return NotFound();
            }

            _context.StudyLevel.Remove(studyLevel);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return studyLevel;
        }

        private bool StudyLevelExists(string id)
        {
            return _context.StudyLevel.Any(e => e.Id == id);
        }
    }
}
