﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[CatchUpScheduleStudent]    Script Date: 9/27/2019 8:05:43 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE TABLE [dbo].[StudentCatchUp](
	[Id] [nvarchar](450) NOT NULL,
	[StudentId] [nvarchar](450) NOT NULL,
	[CatchUpSchedulesId] [nvarchar](450) NOT NULL,
	[IsFinish] [bit] NOT NULL,
 CONSTRAINT [PK_StudentCatchUp] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentCatchUp]  WITH CHECK ADD  CONSTRAINT [FK_StudentCatchUp_CatchUpSchedules] FOREIGN KEY([CatchUpSchedulesId])
REFERENCES [dbo].[CatchUpSchedules] ([Id])
GO

ALTER TABLE [dbo].[StudentCatchUp] CHECK CONSTRAINT [FK_StudentCatchUp_CatchUpSchedules]
GO

ALTER TABLE [dbo].[StudentCatchUp]  WITH CHECK ADD  CONSTRAINT [FK_StudentCatchUp_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[StudentCatchUp] CHECK CONSTRAINT [FK_StudentCatchUp_Student]
GO
