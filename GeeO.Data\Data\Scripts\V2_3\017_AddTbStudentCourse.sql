﻿USE [GeeODb]
GO

/***** Object:  Table [dbo].[StudentCourse]    Script Date: 12-Nov-19 8:51:35 PM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentCourse](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](450) NULL,
	[StartDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[ClassStudentId] [nvarchar](450) NULL,
 CONSTRAINT [PK_StudentCourse] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentCourse]  WITH CHECK ADD  CONSTRAINT [FK_StudentCourse_ClassStudent] FOREIGN KEY([ClassStudentId])
REFERENCES [dbo].[ClassStudent] ([Id])
GO

ALTER TABLE [dbo].[StudentCourse] CHECK CONSTRAINT [FK_StudentCourse_ClassStudent]
GO
