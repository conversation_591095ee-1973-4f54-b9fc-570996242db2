USE [GeeODbStg]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/****** For SSIS import from local to Azure    Script Date: 11/14/2022 8:39:53 PM ******/
CREATE TABLE [dbo].[LessonPlanStg](
	[Id] [nvarchar](450) NOT NULL,
	[LevelId] [nvarchar](450) NOT NULL,
	[Lesson] [nvarchar](256) NULL,
	[Subject] [nvarchar](256) NULL,
	[Content] [nvarchar](512) NULL,
	[Tb] [nvarchar](256) NULL,
	[CreatedDate] [datetime] NULL,
	[WorksheetFileName] [nvarchar](512) NULL
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[LessonPlanUnitStg](
	[Id] [nvarchar](450) NOT NULL,
	[LessonPlanId] [nvarchar](450) NOT NULL,
	[MaterialId] [nvarchar](450) NULL,
	[SortOrder] [int] NULL,
	[Time] [int] NULL,
	[Procedures] [nvarchar](2048) NULL,
	[Description] [nvarchar](2048) NULL,
	[Materials] [nvarchar](2048) NULL,
	[TeacherActivities] [nvarchar](4000) NULL,
	[LearningOutcome] [nvarchar](4000) NULL,
	[Note] [nvarchar](2048) NULL,
	[Category] [nvarchar](1024) NULL
) ON [PRIMARY]
GO


/****** For synchronize Azure to local    Script Date: 11/14/2022 8:39:53 PM ******/
CREATE TABLE [dbo].[StudyLevel_Prod](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](256) NULL,
	[Description] [nvarchar](256) NULL,
	[NumberOfLesson] [int] NULL,
	[LessonTime] [int] NULL
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[LessonPlan_Prod](
	[Id] [nvarchar](450) NOT NULL,
	[LevelId] [nvarchar](450) NOT NULL,
	[Lesson] [nvarchar](256) NULL,
	[Subject] [nvarchar](256) NULL,
	[Content] [nvarchar](512) NULL,
	[Tb] [nvarchar](256) NULL,
	[CreatedDate] [datetime] NULL,
	[WorksheetFileName] [nvarchar](512) NULL
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[LessonPlanUnit_Prod](
	[Id] [nvarchar](450) NOT NULL,
	[LessonPlanId] [nvarchar](450) NOT NULL,
	[MaterialId] [nvarchar](450) NULL,
	[SortOrder] [int] NULL,
	[Time] [int] NULL,
	[Procedures] [nvarchar](2048) NULL,
	[Description] [nvarchar](2048) NULL,
	[Materials] [nvarchar](2048) NULL,
	[TeacherActivities] [nvarchar](4000) NULL,
	[LearningOutcome] [nvarchar](4000) NULL,
	[Note] [nvarchar](2048) NULL,
	[Category] [nvarchar](1024) NULL
) ON [PRIMARY]
GO
