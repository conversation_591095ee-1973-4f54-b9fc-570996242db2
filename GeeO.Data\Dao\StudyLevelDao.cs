﻿using GeeO.Data;
using GeeO.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Dao
{
    public class StudyLevelDao
    {
        protected readonly GeeODbContext _context;

        public StudyLevelDao(GeeODbContext context)
        {
            _context = context;
        }

        public async Task<List<StudyLevel>> GetStudyLevels()
        {
            return await _context.StudyLevel.OrderBy(x => x.Name).ToListAsync();
        }

        private bool StudyLevelExists(string id)
        {
            return _context.StudyLevel.Any(e => e.Id == id);
        }
    }
}
