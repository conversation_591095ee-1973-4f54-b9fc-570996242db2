﻿IF EXISTS (SELECT 1 FROM sys.objects WHERE [name] = N'trg_LessonPlanUnitChanged' AND [type] = 'TR')
BEGIN
    DROP TRIGGER [dbo].[trg_LessonPlanUnitChanged];
END
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER dbo.trg_LessonPlanUnitChanged
   ON  dbo.LessonPlanUnit
   AFTER INSERT, UPDATE
AS 
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @level nvarchar(100),
			@lesson nvarchar(100),
			@eventTitle  nvarchar(1000);

	DECLARE @TimeToCheck datetime = SYSDATETIMEOFFSET() AT TIME ZONE 'SE Asia Standard Time';

	SELECT @level = lv.[Name], 
		   @lesson = lp.Lesson
	FROM LessonPlan lp WITH(NOLOCK)
	JOIN INSERTED lpu WITH(NOLOCK) ON lp.Id = lpu.LessonPlanId
	JOIN StudyLevel lv WITH(NOLOCK) ON lp.LevelId = lv.Id

	SET @eventTitle = N'Lesson plan level ' + @level + N', lesson ' + @lesson + N' thay đổi.';

	IF NOT EXISTS (SELECT * FROM [dbo].[Notifications] WHERE [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE()))

		INSERT INTO [dbo].[Notifications]
					([Id]
					,[Title]
					,[Content]
					,[StartTime]
					,[EndTime]
					,[CreatedDate]
					,[CreatedBy])
				VALUES
					(LOWER(CONVERT(nvarchar(450), NEWID()))
					,@eventTitle
					,@eventTitle
					,@TimeToCheck
					,DATEADD(mi, 5, @TimeToCheck)
					,@TimeToCheck
					,'sysadmin');
END
GO
