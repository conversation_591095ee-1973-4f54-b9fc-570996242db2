USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormProcessTriggers]    Script Date: 3/26/2025 5:51:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormProcessTriggers](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormProcessId] [nvarchar](450) NULL,
	[ProcessAction] [int] NULL,
	[TriggerName] [nvarchar](100) NULL,
	[ApiUrl] [varchar](max) NULL,
	[HttpMethod] [varchar](10) NULL,
	[ContentType] [varchar](100) NULL,
	[Payload] [nvarchar](max) NULL,
	[Description] [nvarchar](500) NULL,
	[IsConditionTrigger] [bit] NULL,
	[ConditionTriggers] [nvarchar](max) NULL,
	[Parameters] [nvarchar](max) NULL,
	[Headers] [nvarchar](max) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormProcessTriggers] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcessTriggers] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcessTriggers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcessTriggers_RequestFormProcess] FOREIGN KEY([RequestFormProcessId])
REFERENCES [dbo].[RequestFormProcess] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcessTriggers] CHECK CONSTRAINT [FK_RequestFormProcessTriggers_RequestFormProcess]
GO


