import React, { Component } from 'react';
//import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import classNames from 'classnames';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
//import { ActionIconButton, NormalTooltip } from '../ui/ButtonStyles';
import { CustomTableCell } from '../../ui/table/CustomizedTable';

const stylesMyLessonPresentTable = theme => ({
  root: {
    width: '100%',
    marginTop: theme.spacing(1),
    overflowX: 'auto'
  },
  table: {
    minWidth: 700
  },
  col: {
    maxWidth: 180,
    padding: '4px 15px'
  },
  colRunStatus: {
    width: 100
  },
  materialFName: {
    maxWidth: 250,
    width: 250
  }
});

class MyLessonPresentTable extends Component {
  constructor(...args) {
    super(...args);
    const { rows } = this.props;
    this.state = {
      data: rows
    };
  }

  render() {
    const { classes, rows, cols, runStatusBox } = this.props;

    return (
      <Paper className={classes.root}>
        <Table className={classes.table}>
          <TableHead>
            <TableRow>
              <CustomTableCell
                component="th"
                scope="row"
                align="center"
                className={classNames(classes.col, classes.colRunStatus)}
              >
                Running Status
              </CustomTableCell>
              {cols.map(col => (
                <CustomTableCell
                  className={classes.col}
                  component="th"
                  scope="row"
                  align={col.align}
                  key={col.name}
                >
                  {col.header}
                </CustomTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, idx) => (
              <TableRow className={classes.row} key={row.id}>
                <CustomTableCell className={classes.col} align="center">
                  {runStatusBox[idx]}
                </CustomTableCell>
                {cols.map(col => (
                  <CustomTableCell
                    align={col.align}
                    key={row.id + col.name}
                    className={
                      col.className === undefined
                        ? classes.col
                        : classNames(classes.col, classes[col.className])
                    }
                  >
                    <Typography
                      variant="body2"
                      noWrap={col.noWrap === undefined ? undefined : true}
                    >
                      {col.obj && row[col.name] !== null
                        ? row[col.name][col.objName]
                        : row[col.name]}
                    </Typography>
                  </CustomTableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Paper>
    );
  }
}

MyLessonPresentTable.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(stylesMyLessonPresentTable)(MyLessonPresentTable);
