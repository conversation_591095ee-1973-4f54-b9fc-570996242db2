﻿using GeeO.Data;
using GeeO.Data.Dto.WorkingTime;
using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace GeeO.Controllers.WorkingTime
{
    [Route("api/[controller]/external")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public UserController(GeeODbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<WokingTimeUser>>> GetAspNetUsers()
        {
            var campusList = await _context.Campus.ToListAsync();
            var result = await _context.AspNetUsers
                .Include(x => x.Role)
                .Include(x => x.UserInfo)
                .Select(x => new WokingTimeUser()
                {
                    Id = x.Id,
                    Email = x.Email,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    UserName = x.UserName,
                    PhoneNumber = x.PhoneNumber,
                    Role = x.Role != null ? x.Role.Name : "",
                    Campus = GetCampusList(x.UserInfo.AssignedCampus, campusList)
                }).ToListAsync().ConfigureAwait(false);

            return result;
        }

        private static string GetCampusList(string assignedCampus, List<Campus> campusList)
        {
            List<string> assignedCampusList = new List<string>();
            if (assignedCampus != null)
            {
                try
                {
                    assignedCampusList = JsonSerializer.Deserialize<List<string>>(assignedCampus);
                }
                catch (JsonException ex)
                {
                    assignedCampusList = new List<string>();
                }

                if (assignedCampusList.Count > 0)
                {
                    var matchingCampuses = campusList.Where(c => assignedCampusList.Contains(c.Id.ToString())).ToList();

                    var campusNames = string.Join(", ", matchingCampuses.Select(c => c.Name));
                    return campusNames;
                }
            }
            return string.Empty;
        }

    }
}
