﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[UserDocuments]    Script Date: 11/25/2024 11:47:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserDocuments](
	[Id] [nvarchar](450) NOT NULL PRIMARY KEY,
	[UserInfoId] [nvarchar](450) NULL,
	[DocumentType] [nvarchar](50) NULL,
	[FileName] [nvarchar](255) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
) 
GO

ALTER TABLE [dbo].[UserDocuments]  WITH CHECK ADD  CONSTRAINT [FK_UserDocuments_UserInfo] FOREIGN KEY([UserInfoId])
REFERENCES [dbo].[UserInfo] ([Id])
GO

ALTER TABLE [dbo].[UserDocuments] CHECK CONSTRAINT [FK_UserDocuments_UserInfo]
GO
