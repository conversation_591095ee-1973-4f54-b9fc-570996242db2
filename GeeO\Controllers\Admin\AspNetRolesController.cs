﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using GeeO.Common;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AspNetRolesController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly RoleManager<IdentityRole> _roleManager;

        public AspNetRolesController(GeeODbContext context, RoleManager<IdentityRole> roleManager)
        {
            _context = context;
            this._roleManager = roleManager;
        }

        // GET: api/AspNetRoles
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AspNetRoles>>> GetAspNetRoles()
        {
            //CreateRoleDefault().Wait();
            return await _context.AspNetRoles.OrderBy(r => r.Name).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/AspNetRoles/5
        [HttpGet("{id}")]
        public async Task<ActionResult<AspNetRoles>> GetAspNetRoles(string id)
        {
            var aspNetRoles = await _context.AspNetRoles.FindAsync(id);

            if (aspNetRoles == null)
            {
                return NotFound();
            }

            return aspNetRoles;
        }

        // PUT: api/AspNetRoles/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutAspNetRoles(string id, AspNetRoles aspNetRoles)
        {
            var role = await _roleManager.FindByIdAsync(id).ConfigureAwait(false);
            if (id != aspNetRoles.Id || role == null)
            {
                return BadRequest();
            }
            role.Name = aspNetRoles.Name;
            try
            {
                await _roleManager.UpdateAsync(role).ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AspNetRolesExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/AspNetRoles
        [HttpPost]
        public async Task<ActionResult<AspNetRoles>> PostAspNetRoles(AspNetRoles aspNetRoles)
        {
            var role = new IdentityRole()
            {
                Name = aspNetRoles.Name
            };
            try
            {
                await _roleManager.CreateAsync(role).ConfigureAwait(false);
            }
            catch (DbUpdateException)
            {
                if (AspNetRolesExists(aspNetRoles.Id))
                {
                    return Conflict();
                }
                else
                {
                    throw;
                }
            }

            return CreatedAtAction("GetAspNetRoles", new { id = aspNetRoles.Id }, aspNetRoles);
        }

        // DELETE: api/AspNetRoles/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<AspNetRoles>> DeleteAspNetRoles(string id)
        {
            var aspNetRoles = await _context.AspNetRoles.FindAsync(id);
            if (aspNetRoles == null)
            {
                return NotFound();
            }
            _context.AspNetRoles.Remove(aspNetRoles);
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return aspNetRoles;
        }

        private bool AspNetRolesExists(string id)
        {
            return _context.AspNetRoles.Any(e => e.Id == id);
        }
        public async Task CreateRoleDefault()
        {
            if (!await _roleManager.RoleExistsAsync(EnumsHelper.GetDescription(AcadRoles.Admin)).ConfigureAwait(false))
            {
                var role = new IdentityRole();
                role.Name = EnumsHelper.GetDescription(AcadRoles.Admin);
                await _roleManager.CreateAsync(role).ConfigureAwait(false);
            }
            if (!await _roleManager.RoleExistsAsync(EnumsHelper.GetDescription(AcadRoles.Teacher)).ConfigureAwait(false))
            {
                var role = new IdentityRole();
                role.Name = EnumsHelper.GetDescription(AcadRoles.Teacher);
                await _roleManager.CreateAsync(role).ConfigureAwait(false);
            }
        }
    }
}
