import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Fab from '@material-ui/core/Fab';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import SkipNextIcon from '@material-ui/icons/SkipNext';
import StopIcon from '@material-ui/icons/Stop';

const stylesRunButton = theme => ({
  fab: {
    margin: theme.spacing(1)
  }
});

class RunButtonComp extends Component {
  static displayName = RunButtonComp.name;

  constructor(...args) {
    super(...args);
    this.state = {
      started: false,
      itemCount: 0,
      itemIndex: 0
    };
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  getItemCount = childItemCount => {
    this.setState({ itemCount: childItemCount });
  };

  handleCallback = () => {
    this.props.handleCallback();
    this.setState({ started: true, itemIndex: this.state.itemIndex + 1 });
  };

  render() {
    const { classes } = this.props;
    const { started, itemCount, itemIndex } = this.state;

    return (
      <Fab
        aria-label="Start"
        color="primary"
        className={classes.fab}
        onClick={this.handleCallback}
      >
        {!started ? (
          <PlayArrowIcon />
        ) : itemIndex < itemCount ? (
          <SkipNextIcon />
        ) : (
          <StopIcon />
        )}
      </Fab>
    );
  }
}

RunButtonComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const RunButton = withStyles(stylesRunButton)(RunButtonComp);
