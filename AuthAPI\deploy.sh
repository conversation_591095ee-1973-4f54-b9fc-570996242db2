#!/bin/bash

echo "🔍 Looking for publish folders to delete..."

# Find and delete all publish folders under bin/Release/net*/publish
found_folders=false
for dir in ./bin/Release/net*/publish/; do
    if [ -d "$dir" ]; then
        echo "🗑️  Deleting: $dir"
        rm -rf "$dir"
        found_folders=true
    fi
done

if [ "$found_folders" = false ]; then
    echo "✅ No publish folders found to delete."
else
    echo "✅ Finished cleaning publish folders."
fi

echo ""
echo "🚀 Starting dotnet publish..."
dotnet publish -c Release

if [ $? -eq 0 ]; then
    echo "✅ Publish completed successfully."
else
    echo "❌ Publish failed."
    exit 1
fi

# Pause at the end
echo ""
read -p "Press Enter to exit..."