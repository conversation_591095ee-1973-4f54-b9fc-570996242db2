/*******************************************
  = LAYOUT
*******************************************/
* {
  -webit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 0;
  margin: 0px;
  padding: 0px;
}

.row-student {
  display: flex;
  position: relative;
  height: auto;
  width: 100%;
  /* -webkit-box-shadow: 0px 3px 0px 0px rgba(97, 53, 0, 1);
  -moz-box-shadow: 0px 3px 0px 0px rgba(97, 53, 0, 1);
  box-shadow: 0px 3px 0px 0px rgba(97, 53, 0, 1); */
  border-bottom: 3px solid rgba(97, 53, 0, 1);
}

.suspended {
  background-color: #70707038;
}
.attended {
  background-color: #d7fcd0;
}
.absent {
  background-color: #fcf6d0;
}
.no-attendance {
  background-color: #fcd0d0;
}

.left-side {
  position: relative;
  width: 500px;
  justify-content: center;
  display: flex;
  padding: 0 5px 0 10px;
}

.right-side {
  position: relative;
  width: 680px;
  height: 150px;
}

.sider1 {
  display: block;
}

.sider2 {
  display: flex;
}

.perform {
  width: 200px;
  position: relative;
  height: 140px;
}

/*******************************************
  = Name
*******************************************/
.name-id {
  position: relative;
  height: 150px;
  text-align: center;
}

.name-id h1 {
  margin-top: 58px !important;
  font-weight: 800;
  font-size: 26px;
  color: #f35626;
  background-image: -webkit-linear-gradient(92deg, #f35626, #feab3a);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-animation: hue 20s infinite linear;
  animation: hue 20s infinite linear;
}

@-webkit-keyframes hue {
  from {
    -webkit-filter: hue-rotate(0deg);
  }

  to {
    -webkit-filter: hue-rotate(360deg);
  }
}

@keyframes hue {
  from {
    -webkit-filter: hue-rotate(0deg);
  }

  to {
    -webkit-filter: hue-rotate(360deg);
  }
}

.swap {
  /* position: absolute;
  top: 0px;
  right: 0px;
  display: flex; */
  height: 100%;
  width: 50px;
  justify-content: center;
  opacity: 0.3;
  z-index: 5;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(252, 252, 252, 1) 50%,
    rgba(230, 230, 230, 1) 100%
  );
}

.swap:hover {
  opacity: 1;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.swap .fa {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 56px;
  color: #111;
  cursor: pointer;
}

/*******************************************
    = RATING
  *******************************************/
/* RATING - Form */
.rating-form {
  height: auto;
  width: auto;
  position: relative;
}

/* RATING - Form - Group */
.rating-form .form-group {
  position: relative;
  margin-top: 35px;
}

/* RATING - Form - Item */
.rating-form .form-item {
  position: relative;
  margin: auto;
  width: 100%;
  text-align: center;
  direction: rtl;
  /*background: green;*/
}

.rating-form .form-legend + .form-item {
  padding-top: 10px;
}

.rating-form input[type='radio'] {
  position: absolute;
  left: -9999px;
}

/* RATING - Form - Label */
.rating-form label {
  display: inline-block;
  cursor: pointer;
  padding: 10px;
}

.rating-form .rating-star {
  display: inline-block;
  position: relative;
}

.rating-form input[type='radio'] + label:before {
  position: absolute;
  font-size: 32px;
  opacity: 0;
  direction: ltr;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.rating-form input[type='radio']:checked + label:before {
  right: 25px;
  opacity: 1;
}

.rating-form input[type='radio'] + label:after {
  padding-left: 10px;
  position: absolute;
  font-size: 32px;
  opacity: 0;
  direction: ltr;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.rating-form input[type='radio']:checked + label:after {
  /*right: 5px;*/
  opacity: 1;
}

.rating-form label .fa {
  font-size: 32px;
  line-height: 65px;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.rating-form label:hover .fa-star-o,
.rating-form label:focus .fa-star-o,
.rating-form label:hover ~ label .fa-star-o,
.rating-form label:focus ~ label .fa-star-o,
.rating-form input[type='radio']:checked ~ label .fa-star-o {
  opacity: 0;
}

.rating-form label .fa-star {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.rating-form label:hover .fa-star,
.rating-form label:focus .fa-star,
.rating-form label:hover ~ label .fa-star,
.rating-form label:focus ~ label .fa-star,
.rating-form input[type='radio']:checked ~ label .fa-star {
  opacity: 1;
}

.rating-form input[type='radio']:checked ~ label .fa-star {
  color: gold;
}

/*******************************************
    = Performance form
*******************************************/
/* RATING - Form */
.per-form {
  display: flex;
  justify-content: center;
  height: 100%;
}

/* RATING - Form - Group */
.per-form .form-group {
  position: relative;
  padding: 0;
}

/* RATING - Form - Item */
.per-form .form-item {
  position: relative;
  margin: auto;
  text-align: center;
  direction: rtl;
  display: flex;
  height: 100%;
}

.per-form .form-legend + .form-item {
  padding-top: 10px;
}

.per-form input[type='radio'] {
  display: none;
}

/* RATING - Form - Label */
.per-form label {
  cursor: pointer;
  padding: 5px;
  margin-top: auto;
}

.per-form .rating-star {
  display: inline-block;
  position: relative;
}

.per-form input[type='radio'] + label:before {
  position: absolute;
  font-size: 16px;
  font-size: 1.6rem;
  opacity: 0;
  direction: ltr;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.per-form input[type='radio']:checked + label:before {
  right: 25px;
  opacity: 1;
}

.per-form input[type='radio'] + label:after {
  padding-left: 10px;
  position: absolute;
  right: 5px;
  top: 96px;
  font-size: 16px;
  font-size: 1.6rem;
  opacity: 0;
  direction: ltr;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.per-form input[type='radio']:checked + label:after {
  /*right: 5px;*/
  opacity: 1;
}

.per-form label .fa {
  font-size: 26px;
  -webkit-transition: all 0s ease 0s;
  -moz-transition: all 0s ease 0s;
  -o-transition: all 0s ease 0s;
  transition: all 0s ease 0s;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.per-form label:hover .fa-check-circle-o,
.per-form label:focus .fa-check-circle-o,
.per-form label:hover ~ label .fa-check-circle-o,
.per-form label:focus ~ label .fa-check-circle-o,
.per-form input[type='radio']:checked ~ label .fa-check-circle-o {
  opacity: 0;
}

.per-form label .fa-check-circle {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.per-form label:hover .fa-check-circle,
.per-form label:focus .fa-check-circle,
.per-form label:hover ~ label .fa-check-circle,
.per-form label:focus ~ label .fa-check-circle,
.per-form input[type='radio']:checked ~ label .fa-check-circle {
  opacity: 1;
}

.per-form input[type='radio']:checked ~ label .fa-check-circle {
  color: rgb(19, 226, 71);
}

/*******************************************
    = EMOJI
  *******************************************/

.emoji-wrapper {
  text-align: center;
  height: 50px;
  overflow: hidden;
  position: absolute;
  top: 15px;
  left: 0px;
}

.emoji-wrapper:before,
.emoji-wrapper:after {
  height: 15px;
  width: 100%;
  position: absolute;
  left: 0;
  z-index: 1;
}

.emoji-wrapper:before {
  top: 0;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(white),
    color-stop(35%, white),
    to(rgba(255, 255, 255, 0))
  );
  background: linear-gradient(
    to bottom,
    white 0%,
    white 35%,
    rgba(255, 255, 255, 0) 100%
  );
}

.emoji-wrapper:after {
  bottom: 0;
  background: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(white),
    color-stop(35%, white),
    to(rgba(255, 255, 255, 0))
  );
  background: linear-gradient(
    to top,
    white 0%,
    white 35%,
    rgba(255, 255, 255, 0) 100%
  );
}

.emoji {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.emoji > svg {
  margin: 5px 0;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.rating-1-emoji {
  -webkit-transform: translateY(-50px);
  transform: translateY(-50px);
}

.rating-2-emoji {
  -webkit-transform: translateY(-100px);
  transform: translateY(-100px);
}

.rating-4-emoji {
  -webkit-transform: translateY(-150px);
  transform: translateY(-150px);
}

.rating-7-emoji {
  -webkit-transform: translateY(-200px);
  transform: translateY(-200px);
}

.rating-9-emoji {
  -webkit-transform: translateY(-250px);
  transform: translateY(-250px);
}

/*******************************************
    = Icon
  *******************************************/

.icon-perform {
  text-align: center;
  height: auto;
  overflow: hidden;
  position: absolute;
  top: 0px;
  left: 0px;
}

.icon-perform:before,
.icon-perform:after {
  height: 15px;
  width: 100%;
  position: absolute;
  left: 0;
  z-index: 1;
}

.icon-perform:before {
  top: 0;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(white),
    color-stop(35%, white),
    to(rgba(255, 255, 255, 0))
  );
  background: linear-gradient(
    to bottom,
    white 0%,
    white 35%,
    rgba(255, 255, 255, 0) 100%
  );
}

.icon-perform:after {
  bottom: 0;
  background: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(white),
    color-stop(35%, white),
    to(rgba(255, 255, 255, 0))
  );
  background: linear-gradient(
    to top,
    white 0%,
    white 35%,
    rgba(255, 255, 255, 0) 100%
  );
}

.icon {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.icon > img {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  padding: 5px 0;
}
