﻿@using Microsoft.Extensions.Hosting
@using Microsoft.AspNetCore.Mvc.ViewEngines
@inject IHostEnvironment Environment
@inject ICompositeViewEngine Engine
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Gee-O English</title>

    <environment include="Development">
        <link rel="stylesheet" href="~/Identity/lib/bootstrap/dist/css/bootstrap.css" />
    </environment>
    <environment exclude="Development">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
              asp-fallback-href="~/Identity/lib/bootstrap/dist/css/bootstrap.min.css"
              asp-fallback-test-class="sr-only" asp-fallback-test-property="position" asp-fallback-test-value="absolute" />
    </environment>
    <link rel="stylesheet" href="~/css/font-faces.css" />
    <link rel="stylesheet" href="~/css/typography.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/gee-o.css" />
    <link rel="shortcut icon" href="~/favicon.ico" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-light navbar-toggleable-sm bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand geeo-brand" href="~/">Gee-O English</a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex flex-sm-row-reverse">
                    @{
                        var result = Engine.FindView(ViewContext, "_LoginPartial", isMainPage: false);
                    }
                    @if (result.Success)
                    {
                        await Html.RenderPartialAsync("_LoginPartial");
                    }
                    else
                    {
                        throw new InvalidOperationException("The default Identity UI layout requires a partial view '_LoginPartial' " +
                            "usually located at '/Pages/_LoginPartial' or at '/Views/Shared/_LoginPartial' to work. Based on your configuration " +
                            $"we have looked at it in the following locations: {System.Environment.NewLine}{string.Join(System.Environment.NewLine, result.SearchedLocations)}.");
                    }
                </div>
            </div>
        </nav>
    </header>

    <div class="container">
        <partial name="_CookieConsentPartial" optional />
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>
    <footer class="footer border-top text-muted">
        <div class="container">
            &copy; 2019 - Gee-O English - <a asp-area="" asp-page="Privacy">Privacy</a>
        </div>
    </footer>

    <environment include="Development">
        <script src="~/Identity/lib/jquery/dist/jquery.js"></script>
        <script src="~/Identity/lib/bootstrap/dist/js/bootstrap.bundle.js"></script>
    </environment>
    <environment exclude="Development">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"
                asp-fallback-src="~/Identity/lib/jquery/dist/jquery.min.js"
                asp-fallback-test="window.jQuery"
                crossorigin="anonymous"
                integrity="sha384-tsQFqpEReu7ZLhBV2VZlAu7zcOV+rXbYlF2cqB8txI/8aZajjp4Bqd+V6D5IgvKT">
        </script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"
                asp-fallback-src="~/Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js"
                asp-fallback-test="window.jQuery && window.jQuery.fn && window.jQuery.fn.modal"
                crossorigin="anonymous"
                integrity="sha384-xrRywqdh3PHs8keKZN+8zzc5TX0GRTLCcmivcbNJWm2rs5C8PRhcEn3czEjhAO9o">
        </script>
    </environment>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @RenderSection("Scripts", required: false)
</body>
</html>
