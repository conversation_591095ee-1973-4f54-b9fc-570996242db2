import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import CustomizedTable from '../ui/table/CustomizedTable';
import authService from '../api-authorization/AuthorizeService';
import { Loading } from '../ui/Loading';

const styles = theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
    marginBottom: theme.spacing(2),
    overflowX: 'auto'
  },
  cell: {
    padding: theme.spacing(1, 0, 1, 2)
  }
});

class CatchUpScheduleSelectComp extends Component {
  static displayName = CatchUpScheduleSelectComp.name;
  constructor(...args) {
    super(...args);
    this.state = {
      data: [],
      lstSelected: [],
      loading: true,
      classId: this.props.classId,
      lstCurrentStudentId: this.props.lstCurrentStudentId
    };
  }

  componentDidMount() {
    this.populateData();
  }

  populateData = async () => {
    const token = await authService.getAccessToken();
    // const response = await fetch(
    //   `api/CatchUpSchedules/GetStudentCatchUpScheduleByClassId/${this.state.classId}`,
    //   {
    //     headers: !token ? {} : { Authorization: `Bearer ${token}` }
    //   }
    // );
    // const data = await response.json();
    // this.setState({ data: data, loading: false });
    const headers = {
      ...{ Accept: 'application/json', 'Content-Type': 'application/json' },
      ...(!token ? {} : { Authorization: `Bearer ${token}` })
    };
    await fetch(
      `api/CatchUpSchedules/GetStudentCatchUpScheduleByClassId/` +
        this.state.classId,
      {
        method: 'POST',
        body: JSON.stringify(this.state.lstCurrentStudentId),
        headers: headers
      }
    )
      .then(async response => {
        const data = await response.json();
        if (response.ok) {
          this.setState({ data: data, loading: false });
        } else throw new Error(response.status);
      })
      .catch(error => {
        console.error('Error:', error);
      });
  };

  callbackValueCheckboxTabl = e => {
    this.props.callbackValueCheckboxTabl(e);
  };

  render() {
    const { data } = this.state;
    const cols = [
      { name: 'studentName', header: 'Name', align: 'left' },
      { name: 'englishName', header: 'English Name', align: 'right' },
      { name: 'fatherName', header: 'Father', align: 'right' },
      { name: 'motherName', header: 'Father', align: 'right' }
    ];
    let contents = this.state.loading ? (
      <Loading />
    ) : (
      <CustomizedTable
        onRef={actualChild => (this.child1 = actualChild)}
        rows={data}
        cols={cols}
        isShowCheckbox={true}
        callbackFromParent={this.callbackValueCheckboxTabl}
        isPaging={true}
      />
    );
    return <Fragment>{contents}</Fragment>;
  }
}

CatchUpScheduleSelectComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const CatchUpScheduleSelect = withStyles(styles)(
  CatchUpScheduleSelectComp
);
