﻿using GeeO.Data;
using GeeO.Data.MobileModels;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class ParentController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly ILogger _logger;
        private readonly ISmsApiService _smsApiService;
        private readonly IEmailService _emailService;
        private readonly IParentService _parentService;
        private readonly IStudentService _studentService;
        private readonly IClassCourseService _classCourseService;

        public ParentController(GeeODbContext context,
            ILogger<ParentController> logger,
            ISmsApiService smsApiService, IEmailService emailService, IParentService parentService, IStudentService studentService, IClassCourseService classCourseService)
        {
            _context = context;
            _logger = logger;
            _smsApiService = smsApiService;
            _emailService = emailService;
            _parentService = parentService;
            _studentService = studentService;
            _classCourseService = classCourseService;
        }

        [HttpGet("[action]/{mobile}")]
        public async Task<ActionResult<Parent>> GetParent(string mobile)
        {
            if (string.IsNullOrEmpty(mobile))
            {
                return BadRequest();
            }

            var parent = await (from prn in _context.Parent.Where(prn => prn.PhoneNumber == mobile.Trim())
                                select prn)
                                 .FirstOrDefaultAsync().ConfigureAwait(false);

            if (parent == null)
            {
                return NotFound();
            }

            return parent;
        }

        [HttpGet("[action]/{parentId}")]
        public async Task<ActionResult<IEnumerable<Student>>> GetStudentsOfParent(string parentId)
        {
            if (string.IsNullOrEmpty(parentId))
            {
                return BadRequest();
            }

            var parent = from prn in _context.StudentParent.Where(prn => prn.ParentId == parentId)
                         from std in _context.Student.Where(std => std.Id == prn.StudentId)
                         select std;

            return await parent.ToListAsync().ConfigureAwait(false);
        }

        [HttpPost("[action]/{parentId}")]
        public async Task<ActionResult> SendOTPToParent(string parentId)
        {
            var parent = await _context.Parent
                                        .Where(u => u.Id == parentId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (parent == null)
            {
                return NotFound("Parent not found.");
            }

            try
            {
                string otpCode = await _smsApiService.SendOTPToParent(parent);
                await LogParentAuthenticationAsync(parent, otpCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while sending OTP to parent: {0}, phone: {1}", parent.Name, parent.PhoneNumber);
                throw;
            }
            return Ok("Sent OTP to parent.");
        }

        async Task LogParentAuthenticationAsync(Parent parent, string otpCode)
        {
            _context.Database.ExecuteSqlRaw($"UPDATE dbo.ParentAuthenticationLog SET Expired = 1 WHERE ParentId = @parentId", new SqlParameter("parentId", parent.Id));
            ParentAuthenticationLog newOTPCheck = new() { ParentId = parent.Id, CheckCode = otpCode, SentDateTime = DateTime.Now };
            _context.ParentAuthenticationLog.Add(newOTPCheck);
            await _context.SaveChangesAsync().ConfigureAwait(false);
            _logger.LogInformation("Sent OTP to parent: {0}, phone: {1}, OTP: {2}", parent.Name, parent.PhoneNumber, otpCode);
        }

        [HttpPost("[action]/{parentId}/{otpCode}")]
        public async Task<ActionResult<ParentAuthenticationLog>> AuthenticateParent(string parentId, string otpCode)
        {
            var parent = await _context.Parent
                                        .Where(u => u.Id == parentId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);
            if (parent == null)
            {
                return NotFound();
            }
            var otpCheck = await _context.ParentAuthenticationLog
                                        .Where(u => u.ParentId == parentId && u.CheckCode == otpCode &&
                                                    u.Expired == false &&
                                                    DateTime.Now.AddMinutes(-3) < u.SentDateTime)
                                        .OrderByDescending(x => x.SentDateTime)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            return otpCheck ?? new ParentAuthenticationLog { Id = "", Expired = true };
        }

        [HttpPost("feedback/{parentId}/{studentId}/{message}")]
        public async Task<IActionResult> SendFeedback(string parentId, string studentId, string message)
        {
            try
            {
                _logger.LogWarning("Sending feedback from parent: {0}, student: {1}, message: {2}", parentId, studentId, message);
                var parentInfo = await _parentService.GetParentById(parentId);
                var studentInfo = await _studentService.GetStudentById(studentId);
                var classStudent = studentInfo.ClassStudents.ToList().LastOrDefault(cs => cs.StudentType == ClassType.Regular);
                var classInfo = await _classCourseService.GetClassCourseById(classStudent.ClassId);

                var feedbackInfo = new FeedbackInfo { 
                    ParentName = parentInfo.Name,
                    ParentEmail = parentInfo.Email,
                    StudentName = studentInfo.StudentName,
                    PhoneNumber = parentInfo.PhoneNumber,
                    ClassName = classInfo.Name
                };
                await _emailService.SendEmailFeedback(message, feedbackInfo);
                return Ok();

            } 
            catch(Exception ex)
            {
                  _logger.LogError(ex, "Error while sending feedback from parent: {0}, student: {1}, message: {2}", parentId, studentId, message);
                return BadRequest("Error while sending feedback");  
            }
        }

    }
}
