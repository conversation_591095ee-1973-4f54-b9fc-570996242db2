﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Extensions;
using GeeO.Data.Dto;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StudentReportController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public StudentReportController(GeeODbContext context)
        {
            _context = context;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentAttending>>> GetStudentAttending(string classId)
        {
            var data = from cs in _context.ClassStudent.Where(cs => cs.ClassId == classId)
                       from std in _context.Student.Where(std => std.Id == cs.StudentId)
                       orderby std.StudentName
                       select new
                       {
                           Student = std,
                           Logs = std.StudentLessonLogDatas
                                    .Where(sll => sll.LessonLog.ClassLesson.ClassId == classId &&
                                                 (sll.Present == 1 || sll.Present == 0))
                       };

            var result = await data.ToListAsync().ConfigureAwait(false);

            var studentAttendings = result.Select(d =>
            {
                var studentLogs = d.Logs.ToList();
                int attendingNumber = studentLogs.Count(sll => sll.Present == 1);
                int totalNumber = studentLogs.Count();
                int absentNumber = totalNumber - attendingNumber;

                return new StudentAttending()
                {
                    Id = d.Student.Id,
                    StudentName = d.Student.StudentName,
                    EnglishName = d.Student.EnglishName + " - " + d.Student.StudentName,
                    AttendingNumber = attendingNumber,
                    TotalNumber = totalNumber,
                    AbsentNumber = absentNumber,
                    SumTotalAndAbsent = totalNumber + absentNumber
                };
            });

            return Ok(studentAttendings);
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<ClassAttendingStatistics>> GetAttendingStatistics(string classId)
        {
            var data = from cls in _context.ClassCourse.Where(cls => cls.Id == classId)
                       from cs in _context.ClassStudent.Where(cs => cs.ClassId == cls.Id)
                       from cl in _context.ClassLesson.Where(cl => cl.ClassId == cls.Id && cl.EndTime.Value < DateTime.Now)
                       from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id /*&& tll.HistoryLog == false*/)
                       select new ClassAttendingStatistics
                       {
                           Id = cls.Id,
                           LessonId = cl.LessonId,
                           AttendingCount = tll.StudentLogDatas.Where(sll => sll.Present == 1).Count(),
                           TerminateCount = cls.ClassStudents.Where(cs => cs.StudentType == ClassType.Terminated &&
                                                                         (cs.Student.TerminateDate != null || cs.Student.SuspendDate != null)).Count(),
                           TerminateOrSuspendedCount = cls.ClassStudents.Count(),
                           MovedInCount = cls.ClassStudents.Where(cs => cs.Student.StudentClassChanges.Count > 0).Count(),
                           NewRegisterCount = (from cs in _context.ClassStudent
                                                  join sc in _context.StudentCourse on cs.StudentId equals sc.StudentId
                                                  where cs.ClassId == cls.Id && sc.Type == TypeEnum.New
                                                  select sc).Count()
                       };

            var dataList = await data.ToListAsync().ConfigureAwait(false);

            var maxValue = dataList.Where(x => x.AttendingCount == dataList.Max(y => y.AttendingCount)).FirstOrDefault();
            var minValue = dataList.Where(x => x.AttendingCount == dataList.Min(y => y.AttendingCount)).FirstOrDefault();

            maxValue.MaxAttendingCount = maxValue.AttendingCount;
            maxValue.MinAttendingCount = minValue.AttendingCount;

            return maxValue;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<ClassAttendingStatistics>>> GetAttendingPerSession(string classId)
        {
            var data = from cls in _context.ClassCourse.Where(cls => cls.Id == classId)
                       from cl in _context.ClassLesson.Where(cl => cl.ClassId == cls.Id && cl.EndTime.Value < DateTime.Now)
                       from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id /*&& tll.HistoryLog == false*/)
                       orderby cl.StartTime
                       select new ClassAttendingStatistics
                       {
                           Id = cls.Id,
                           LessonId = cl.LessonId,
                           LessonDate = cl.StartTime,
                           AttendingCount = tll.StudentLogDatas.Where(sll => sll.Present == 1).Count()
                       };

            return await data.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentPaymentStatistics>>> GetStudentPaymentStatistics(string classId)
        {
            var data = from cs in _context.ClassStudent.Where(cs => cs.ClassId == classId)
                       from std in _context.Student.Where(std => std.Id == cs.StudentId && std.SuspendDate == null && std.TerminateDate == null)
                       select new StudentPaymentStatistics
                       {
                           StudentId = std.Id,
                           StudentName = std.StudentName,
                           EnglishName = std.EnglishName,
                           TotalPayment = (from sc in std.StudentCourses select sc.Amount.Value).Sum(),
                           NumberOfSessions = (from sc in std.StudentCourses select sc.NumberOfSession).Sum(),
                           StudiedSessions = (from sll in _context.StudentLessonLogData
                                              where sll.StudentInfoId == std.Id && sll.Present > -1
                                              join tll in _context.TeacherLessonLog on sll.LogId equals tll.Id
                                              select sll).Count()
                       };

            return await data.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<ClassExamResult>>> GetStudentExamResults(string classId)
        {
            List<ClassExamResult> classExamResults = new();
            for (int examType = 1; examType < 5; examType++)
            {
                var data = from cs in _context.ClassStudent.Where(cs => cs.ClassId == classId /*&& cs.StudentType == ClassType.Regular*/)
                           from std in _context.Student.Where(std => std.Id == cs.StudentId /*&& std.SuspendDate == null && std.TerminateDate == null*/)
                           from erf in _context.ExamResultForm.Where(x => x.ExamType == examType && x.LevelId == cs.ClassCourse.LevelId).DefaultIfEmpty()
                           from er in _context.ExamResult.Where(x => x.ClassId == cs.ClassId && x.StudentId == cs.StudentId && x.ExamType == examType).DefaultIfEmpty()
                           orderby std.StudentName
                           select new StudentExamResult
                           {
                               StudentId = std.Id,
                               StudentName = std.StudentName,
                               EnglishName = std.EnglishName,
                               ExamResultJson = er.ExamResultJson ?? erf.ExamFormJson ?? string.Empty
                           };

                List<StudentExamResult> examResults = await data.ToListAsync().ConfigureAwait(false);

                classExamResults.Add(new ClassExamResult
                {
                    ExamType = examType,
                    ExamResult = examResults
                });
            }

            return classExamResults;
        }

    }
}
