﻿/****** Object:  Table [dbo].[TaskSys]    Script Date: 20-Oct-19 1:56:09 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TaskSys](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](256) NULL,
	[Note] [nvarchar](2048) NULL,
	[Status] [int] NOT NULL,
	[CreateDate] [datetime] NULL,
	[ApprovedDate] [datetime] NULL,
	[ApprovedUserId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NOT NULL,
	[AssignUserId] [nvarchar](450) NULL,
 CONSTRAINT [PK_TaskSys] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

ALTER TABLE [dbo].[TaskSys]  WITH CHECK ADD  CONSTRAINT [FK_TaskSys_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO
ALTER TABLE [dbo].[TaskSys] CHECK CONSTRAINT [FK_TaskSys_AspNetUsers]
GO

ALTER TABLE [dbo].[TaskSys]  WITH CHECK ADD  CONSTRAINT [FK_TaskSys_AssignUser] FOREIGN KEY([AssignUserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON UPDATE SET NULL
ON DELETE SET NULL
GO
ALTER TABLE [dbo].[TaskSys] CHECK CONSTRAINT [FK_TaskSys_AssignUser]
GO

ALTER TABLE [dbo].[TaskSys]  WITH CHECK ADD  CONSTRAINT [FK_TaskSys_ApprovedUser] FOREIGN KEY([ApprovedUserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE NO ACTION
GO
ALTER TABLE [dbo].[TaskSys] CHECK CONSTRAINT [FK_TaskSys_ApprovedUser]
GO
