using GeeO.Api.Hubs.Clients;
using GeeO.Api.Models;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Api.Hubs
{
    public class LiveClassHub : Hub<ILiveClassClient>
    {
        public static readonly ConcurrentDictionary<string, string> OnlineClientsDict = new();
        public static readonly ConcurrentDictionary<string, List<LiveClassUser>> UsersByRoom = new();

        public bool IsClassOnline(string classLessonId)
        {
            bool result = UsersByRoom.Keys.Contains(classLessonId) && UsersByRoom[classLessonId].Any();
            return result;
        }

        public async Task<string> LoginAsync(string classLessonId, string userRole, string userId)
        {
            string sKey = $"{classLessonId}/{userRole}/{userId}";
            _ = OnlineClientsDict.TryRemove(sKey, out _);
            _ = OnlineClientsDict.TryAdd(sKey, Context.ConnectionId);

            LiveClassUser loginUser = new() { ConnectionId = Context.ConnectionId, ClassLessonId = classLessonId, UserRole = userRole, UserId = userId };

            if (UsersByRoom.Keys.Contains(classLessonId))
            {
                UsersByRoom[classLessonId].Add(loginUser);
            }
            else
            {
                _ = UsersByRoom.TryAdd(classLessonId, new List<LiveClassUser>() { loginUser });
            }

            List<LiveClassUser> usersInThisRoom = UsersByRoom[classLessonId].Where(x => x.UserId != userId).ToList();
            List<string> otherUsers = usersInThisRoom.Select(x => x.ConnectionId).ToList();
            
            await Clients.Clients(new List<string>() { Context.ConnectionId }).GetOtherUsersInClass(usersInThisRoom);
            await Clients.Clients(otherUsers).UserJoined(loginUser);

            return await Task.FromResult(Context.ConnectionId);
        }

        public override Task OnDisconnectedAsync(Exception exception)
        {
            string offUserId = OnlineClientsDict.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
            if (offUserId != null)
            {
                _ = OnlineClientsDict.TryRemove(offUserId, out _);
            }

            List<LiveClassUser> usersInThisRoom = UsersByRoom.FirstOrDefault(x => x.Value.Where(i => i.ConnectionId == Context.ConnectionId).Any()).Value;
            LiveClassUser logoutUser = usersInThisRoom?.FirstOrDefault(x => x.ConnectionId == Context.ConnectionId);
            if (logoutUser != null)
            {
                string classLessonId = logoutUser.ClassLessonId;
                usersInThisRoom.Remove(logoutUser);
                UsersByRoom[classLessonId] = usersInThisRoom;

                List<string> otherUsers = usersInThisRoom.Select(x => x.ConnectionId).ToList();
                Clients.Clients(otherUsers).UserLeaved(logoutUser);
            }

            return base.OnDisconnectedAsync(exception);
        }
    }
}