﻿--DECLARE @TimeToCheckPayment datetime = DATEADD(dd, 10, GETDATE());

INSERT INTO [dbo].[Notifications]
	([Id]
	,[ClassId]
	,[Title]
	,[Content]
	,[StartTime]
	,[EndTime]
	,[CreatedDate]
	,[CreatedBy])
SELECT 
	LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
	,ClassId
	,N'Học sinh ' + IIF(NULLIF(EnglishName, '') IS NULL, '', EnglishName + ', ') + StudentName + N' tại lớp ' + ClassName + N' sắp hết gói học phí.'
	,N'Thông báo sắp hết gói học phí.'
	,GETDATE()
	,DATEADD(mi, 5, GETDATE())
	,GETDATE()
	,'sysadmin'
FROM
(
SELECT cls.Id AS ClassId
	  ,cls.[Name] As ClassName
	  ,std.StudentName
	  ,std.EnglishName
	  ,sc.NumberOfSessions
	  ,sll.StudiedSessions
FROM Student std 
JOIN 
(
	SELECT sc.StudentId AS StudentId, SUM(sc.NumberOfSession) AS NumberOfSessions
	FROM StudentCourse sc 
	GROUP BY sc.StudentId
) sc ON sc.StudentId = std.Id
JOIN
(
	SELECT sll.StudentInfoId AS StudentId, COUNT(sll.Present) AS StudiedSessions
	FROM StudentLessonLogData sll 
	INNER JOIN TeacherLessonLog tll ON sll.LogId = tll.Id
	WHERE tll.HistoryLog = 0 AND sll.Present = 1
	GROUP BY sll.StudentInfoId
) sll ON sll.StudentId = std.Id
JOIN ClassStudent cs ON cs.StudentId = std.Id
JOIN ClassCourse cls ON cs.ClassId = cls.Id
JOIN Schedule sch ON cs.ClassId = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
) t
WHERE t.NumberOfSessions - t.StudiedSessions > 0 AND t.NumberOfSessions - t.StudiedSessions < 5
