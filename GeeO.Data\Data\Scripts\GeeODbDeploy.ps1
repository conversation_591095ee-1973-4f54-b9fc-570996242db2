
param([String]$Version)

$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

$servername = $settings.database.servername
$database = $settings.database.database
$user = $settings.database.user
$password = $settings.database.password

$SourceDirectory = [System.IO.Path]::Combine($settings.SourceDirectory, $Version)

#Set-Location -Path $settings.SourceDirectory

$ParamedScriptFile = "$SourceDirectory\\ExecuteScriptFile.sql"
$ExecuteScriptFile = $ParamedScriptFile

$files = Get-ChildItem -Path $SourceDirectory -File -Filter '*.sql'
#$files = @(gci $SourceDirectory | where {$_.Extension -eq ".sql"})

foreach($file in $files)
{
    Write-Output -InputObject "Run SQL file: $($file.FullName)"
    $FileContent = Get-Content $file.FullName
    $containsWord = $FileContent | %{$_ -match $settings.ScriptLocationKey}
    if ($containsWord -contains $true) {
        $FileContent -replace $settings.ScriptLocationKey, $settings.SourceDirectory | Set-Content -Path $ParamedScriptFile -Encoding $settings.Encoding
        $ExecuteScriptFile = $ParamedScriptFile
    }
    else
    {
        $ExecuteScriptFile = $file.FullName
    }
    Invoke-Sqlcmd -ServerInstance $servername -Database $database -Username  $user -Password $password -InputFile $ExecuteScriptFile
}

if (Test-Path -Path $ParamedScriptFile)
{
    Write-Output -InputObject "Remove file: $ParamedScriptFile"
    Remove-Item -Path $ParamedScriptFile -Force
}
