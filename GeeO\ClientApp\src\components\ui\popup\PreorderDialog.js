import React, { useState } from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox
} from '@material-ui/core';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { withStyles } from '@material-ui/core/styles';
import { Box, Typography } from '@mui/material';
import { fetchHelper } from '../../../helpers/fetch-helper';

const CustomTableCell = withStyles(theme => ({
  head: {
    fontFamily: 'RalewayBold',
    fontWeight: 'normal',
    fontSize: 14,
    textTransform: 'uppercase',
    backgroundColor: 'rgba(224, 224, 224, 0.5)',
    color: theme.palette.text.primary,
    padding: theme.spacing(1.75, 2.5),
    paddingLeft: 10,
    paddingRight: 10,
    minHeight: 41,
    border: 'none'
  },
  body: {
    fontFamily: 'RalewayMedium',
    fontWeight: 'normal',
    fontSize: 14,
    color: theme.palette.text.secondary,
    maxWidth: 250,
    minHeight: 41,
    padding: theme.spacing(1, 2.5),
    paddingLeft: 10,
    paddingRight: 10
  }
}))(TableCell);

export const PreorderDialog = ({
  open,
  onClose,
  data,
  onSave,
  refreshLessonPlans
}) => {
  const [lessonData, setLessonData] = useState(data);
  const [originalLessonPlan, setOriginalLessonPlan] = useState([]);
  const [selectedLessons, setSelectedLessons] = useState([]);

  const onDragEnd = result => {
    const { destination, source } = result;
    if (!destination) return;
    if (originalLessonPlan.length === 0) {
      setOriginalLessonPlan(lessonData);
    }
    setLessonData(prev => {
      const newData = [...prev];
      const draggedLesson = newData[source.index];
      const replacedLesson = newData[destination.index];
      newData[destination.index] = {
        ...draggedLesson
      };
      newData[source.index] = {
        ...replacedLesson
      };
      return newData;
    });
  };

  const handleSave = () => {
    onSave(lessonData);
  };

  const handleCheckboxChange = lessonId => {
    setSelectedLessons(prevSelected =>
      prevSelected.includes(lessonId)
        ? prevSelected.filter(id => id !== lessonId)
        : [...prevSelected, lessonId]
    );
  };

  const handleRemoveContent = async () => {
    if (selectedLessons.length === 0) return;
    const lessonIds = selectedLessons;
    try {
      const res = await fetchHelper.post(
        `api/LessonPlans/RemoveContent/`,
        lessonIds
      );
      refreshLessonPlans();
      onClose();
    } catch (error) {
      console.error('Delete LessonPlan Content Error!', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogContent>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={2}
        >
          <Typography variant="h5" color="#BE3144">
            Preorder Overall
          </Typography>
          <Button
            variant="contained"
            size="small"
            style={{ borderRadius: '30px', padding: '4px 12px', fontSize: 12 }}
            color="primary"
            onClick={handleRemoveContent}
          >
            Remove Content
          </Button>
        </Box>
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="preorder">
            {provided => (
              <TableContainer
                component={Paper}
                {...provided.droppableProps}
                ref={provided.innerRef}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <CustomTableCell style={{ width: 80 }} align={'center'}>
                        <Checkbox
                          checked={
                            selectedLessons.length === lessonData.length &&
                            lessonData.length > 0
                          }
                          indeterminate={
                            selectedLessons.length > 0 &&
                            selectedLessons.length < lessonData.length
                          }
                          onChange={() => {
                            if (selectedLessons.length === lessonData.length) {
                              setSelectedLessons([]);
                            } else {
                              setSelectedLessons(
                                lessonData.map(lesson => lesson.id)
                              );
                            }
                          }}
                        />
                      </CustomTableCell>
                      <CustomTableCell style={{ width: 80 }} align={'center'}>
                        <TableCell>New Lesson</TableCell>
                      </CustomTableCell>
                      <CustomTableCell style={{ width: 80 }} align={'center'}>
                        <TableCell>Old Lesson</TableCell>
                      </CustomTableCell>
                      <CustomTableCell style={{ width: 80 }}>
                        <TableCell>Subject</TableCell>
                      </CustomTableCell>
                      <CustomTableCell style={{ width: 80 }} align={'center'}>
                        <TableCell>Content</TableCell>
                      </CustomTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lessonData.map((lesson, index) => (
                      <Draggable
                        key={lesson.id}
                        draggableId={lesson.id}
                        index={index}
                      >
                        {provided => (
                          <TableRow
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            <CustomTableCell
                              style={{ width: 80 }}
                              align={'center'}
                            >
                              <Checkbox
                                checked={selectedLessons.includes(lesson.id)}
                                onChange={() => handleCheckboxChange(lesson.id)}
                              />
                            </CustomTableCell>
                            <TableCell sx={{ width: '20%' }} align={'center'}>
                              {originalLessonPlan[index]?.lesson ||
                                lesson.lesson}
                            </TableCell>
                            <TableCell sx={{ width: '10%' }} align={'center'}>
                              {lesson.lesson}
                            </TableCell>
                            <TableCell sx={{ width: '30%' }}>
                              {lesson.subject}
                            </TableCell>
                            <TableCell sx={{ width: '30%' }}>
                              {lesson.content}
                            </TableCell>
                          </TableRow>
                        )}
                      </Draggable>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Droppable>
        </DragDropContext>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};
