﻿using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace GeeO.Common.FileLogging
{
    public class FileLogger : ILogger
    {
        private readonly string filePath;
        private static readonly object _lock = new();
        private readonly string configLogLevel;

        public FileLogger(string path, string logLevel)
        {
            filePath = path;
            configLogLevel = logLevel;
        }

        public IDisposable BeginScope<TState>(TState state)
        {
            return null;
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            Console.WriteLine("Log Level: {0}", logLevel.ToString());
            Console.WriteLine("Config Log Level: {0}", configLogLevel);
            //return logLevel.ToString() == configLogLevel;
            return true;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (formatter != null)
            {
                lock (_lock)
                {
                    string fullFilePath = Path.Combine(filePath, DateTime.Now.ToString("yyyy-MM-dd") + "_log.txt");
                    var n = Environment.NewLine;
                    string exc = "";
                    if (exception != null) exc = exception.GetType() + ": " + exception.Message + n + exception.StackTrace + n;
                    File.AppendAllText(fullFilePath, logLevel.ToString() + ": " + DateTime.Now.ToString() + " " + formatter(state, exception) + n + exc);
                }
            }
        }
    }
}
