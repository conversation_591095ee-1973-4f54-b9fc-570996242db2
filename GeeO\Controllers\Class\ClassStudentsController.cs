﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Extensions;
using GeeO.GridVo;
using GeeO.Models;
using Microsoft.Data.SqlClient;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassStudentsController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public ClassStudentsController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/ClassStudents
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClassStudent>>> GetClassStudent()
        {
            return await _context.ClassStudent.Where(x => x.Student.SuspendDate == null).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassStudents/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClassStudent>> GetClassStudent(string id)
        {
            var classStudent = await _context.ClassStudent.FindAsync(id);

            if (classStudent == null)
            {
                return NotFound();
            }

            return classStudent;
        }

        // GET: api/ClassStudents/5
        [HttpGet("[action]/{classId}/{studentId}")]
        public async Task<ActionResult<ClassStudent>> GetByClass(string classId, string studentId)
        {
            var classStudent = await _context.ClassStudent.Where(x => x.ClassId == classId && x.StudentId == studentId).FirstOrDefaultAsync();

            if (classStudent == null)
            {
                return NotFound();
            }

            return classStudent;
        }

        // PUT: api/ClassStudents/5
        [HttpPut("[action]/{id}/{studentType}")]
        public async Task<IActionResult> UpdateStudentType(string id, int studentType)
        {

            if (string.IsNullOrEmpty(id) || !Enum.IsDefined(typeof(ClassType), studentType))
            {
                return BadRequest();
            }

            ClassStudent classStudent = await _context.ClassStudent.Where(x => x.Id == id).FirstOrDefaultAsync();

            if (classStudent == null)
            {
                return NotFound();
            }

            classStudent.StudentType = (ClassType) studentType;
            _context.Entry(classStudent).State = EntityState.Modified;

            // Update to Suspend Student
            var student = await _context.Student.Where(u => u.Id == classStudent.StudentId).FirstAsync().ConfigureAwait(false);
            if ((ClassType)studentType == ClassType.Suspended) {
                student.SuspendDate = DateTime.Now;
                _context.Entry(student).State = EntityState.Modified;
            } else if ((ClassType)studentType == ClassType.Terminated)
            {
                student.TerminateDate = DateTime.Now;
                _context.Entry(student).State = EntityState.Modified;
            }

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ClassStudentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // PUT: api/ClassStudents/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutClassStudent(string id, ClassStudent classStudent)
        {
            if (classStudent == null || id != classStudent.Id)
            {
                return BadRequest();
            }

            _context.Entry(classStudent).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ClassStudentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/ClassStudents
        [HttpPost]
        public async Task<ActionResult<ClassStudent>> PostClassStudent(ClassStudent classStudent)
        {
            if (classStudent == null)
            {
                return BadRequest();
            }

            ClassStudent demoStudent = await _context.ClassStudent
                        .FirstOrDefaultAsync(cs => cs.StudentId == classStudent.StudentId && cs.StudentType == ClassType.Demo)
                        .ConfigureAwait(false);

            if (demoStudent != null)
            {
                _context.ClassStudent.Remove(demoStudent);
            }

            Student student = await _context.Student.Where(std => std.Id == classStudent.StudentId).FirstAsync();
            student.SuspendDate = null;
            student.SuspendMonths = null;
            student.SuspendReason = null;
            student.TerminateDate = null;
            student.TerminateReason = null;
            _context.Entry(student).State = EntityState.Modified;

            var existClassStudent = await _context.ClassStudent
                        .FirstOrDefaultAsync(x => x.ClassId == classStudent.ClassId & x.StudentId == classStudent.StudentId)
                        .ConfigureAwait(false);
            if (existClassStudent == null)
            {
                classStudent.RegisterDate = DateTime.Now;
                _context.ClassStudent.Add(classStudent);
            }
            else
            {
                existClassStudent.StudentType = classStudent.StudentType;
                _context.Entry(existClassStudent).State = EntityState.Modified;
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok(classStudent);
        }

        // DELETE: api/ClassStudents/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<ClassStudent>> DeleteClassStudent(string id)
        {
            var classStudent = await _context.ClassStudent.FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
            if (classStudent == null)
            {
                return NotFound();
            }

            _context.ClassStudent.Remove(classStudent);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return classStudent;
        }

        private bool ClassStudentExists(string id)
        {
            return _context.ClassStudent.Any(e => e.Id == id);
        }


        // GET: api/ClassStudents
        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetFreeStudents()
        {
            var demoStudents = from std in _context.Student.Include(std => std.StudentParents).Where(std => std.SuspendDate == null && std.TerminateDate == null)
                               from cs in _context.ClassStudent.Where(cs => cs.StudentId == std.Id && cs.StudentType == ClassType.Demo)
                               select new StudentGrid
                               {
                                   Id = std.Id,
                                   StudentName = std.StudentName,
                                   Birthday = std.Birthday,
                                   EnglishName = std.EnglishName,
                                   FatherName = std.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                   MotherName = std.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                   SuspendDate = null,
                                   TerminateDate = null,
                                   StudentType = cs.StudentType ?? ClassType.Unknown,
                               };

            var suspendStudents = from std in _context.Student.Where(std => std.SuspendDate != null || std.TerminateDate != null).Include(std => std.StudentParents)
                                  select new StudentGrid
                                  {
                                      Id = std.Id,
                                      StudentName = std.StudentName,
                                      Birthday = std.Birthday,
                                      EnglishName = std.EnglishName,
                                      FatherName = std.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                      MotherName = std.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                      SuspendDate = std.SuspendDate,
                                      TerminateDate = std.TerminateDate,
                                      StudentType = ClassType.Unknown,
                                  };

            var result = await demoStudents.Union(suspendStudents).ToListAsync().ConfigureAwait(false);
            return result;
        }

        // GET: api/ClassStudents
        [HttpGet]
        [Route("list-filter-student-class-notuse/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentInPopupClass(string classId)
        {
            var dataStudentInClass = await _context.ClassStudent.Where(x => x.ClassId == classId).Select(x => x.StudentId).ToListAsync().ConfigureAwait(false);
            var result = await _context.Student.Where(x => !dataStudentInClass.Contains(x.Id) && x.SuspendDate == null).Select(x => new StudentGrid()
            {
                Id = x.Id,
                StudentName = x.StudentName,
                Birthday = x.Birthday,
                EnglishName = x.EnglishName,
                FatherName = x.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                MotherName = x.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                ValDropdown = "1",
                Present = false
            }).ToListAsync().ConfigureAwait(false);
            return result;
        }

        // POST: api/ClassStudents
        [HttpPost("[action]/{classId}")]
        public async Task<ActionResult> AddStudent(string classId, List<string> studentIds)
        {
            foreach (var id in studentIds)
            {
                ClassStudent demoStudent = await _context.ClassStudent.Where(cs => cs.StudentId == id && cs.StudentType == ClassType.Demo).FirstOrDefaultAsync();
                if (demoStudent != null && demoStudent.ClassId != classId)
                {
                    _context.ClassStudent.Remove(demoStudent);
                }

                Student student = await _context.Student.Where(std => std.Id == id).FirstAsync();
                student.SuspendDate = null;
                student.SuspendMonths = null;
                student.SuspendReason = null;
                student.TerminateDate = null;
                student.TerminateReason = null;
                _context.Entry(student).State = EntityState.Modified;

                ClassStudent classStudent = await _context.ClassStudent.Where(cs => cs.ClassId == classId && cs.StudentId == id).FirstOrDefaultAsync();
                if (classStudent == null)
                {
                    classStudent = new()
                    {
                        ClassId = classId,
                        StudentId = id,
                        StudentType = ClassType.Regular
                    };
                    _context.ClassStudent.Add(classStudent);
                }
                else
                {
                    classStudent.StudentType = ClassType.Regular;
                    _context.Entry(classStudent).State = EntityState.Modified;
                }
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        // GET: api/ClassStudents
        [HttpGet]
        [Route("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentByClass(string classId)
        {
            //Schedule cls = await _context.Schedule.Where(x => x.ClassCourseId == classId)
            //                                      .FirstOrDefaultAsync().ConfigureAwait(false);
            //bool finished = cls.EndDate < DateTime.Today;

            var dataStudentInClass = from cs in _context.ClassStudent.Where(x => x.ClassId == classId && x.StudentType != ClassType.Suspended && x.StudentType != ClassType.Terminated)
                                     from el in _context.StudentExternalAccount.Where(el => cs.StudentId == el.StudentId).DefaultIfEmpty()
                                     orderby cs.Student.StudentName
                                     select new StudentGrid()
                                     {
                                         Id = cs.Id,
                                         SubId = cs.StudentId,
                                         SortOrder = cs.SortOrder ?? 0,
                                         StudentName = cs.Student.StudentName,
                                         EnglishName = cs.Student.EnglishName,
                                         Birthday = cs.Student.Birthday,
                                         Traits = cs.Student.Traits,
                                         FatherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                         FatherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         MotherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                         MotherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         ElAccount = el.Email ?? string.Empty,
                                         StudentType = cs.StudentType.GetValueOrDefault(),
                                         ClassType = EnumExtensionMethods.GetDescription(cs.StudentType ?? ClassType.Unknown),
                                     };

            return await dataStudentInClass.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet]
        [Route("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentsByCompletedClass(string classId)
        {

            var dataStudentInClass = from cs in _context.ClassStudent.Where(x => x.ClassId == classId)
                                     from el in _context.StudentExternalAccount.Where(el => cs.StudentId == el.StudentId).DefaultIfEmpty()
                                     orderby cs.Student.StudentName
                                     select new StudentGrid()
                                     {
                                         Id = cs.Id,
                                         SubId = cs.StudentId,
                                         SortOrder = cs.SortOrder ?? 0,
                                         StudentName = cs.Student.StudentName,
                                         EnglishName = cs.Student.EnglishName,
                                         Birthday = cs.Student.Birthday,
                                         Traits = cs.Student.Traits,
                                         FatherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                         FatherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         MotherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                         MotherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         ElAccount = el.Email ?? string.Empty,
                                         StudentType = cs.StudentType.GetValueOrDefault(),
                                         ClassType = EnumExtensionMethods.GetDescription(cs.StudentType ?? ClassType.Unknown),
                                     };

            return await dataStudentInClass.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetSuspendStudentByClass(string classId)
        {
            var dataStudentInClass = from cs in _context.ClassStudent.Where(x => x.ClassId == classId &&
                                                                            (x.StudentType == ClassType.Suspended || x.StudentType == ClassType.Terminated))
                                     from el in _context.StudentExternalAccount.Where(el => cs.StudentId == el.StudentId).DefaultIfEmpty()
                                     orderby cs.Student.StudentName
                                     select new StudentGrid()
                                     {
                                         Id = cs.Id,
                                         SubId = cs.StudentId,
                                         SortOrder = cs.SortOrder ?? 0,
                                         StudentName = cs.Student.StudentName,
                                         EnglishName = cs.Student.EnglishName,
                                         Birthday = cs.Student.Birthday,
                                         Traits = cs.Student.Traits,
                                         FatherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                         FatherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         MotherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                         MotherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         ElAccount = el.Email ?? string.Empty,
                                         StudentType = cs.StudentType.GetValueOrDefault(),
                                         ClassType = EnumExtensionMethods.GetDescription(cs.StudentType ?? ClassType.Unknown),
                                     };

            return await dataStudentInClass.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassStudents/GetStudentPresenceByClass
        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Object>>> GetStudentPresenceByClass(string classId)
        {
            var result = from cl in _context.ClassLesson.Where(cl => cl.ClassId == classId && cl.StartTime.Value.Date == DateTime.Now.Date)
                         from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id)
                         from sll in _context.StudentLessonLogData.Where(sll => sll.LogId == tll.Id)
                         from std in _context.Student.Where(std => std.Id == sll.StudentInfoId)
                         where sll.Present == 1
                         orderby std.StudentName
                         select new
                         {
                             std.Id,
                             std.StudentName,
                             std.EnglishName
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassStudents/GetStudentPresenceByClassLesson
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<Object>>> GetStudentPresenceByClassLesson(string classLessonId)
        {
            var result = from cl in _context.ClassLesson.Where(cl => cl.Id == classLessonId)
                         from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id)
                         from sll in _context.StudentLessonLogData.Where(sll => sll.LogId == tll.Id)
                         from std in _context.Student.Where(std => std.Id == sll.StudentInfoId)
                         where sll.Present == 1
                         orderby std.StudentName
                         select new
                         {
                             std.Id,
                             std.StudentName,
                             std.EnglishName
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassStudents
        [HttpGet]
        [Route("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentAttendanceByClass(string classId)
        {
            var dataStudentInClass = from cs in _context.ClassStudent.Where(x => x.ClassId == classId && x.StudentType != ClassType.Terminated)
                                     from el in _context.StudentExternalAccount.Where(el => cs.StudentId == el.StudentId).DefaultIfEmpty()
                                     orderby cs.Student.StudentName
                                     select new StudentGrid()
                                     {
                                         Id = cs.Id,
                                         SubId = cs.StudentId,
                                         SortOrder = cs.SortOrder ?? 0,
                                         StudentName = cs.Student.StudentName,
                                         EnglishName = cs.Student.EnglishName,
                                         Birthday = cs.Student.Birthday,
                                         Traits = cs.Student.Traits,
                                         FatherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.Name).FirstOrDefault(),
                                         FatherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 1).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         MotherName = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.Name).FirstOrDefault(),
                                         MotherPhone = cs.Student.StudentParents.Where(c => c.Parent.Relation == 2).Select(c => c.Parent.PhoneNumber).FirstOrDefault(),
                                         ElAccount = el.Email ?? string.Empty,
                                         StudentType = cs.StudentType.GetValueOrDefault(),
                                         ClassType = EnumExtensionMethods.GetDescription(cs.StudentType ?? ClassType.Unknown),
                                     };

            return await dataStudentInClass.ToListAsync().ConfigureAwait(false);
        }
    }
}
