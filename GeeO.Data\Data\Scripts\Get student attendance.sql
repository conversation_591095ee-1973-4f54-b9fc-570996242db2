			select cl.Id, lp.<PERSON>on, cl.StartTime as 'LessonDate',
				   std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			from ClassLesson cl 
			join LessonPlan lp on cl.LessonId = lp.Id
			join ClassStudent cs on cl.ClassId = cs.ClassId
			join Student std on cs.StudentId = std.Id
			left join TeacherLessonLog tll on cl.Id = tll.ClassLessonId 
			left join StudentLessonLogData sll on tll.Id = sll.LogId and std.Id = sll.StudentInfoId
			where cl.ClassId = '0a97f2fe-e326-4d44-b88a-0a35f18d44cb' and cl.StartTime < CONVERT(date, GETDATE())
			order by cl.StartTime, std.Id



select distinct cls.Id as 'ClassId', cls.[Name] as 'Class', 
		cl.Id as 'ClassLessonId', lp.Lesson, cl.StartTime as 'LessonDate',
		std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
		sll.Present, sll.StarScore, sll.Note
from ClassCourse cls
join ClassLesson cl on cls.Id = cl.ClassId 
join ClassStudent cs on cls.Id = cs.ClassId 
join Student std on cs.StudentId = std.Id
join LessonPlan lp on cl.LessonId = lp.Id 
left join TeacherLessonLog tll on cl.Id = tll.ClassLessonId 
left join StudentLessonLogData sll on tll.Id = sll.LogId
where cls.[Name] not like '%test%' and cls.[Name] not like 'SC%'
order by cls.[Name], cl.StartTime