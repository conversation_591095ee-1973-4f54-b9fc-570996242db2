import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { ClassReportRoutes } from './class/ClassReportRoutes';
import { ReportActions } from './ReportConstants';

const stylesReport = {};

class ReportComp extends Component {
  static displayName = ReportComp.name;

  constructor(...args) {
    super(...args);
    this.state = {
      content: null
    };
    switch (this.props.action) {
      case ReportActions.Class:
        this.state.content = <ClassReportRoutes />;
        break;
      case undefined:
      default:
    }
  }

  render() {
    return this.state.content;
  }
}

ReportComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const Reports = withStyles(stylesReport)(ReportComp);
