USE [GeeODb]
GO

INSERT INTO [dbo].[UserInfo]
           ([Id]
           ,[UserId]
           ,[TitleName]
           ,[EnglishName]
           ,[CreatedDate]
           ,[CreatedBy])
SELECT LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
	  ,usr.Id
	  ,'Ms.'
	  ,dbo.fnConvertTitleCase(SUBSTRING(usr.Email, 0, CHARINDEX('@', usr.Email, 0))) AS EnglishName
	  ,GETDATE()
	  ,'sysadmin'
FROM AspNetUsers usr