USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplateWatchers]    Script Date: 3/26/2025 6:08:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplateWatchers](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[FullName] [nvarchar](450) NULL,
	[UserName] [nvarchar](450) NULL,
	[Email] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplateWatchers] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FormTemplateWatchers] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FormTemplateWatchers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateWatchers_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateWatchers] CHECK CONSTRAINT [FK_FormTemplateWatchers_AspNetUsers]
GO

ALTER TABLE [dbo].[FormTemplateWatchers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateWatchers_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateWatchers] CHECK CONSTRAINT [FK_FormTemplateWatchers_FormTemplate]
GO


