﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.Data.Dto;
using GeeO.Common;
using GeeO.Services;
using GeeO.Services.Interfaces;
using GeeO.Model.CampusReport;
using GeeO.Data.Dto.Student;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CampusReportController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;
        private readonly ICampusService _campusService;

        public CampusReportController(GeeODbContext context, IAcadManageService acadManageService, ICampusService campusService)
        {
            _context = context;
            _acadManageService = acadManageService;
            _campusService = campusService;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<Data.Dto.Campus>> GetCampusInfo(string campusId)
        {
            var campus = await _context.Campus.Where(cps => cps.Id == campusId)
                                              .Include(cps => cps.ClassRooms)
                                              .FirstOrDefaultAsync().ConfigureAwait(false);

            var data = from ct in _context.ClassTeacher.Where(ct => ct.ClassCourse.CampusId == campusId)
                       select ct.TeacherId;

            Data.Dto.Campus campusInfo = new()
            {
                Id = campus.Id,
                Name = campus.Address,
                Address = campus.FullAddress,
                NumberOfRooms = campus.ClassRooms.Count,
                NumberOfStaffs = await data.Distinct().CountAsync().ConfigureAwait(false),
            };

            return campusInfo;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<IActionResult> GetCampusStudentStatistic(string campusId)
        {
            try
            {
                var campusData = await _campusService.GetStudentStatisticsByCampusId(campusId).ConfigureAwait(false);
                var result = new CampusStudentRespone
                {
                    TotalStudents = campusData.TotalStudents,
                    TotalRegularStudents = campusData.TotalRegularStudents,
                    TotalDemoStudents = campusData.TotalDemoStudents,
                    TotalSuspendStudents = campusData.TotalSuspendStudents,
                    TotalTerminateStudents = campusData.TotalTerminateStudents,
                    TotalInStudents = campusData.TotalInStudents,
                    TotalOutStudents = campusData.TotalOutStudents,
                };
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<CampusClassStatistic>> GetCampusClassStatistic(string campusId)
        {
            var data = from cls in _context.ClassCourse.Where(cls => cls.CampusId == campusId && cls.Schedule != null)
                       select cls;

            CampusClassStatistic campusInfo = new()
            {
                ClassStudents = await (from cls in data
                                       where cls.Schedule.EndDate >= DateTime.Today
                                       select new CampusClassStudents
                                       {
                                           Class = cls.Name,
                                           NumberOfStudents = cls.ClassStudents
                                               .Count(cs => cs.StudentType == ClassType.Demo || cs.StudentType == ClassType.Regular)
                                       }).ToListAsync().ConfigureAwait(false),
                TotalClasses = await data.CountAsync(cls => cls.Schedule.EndDate >= DateTime.Today).ConfigureAwait(false),
                TotalFinishedClasses = await data.CountAsync(cls => cls.Schedule.EndDate < DateTime.Today).ConfigureAwait(false),
                TotalLessons = await (from cl in _context.ClassLesson
                                      join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                      join tll in _context.TeacherLessonLog on cl.Id equals tll.ClassLessonId
                                      where cc.CampusId == campusId
                                      select tll.Id
                                   ).CountAsync().ConfigureAwait(false),
                TotalDemoSessions = await (from cl in _context.ClassLesson.Where(cl => cl.ClassCourse.CampusId == campusId)
                                           from cs in _context.ClassStudent.Where(cs => cs.ClassId == cl.ClassId && cs.StudentType == ClassType.Demo)
                                           select cl
                                          ).CountAsync().ConfigureAwait(false),
                TotalCatchUpSessions = await (from cl in _context.ClassLesson.Where(cl => cl.ClassCourse.CampusId == campusId)
                                              from cs in _context.ClassStudent.Where(cs => cs.ClassId == cl.ClassId && cs.StudentType == ClassType.CatchUp)
                                              select cl
                                             ).CountAsync().ConfigureAwait(false)
            };
            return campusInfo;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<CampusClassStatistic>> GetCampusCompletedClassStatistic(string campusId)
        {
            var data = from cls in _context.ClassCourse.Where(cls => cls.CampusId == campusId && cls.Schedule != null)
                       select cls;

            CampusClassStatistic campusInfo = new()
            {
                ClassStudents = await (from cls in data
                                       where cls.Schedule.EndDate < DateTime.Today
                                       select new CampusClassStudents
                                       {
                                           Class = cls.Name,
                                           NumberOfStudents = cls.ClassStudents
                                               .Count(cs => cs.StudentType == ClassType.Demo || cs.StudentType == ClassType.Regular)
                                       }).ToListAsync().ConfigureAwait(false),
                TotalClasses = await data.CountAsync(cls => cls.Schedule.EndDate >= DateTime.Today).ConfigureAwait(false),
                TotalFinishedClasses = await data.CountAsync(cls => cls.Schedule.EndDate < DateTime.Today).ConfigureAwait(false),
                TotalLessons = await (from cl in _context.ClassLesson
                                      join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                      join tll in _context.TeacherLessonLog on cl.Id equals tll.ClassLessonId
                                      where cc.CampusId == campusId
                                      select tll.Id
                                   ).CountAsync().ConfigureAwait(false),
                TotalDemoSessions = await (from cl in _context.ClassLesson.Where(cl => cl.ClassCourse.CampusId == campusId)
                                           from cs in _context.ClassStudent.Where(cs => cs.ClassId == cl.ClassId && cs.StudentType == ClassType.Demo)
                                           select cl
                                          ).CountAsync().ConfigureAwait(false),
                TotalCatchUpSessions = await (from cl in _context.ClassLesson.Where(cl => cl.ClassCourse.CampusId == campusId)
                                              from cs in _context.ClassStudent.Where(cs => cs.ClassId == cl.ClassId && cs.StudentType == ClassType.CatchUp)
                                              select cl
                                             ).CountAsync().ConfigureAwait(false)
            };

            return campusInfo;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<CampusLevelStatistic>>> GetCampusLevelStatistic(string campusId)
        {
            var data = await (from lv in _context.StudyLevel
                              from cls in _context.ClassCourse.Where(cls => cls.LevelId == lv.Id && cls.CampusId == campusId && cls.Schedule != null)
                              select new CampusLevelClasses
                              {
                                  Level = lv.Name,
                                  Class = cls.Name
                              }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, List<CampusLevelClasses>> dict = data.GroupBy(x => x.Level).ToDictionary(x => x.Key, x => x.ToList());
            List<CampusLevelStatistic> levels = new();

            foreach (var level in dict.Keys)
            {
                CampusLevelStatistic campusInfo = new()
                {
                    Level = level,
                    NumberOfClasses = dict[level].Count
                };
                levels.Add(campusInfo);
            }

            return levels;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<LevelClassSeries>>> GetLevelClassStatistic(string campusId)
        {
            var data = await (from cls in _context.ClassCourse.Where(cls => cls.CampusId == campusId)
                              from sch in _context.Schedule.Where(sch => sch.ClassCourseId == cls.Id && sch.StartDate.Year == DateTime.Now.Year)
                              orderby sch.StartDate
                              select new LevelClassSchedule
                              {
                                  Level = cls.Level.Name,
                                  Class = cls.Name,
                                  StartDate = sch.StartDate
                              }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, List<LevelClassSchedule>> dict = data.GroupBy(x => x.Level).ToDictionary(x => x.Key, x => x.ToList());
            List<LevelClassSeries> classSeries = new();

            foreach (var level in dict.Keys)
            {
                Dictionary<int, int> monthData = new()
                {
                    [1] = 0,
                    [2] = 0,
                    [3] = 0,
                    [4] = 0,
                    [5] = 0,
                    [6] = 0,
                    [7] = 0,
                    [8] = 0,
                    [9] = 0,
                    [10] = 0,
                    [11] = 0,
                    [12] = 0,
                };

                Dictionary<int, List<LevelClassSchedule>> dict1 = dict[level].GroupBy(x => x.StartDate.Month).ToDictionary(x => x.Key, x => x.ToList());
                foreach (var month in dict1.Keys)
                {
                    monthData[month] += dict1[month].Count();
                }
                List<int> classesCount = monthData.Values.ToList();
                LevelClassSeries classData = new()
                {
                    Name = level,
                    Data = classesCount
                };
                classSeries.Add(classData);
            }

            return classSeries;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<StudentSuspendSeries>> GetStudentsStatistic(string campusId)
        {
            var suspendStudents = await (from cs in _context.ClassStudent.Where(cs => cs.ClassCourse.CampusId == campusId &&
                                                                                      cs.Student.SuspendDate != null)
                                         select new StudentSuspendData
                                         {
                                             StudentId = cs.StudentId,
                                             SuspendDate = cs.Student.SuspendDate.Value
                                         }).ToListAsync().ConfigureAwait(false);

            Dictionary<int, List<StudentSuspendData>> dict = suspendStudents.GroupBy(x => x.SuspendDate.Month).ToDictionary(x => x.Key, x => x.ToList());
            Dictionary<int, int> suspendCount = new()
            {
                [1] = 0,
                [2] = 0,
                [3] = 0,
                [4] = 0,
                [5] = 0,
                [6] = 0,
                [7] = 0,
                [8] = 0,
                [9] = 0,
                [10] = 0,
                [11] = 0,
                [12] = 0,
            };
            foreach (var month in dict.Keys)
            {
                suspendCount[month] += dict[month].Count();
            }

            var terminateStudents = await (from cs in _context.ClassStudent.Where(cs => cs.ClassCourse.CampusId == campusId &&
                                                                                        cs.Student.TerminateDate != null)
                                           select new StudentSuspendData
                                           {
                                               StudentId = cs.StudentId,
                                               SuspendDate = cs.Student.TerminateDate.Value
                                           }).ToListAsync().ConfigureAwait(false);

            dict = terminateStudents.GroupBy(x => x.SuspendDate.Month).ToDictionary(x => x.Key, x => x.ToList());
            Dictionary<int, int> terminateCount = new()
            {
                [1] = 0,
                [2] = 0,
                [3] = 0,
                [4] = 0,
                [5] = 0,
                [6] = 0,
                [7] = 0,
                [8] = 0,
                [9] = 0,
                [10] = 0,
                [11] = 0,
                [12] = 0,
            };
            foreach (var month in dict.Keys)
            {
                terminateCount[month] += dict[month].Count();
            }

            return new StudentSuspendSeries { SuspendData = suspendCount.Values.ToList(), TerminateData = terminateCount.Values.ToList() };
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<RoomTimeStatistic>>> GetRoomStatistic(string campusId)
        {
            List<DateTime> holidays = await _acadManageService.GetHolidays(string.Empty).ConfigureAwait(false);
            DateTime nextScheduleDate = holidays.Max().AddDays(7);
            DateTime thisMonday = DateUtils.GetMondayOfWeek(nextScheduleDate);
            DateTime thisSunday = DateUtils.GetSundayOfWeek(nextScheduleDate);

            var data = await (from cl in _context.ClassLesson.Where(cl => cl.ClassCourse.CampusId == campusId &&
                                                                          cl.StartTime.Value.Date >= thisMonday &&
                                                                          cl.StartTime.Value.Date <= thisSunday)
                              from sch in _context.Schedule.Where(sch => sch.ClassCourseId == cl.ClassId && sch.ClassRoomId != null)
                              orderby cl.StartTime
                              select new RoomSchedule
                              {
                                  RoomName = sch.ClassRoom != null ? sch.ClassRoom.Name : string.Empty,
                                  StartTime = cl.StartTime.Value,
                                  Duration = cl.Lesson.Level.LessonTime.Value
                              }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, List<RoomSchedule>> dict = data.GroupBy(x => x.RoomName).ToDictionary(x => x.Key, x => x.ToList());
            List<RoomTimeStatistic> roomsInfo = new();

            foreach (var roomName in dict.Keys)
            {
                Dictionary<DayOfWeek, int> timeInWeek = new()
                {
                    [DayOfWeek.Monday] = 0,
                    [DayOfWeek.Tuesday] = 0,
                    [DayOfWeek.Wednesday] = 0,
                    [DayOfWeek.Thursday] = 0,
                    [DayOfWeek.Friday] = 0,
                    [DayOfWeek.Saturday] = 0,
                    [DayOfWeek.Sunday] = 0,
                };

                Dictionary<DayOfWeek, List<RoomSchedule>> dict1 = dict[roomName].GroupBy(x => x.StartTime.DayOfWeek).ToDictionary(x => x.Key, x => x.ToList());
                foreach (var day in dict1.Keys)
                {
                    timeInWeek[day] += dict1[day].Sum(x => x.Duration);
                }
                List<int> usedTimes = timeInWeek.Values.ToList();
                RoomTimeStatistic roomTimeStatistic = new()
                {
                    RoomName = roomName,
                    TotalUsedTime = usedTimes.Sum(),
                    RoomTimes = usedTimes
                };
                roomsInfo.Add(roomTimeStatistic);
            }

            return roomsInfo;
        }

        [HttpGet("[action]/{campusId}/{selectedYear}")]
        public async Task<ActionResult<CampusPaymentStatistic>> GetCampusPaymnetStatistic(string campusId, int? selectedYear)
        {
            CampusPaymentStatistic paymentStatistic = await GetPaymentInfo(campusId, selectedYear).ConfigureAwait(false);

            return paymentStatistic;
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<StudentRemainingSessions>>> GetStudentsWithRemainingSessions(string campusId)
        {
            try
            {
                var classStudent = await (from cs in _context.ClassStudent.Where(cs => cs.ClassCourse.CampusId == campusId && cs.StudentType == ClassType.Regular)
                                          from std in _context.Student.Where(std => std.Id == cs.StudentId && std.SuspendDate == null && std.TerminateDate == null)
                                          select new StudentRemainingSessions
                                          {
                                              StudentId = std.Id,
                                              StudentName = std.StudentName,
                                              EnglishName = std.EnglishName,
                                              NumberOfSessions = (from sc in std.StudentCourses select sc.NumberOfSession).Sum(),
                                              StudiedSessions = (from sll in _context.StudentLessonLogData
                                                                 where sll.StudentInfoId == std.Id && sll.Present > -1
                                                                 join tll in _context.TeacherLessonLog on sll.LogId equals tll.Id
                                                                 select sll).Count()
                                          }).ToListAsync().ConfigureAwait(false);
                var data = classStudent.Where(x => x.RemainSessions > 0 && x.RemainSessions < 12).ToList();
                return data;
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        private async Task<CampusPaymentStatistic> GetPaymentInfo(string campusId, int? selectedYear)
        {
            var years = await (from sc in _context.StudentCourse
                               join cs in _context.Student on sc.StudentId equals cs.Id
                               join cc in _context.ClassCourse on sc.ClassId equals cc.Id
                               where cc.CampusId == campusId
                               select sc.StartDate.Value.Year)
                               .Distinct().OrderBy(year => year).ToListAsync().ConfigureAwait(false);
            var data = from sc in _context.StudentCourse
                       join cc in _context.ClassCourse on sc.ClassId equals cc.Id
                       join std in _context.Student on sc.StudentId equals std.Id
                       where cc.CampusId == campusId && sc.StartDate.Value.Year == selectedYear
                       group sc by new { std.Id, std.StudentName, std.EnglishName } into studentGroup
                       select new StudentPaymentStatistics
                       {
                           StudentId = studentGroup.Key.Id,
                           StudentName = studentGroup.Key.StudentName,
                           EnglishName = studentGroup.Key.EnglishName,
                           TotalPayment = studentGroup.Sum(x => x.Amount.Value),
                           TotalRenews = studentGroup.Where(x => x.Type == TypeEnum.Renew).Sum(x => x.Amount.Value),
                           TotalNews = studentGroup.Where(x => x.Type == TypeEnum.New).Sum(x => x.Amount.Value),
                           NumberOfSessions = studentGroup.Sum(x => x.NumberOfSession),
                           StudiedSessions = (from sll in _context.StudentLessonLogData
                                              where sll.StudentInfoId == studentGroup.Key.Id
                                              select sll).Count(),
                           TotalPaymentStudents = studentGroup.Where(x => x.Amount > 0).Select(x => x.StudentId).Count(),
                           TotalRenewStudents = studentGroup.Where(x => x.Amount > 0 && x.Type == TypeEnum.Renew).Select(x => x.StudentId).Count(),
                           TotalNewStudents = studentGroup.Where(x => x.Amount > 0 && x.Type == TypeEnum.New).Select(x => x.StudentId).Count()

                       };

            var monthPayment = await (from sc in _context.StudentCourse
                                      join cc in _context.ClassCourse on sc.ClassId equals cc.Id
                                      where cc.CampusId == campusId
                                          && sc.StartDate.HasValue && sc.StartDate.Value.Year == selectedYear
                                      group sc by sc.StartDate.Value.Month into g
                                      orderby g.Key
                                      select new CampusMonthPayment
                                      {
                                          Month = g.Key,
                                          Payment = g.Sum(sc => sc.Amount.Value),
                                          Renews = g.Where(sc => sc.Type == TypeEnum.Renew).Sum(sc => sc.Amount.Value),
                                          News = g.Where(sc => sc.Type == TypeEnum.New).Sum(sc => sc.Amount.Value),
                                      }).ToListAsync().ConfigureAwait(false);

            Dictionary<int, List<CampusMonthPayment>> dict = monthPayment.GroupBy(x => x.Month).ToDictionary(x => x.Key, x => x.ToList());
            List<CampusMonthPayment> paymentData = new();
            List<int> months = new();
            double totalPayments = 0;
            double totalRenews = 0;
            double totalNews = 0;

            foreach (var month in dict.Keys)
            {
                totalPayments = dict[month].Sum(x => x.Payment);
                totalRenews = dict[month].Sum(x => x.Renews);
                totalNews = dict[month].Sum(x => x.News);
                CampusMonthPayment campusInfo = new()
                {
                    Month = month,
                    Payment = totalPayments,
                    Renews = totalRenews,
                    News = totalNews,
                };
                paymentData.Add(campusInfo);
                months.Add(month);
            }

            CampusPaymentStatistic paymentStatistic = new()
            {
                Years = years,
                Months = months,
                MonthPayments = paymentData,
                StudentPayments = await data.ToListAsync().ConfigureAwait(false)
            };

            return paymentStatistic;
        }
    }
}
