/****** <PERSON><PERSON><PERSON> for SelectTopNRows command from SSMS  ******/
SELECT [LessonPlan].[Lesson]
      ,[LessonPlanUnit].[Time]
      ,[LessonPlanUnit].[Procedures]
      ,[LessonPlanUnit].[Description]
      ,[LessonPlanUnit].[Materials]
      ,[LessonPlanUnit].[TeacherActivities]
      ,[LessonPlanUnit].[LearningOutcome]
      ,[LessonPlanUnit].[Note]
  FROM [GeeODb].[dbo].[LessonPlan]
  INNER JOIN [GeeODb].[dbo].[LessonPlanUnit]
	ON [LessonPlan].Id = [LessonPlanUnit].[LessonPlanId]
  where [LessonPlan].LevelId='86c5b8d3-f14c-4cea-9bea-dad0e8984c0d'
  order by CreatedDate, SortOrder