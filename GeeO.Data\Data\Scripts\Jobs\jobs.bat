@echo off
echo "GeeO Acad scheduled jobs - Starting..."

SET JobsPath=C:\GeeOBin\SQLJobs

echo "JobsPath: %JobsPath%"


sqlcmd -U acadazure -d GeeODb -S geeodbsrv.database.windows.net -P "Acad@Gee0@2022!" -i "%JobsPath%\Jobs\1.StudentBirthdayReminder.sql" -o "%JobsPath%\Logs\1.StudentBirthdayReminder.log"
sqlcmd -U acadazure -d GeeODb -S geeodbsrv.database.windows.net -P "Acad@Gee0@2022!" -i "%JobsPath%\Jobs\2.PaymentReminder.sql" -o "%JobsPath%\Logs\2.PaymentReminder.log"
sqlcmd -U acadazure -d GeeODb -S geeodbsrv.database.windows.net -P "Acad@Gee0@2022!" -i "%JobsPath%\Jobs\3.TeacherLessonReminder.sql" -o "%JobsPath%\Logs\3.TeacherLessonReminder.log"
sqlcmd -U acadazure -d GeeODb -S geeodbsrv.database.windows.net -P "Acad@Gee0@2022!" -i "%JobsPath%\Jobs\4.StudentAbsenceReminder.sql" -o "%JobsPath%\Logs\4.StudentAbsenceReminder.sql.log"
sqlcmd -U acadazure -d GeeODb -S geeodbsrv.database.windows.net -P "Acad@Gee0@2022!" -i "%JobsPath%\Jobs\5.CheckStudentSuspend.sql" -o "%JobsPath%\Logs\5.CheckStudentSuspend.log"
