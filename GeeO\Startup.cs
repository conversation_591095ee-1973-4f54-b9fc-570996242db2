using AutoMapper;
using Geeo.Services.Services;
using Geeo.Data.Models.MappingProfile;
using GeeO.Api.Hubs;
using GeeO.Common;
using GeeO.Common.Common.Config;
using GeeO.Config;
using GeeO.Data;
using GeeO.Model;
using GeeO.Data.Models.MappingProfile;
using GeeO.Data.Models.MappingPtofile;
using GeeO.Models;
using GeeO.Services;
using GeeO.Services.Interfaces;
using GeeO.Services.Services;
using IdentityServer4.Models;
using IdentityServer4.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Serialization;
using NSwag;
using NSwag.Generation.Processors.Security;
using RazorHtmlEmails.RazorClassLib.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            log4net.Config.XmlConfigurator.Configure();
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            #region Swagger Configuration
            services.AddOpenApiDocument(configure =>
            {
                configure.Title = "Admin API";
                configure.Version = "v1";
                configure.AddSecurity("JWT", new OpenApiSecurityScheme
                {
                    Type = OpenApiSecuritySchemeType.ApiKey,
                    Name = "Authorization",
                    In = OpenApiSecurityApiKeyLocation.Header,
                    Description = "Type into the textbox: Bearer {your JWT token}."
                });

                configure.OperationProcessors.Add(new AspNetCoreOperationSecurityScopeProcessor("JWT"));
            });
            #endregion Swagger Configuration

            services.AddDbContext<GeeODbContext>(options =>
                options.UseSqlServer(
                    Configuration.GetConnectionString("GeeODbConnection"), b => b.MigrationsAssembly("GeeO")));

            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(
                    Configuration.GetConnectionString("GeeODbConnection")));

            services.AddDbContext<SSISDbContext>(options =>
                options.UseSqlServer(
                    Configuration.GetConnectionString("SSISConnection")));

            services.AddDefaultIdentity<ApplicationUser>(options =>
            {
                options.ClaimsIdentity.UserIdClaimType = "UserID";
            })
            .AddDefaultUI()
                .AddRoles<IdentityRole>()
                .AddEntityFrameworkStores<ApplicationDbContext>();

            services.AddIdentityServer(options => options.IssuerUri = Configuration["IssuerUri"])
                    .AddApiAuthorization<ApplicationUser, ApplicationDbContext>(options =>
                    {
                        options.IdentityResources["openid"].UserClaims.Add("role");
                        options.ApiResources.Single().UserClaims.Add("role");

                        options.Clients.Single().AccessTokenLifetime = (int)TimeSpan.FromDays(1).TotalSeconds;
                        options.Clients.Single().AllowOfflineAccess = true;
                        options.Clients.Single().RefreshTokenUsage = TokenUsage.OneTimeOnly;
                        options.Clients.Single().AllowedScopes.Add("offline_access");
                    });

            services.AddDistributedMemoryCache();

            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromDays(1);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            services.ConfigureApplicationCookie(options =>
            {
                options.Cookie.HttpOnly = true;
                options.ExpireTimeSpan = TimeSpan.FromDays(1);

                options.LoginPath = "/Identity/Account/Login";
                options.AccessDeniedPath = "/Identity/Account/AccessDenied";
                options.SlidingExpiration = true;
                options.LogoutPath = "/Identity/Account/Logout";
            });

            services.AddControllersWithViews()
                .AddNewtonsoftJson();
            services.AddRazorPages();
            services.AddSignalR().AddJsonProtocol();

            //services.AddCors(options =>
            //{
            //    options.AddPolicy("ClientPermission", policy =>
            //    {
            //        policy.SetIsOriginAllowed((host) => true)
            //            .WithOrigins("http://localhost:3000", "http://localhost:8081", "https://dev-tpm-geeo.fm.edu.vn", "https://tms.gee-o.edu.vn")
            //            .SetIsOriginAllowedToAllowWildcardSubdomains()
            //            .AllowAnyHeader()
            //            .AllowAnyMethod()
            //            .AllowCredentials();
            //    });
            //});

            services.AddCors(options =>
            {
                options.AddPolicy("ClientPermission", policy =>
                {
                    policy
                        .WithOrigins(
                            "http://localhost:3000",
                            "http://localhost:8081",
                            "https://dev-tpm-geeo.fm.edu.vn",
                            "https://tms.gee-o.edu.vn"
                        )
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
            });



            services
                .AddAuthentication(o =>
                {
                    o.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                })
                .AddCookie()
                // .AddGoogle(options =>
                // {
                //     IConfigurationSection googleAuthNSection =
                //         Configuration.GetSection("Authentication:Google");

                //     options.ClientId = googleAuthNSection["ClientId"];
                //     options.ClientSecret = googleAuthNSection["ClientSecret"];

                //     // options.ClaimActions.MapJsonKey("urn:google:picture", "picture", "url");
                //     // options.ClaimActions.MapJsonKey("urn:google:locale", "locale", "string");
                //     options.SaveTokens = true;
                //     // options.CallbackPath = "/mobileauth";

                //     // options.Events.OnRedirectToAuthorizationEndpoint = ctx =>
                //     // {
                //     //     ctx.Response.Redirect(ctx.RedirectUri + "&hd=" + System.Net.WebUtility.UrlEncode(Configuration["Authentication:Google:OrgDomain"]));

                //     //     return Task.CompletedTask;
                //     // };
                //     options.Events.OnCreatingTicket = ctx =>
                //     {
                //         List<AuthenticationToken> tokens = ctx.Properties.GetTokens().ToList();

                //         tokens.Add(new AuthenticationToken()
                //         {
                //             Name = "TicketCreated",
                //             Value = DateTime.UtcNow.ToString(new CultureInfo("en-us"))
                //         });

                //         ctx.Properties.StoreTokens(tokens);

                //         return Task.CompletedTask;
                //     };
                //     //options.Events = new OAuthEvents
                //     //{
                //     //    OnRedirectToAuthorizationEndpoint = context =>
                //     //    {
                //     //        context.Response.Redirect(context.RedirectUri + "&hd=" + System.Net.WebUtility.UrlEncode("gee-o.edu.vn"));

                //     //        return Task.CompletedTask;
                //     //    },
                //     //    OnCreatingTicket = context =>
                //     //    {
                //     //        string domain = context.User.GetString("domain");
                //     //        if (domain != "gee-o.edu.vn")
                //     //            throw new GoogleAuthenticationException("You must sign in with a gee-o.edu.vn email address");

                //     //        return Task.CompletedTask;
                //     //    }
                //     //};
                // })
                .AddMicrosoftAccount(ms =>
                {
                    IConfigurationSection MicrosoftAuthentication =
                        Configuration.GetSection("Authentication:Microsoft");

                    ms.ClientId = MicrosoftAuthentication["ClientId"];
                    ms.ClientSecret = MicrosoftAuthentication["ClientSecret"];
                    ms.CallbackPath = "/mobileauth";
                    ms.SaveTokens = true;
                })
                .AddIdentityServerJwt();

            services.AddAuthorization(
                option => option.AddPolicy("GeeoApiScope", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireClaim("scope", "geeo_api");
                })
                   );
            services.AddAuthentication("Bearer")
                .AddJwtBearer("Bearer", options =>
                {
                    options.Authority = Configuration["IdentityServer:Authority"];
                    options.RequireHttpsMetadata = false;
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateAudience = false
                    };
                });

            services.AddMvc()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                });

            services.AddHttpContextAccessor();
            services.AddHttpClient();
            //services.Configure<IdentityOptions>(options =>
            //{
            //    // Password settings.
            //    options.Password.RequireDigit = true;
            //    options.Password.RequireLowercase = true;
            //    options.Password.RequireNonAlphanumeric = true;
            //    options.Password.RequireUppercase = true;
            //    options.Password.RequiredLength = 6;
            //    options.Password.RequiredUniqueChars = 1;

            //    // Lockout settings.
            //    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
            //    options.Lockout.MaxFailedAccessAttempts = 5;
            //    options.Lockout.AllowedForNewUsers = true;

            //    // User settings.
            //    options.User.AllowedUserNameCharacters =
            //    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
            //    options.User.RequireUniqueEmail = false;
            //});

            var provider = new FileExtensionContentTypeProvider();
            //provider.Mappings.Add(".dnct", "application/dotnetcoretutorials");
            services.AddSingleton<IMimeMappingService>(new MimeMappingService(provider));

            services.AddAutoMapper(typeof(AppMappingProfile).Assembly, typeof(MappingProfile).Assembly);
            services.AddAutoMapper(typeof(WorkLogMap));
            services.AddAutoMapper(typeof(WorkingTimeMap));
            services.AddAutoMapper(typeof(SingleLessonMap));
            services.AddAutoMapper(typeof(SingleLessonStudentMap));
            services.AddAutoMapper(typeof(SingleLessonTeacherMap));
            services.AddAutoMapper(typeof(StudentCourseMap));
            services.AddAutoMapper(typeof(TemplateMap));

            services.AddAutoMapper(typeof(FieldTemplateMap));
            services.AddAutoMapper(typeof(FormTemplateMap));
            services.AddAutoMapper(typeof(UserRequestGroupMap));
            services.AddAutoMapper(typeof(RequestFormMap));

            services.AddAutoMapper(typeof(LessonPlanMap));

            // Add Role claims to the User object
            // See: https://github.com/aspnet/Identity/issues/1813#issuecomment-420066501
            services.AddScoped<IUserClaimsPrincipalFactory<ApplicationUser>, UserClaimsPrincipalFactory<ApplicationUser, IdentityRole>>();

            services.AddScoped<IRazorViewToStringRenderer, RazorViewToStringRenderer>();
            services.AddScoped<ILessonPlanService, LessonPlanService>();
            services.AddScoped<IUploadService, UploadService>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IHtmlTemplateService, HtmlTemplateService>();
            services.AddScoped<IPdfService, PdfService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<ISSISService, SSISService>();
            services.AddScoped<ISmsApiService, SmsApiService>();
            services.AddScoped<IAcadManageService, AcadManageService>();
            services.AddScoped<IStudentPaymentService, StudentPaymentService>();
            services.AddScoped<INewsFeedService, NewsFeedService>();
            services.AddScoped<IAzureAdService, AzureAdService>();
            services.AddScoped<IStudentService, StudentService>();
            services.AddScoped<IScheduleService, ScheduleService>();
            services.AddScoped<IParentService, ParentService>();
            services.AddScoped<ITemplateService, TemplateService>();
            services.AddScoped<IClassCourseService, ClassCourseService>();
            services.AddScoped<IExcelService, ExcelService>();
            services.AddScoped<IClassLessonService, ClassLessonService>();
            services.AddScoped<IWorkLogService, WorkLogService>();
            services.AddScoped<IWorkingTimeService, WorkingTimeService>();
            services.AddScoped<IProfileService, ProfileService>();
            services.AddScoped<ICampusService, CampusService>();
            services.AddScoped<ISingleLessonService, SingleLessonService>();
            services.AddScoped<ISingleLessonStudentService, SingleLessonStudentService>();
            services.AddScoped<ISingleLessonTeacherService, SingleLessonTeacherService>();

            services.AddScoped<IActivityLogService, ActivityLogService>();

            services.AddScoped<IFormTemplateService, FormTemplateService>();
            services.AddScoped<IFieldTemplateService, FieldTemplateService>();
            services.AddScoped<IUserRequestGroupService, UserRequestGroupService>();
            services.AddScoped<IRequestFormService, RequestFormService>();
            services.AddScoped<IHttpRequestService, HttpRequestService>();

            services.AddScoped<ILessonPlanService, LessonPlanService>();

            services.AddScoped<IAccountService, AccountService>();
            services.AddScoped<IRequestFormProcessTriggerService, RequestFormProcessTriggerService>();


            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = **********;
                options.AutomaticAuthentication = false;
            });

            services.Configure<FormOptions>(x =>
            {
                x.ValueLengthLimit = **********;
                x.MultipartBodyLengthLimit = **********; // if don't set default value is: 128 MB
                x.MultipartHeadersLengthLimit = **********;
            });

            // In production, the React files will be served from this directory
            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = "ClientApp/build";
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceProvider services) /*ILoggerFactory loggerFactory,*/
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseDatabaseErrorPage();

                app.UseOpenApi();
                app.UseSwaggerUi();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseSpaStaticFiles();

            app.UseCors("ClientPermission");
            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();
            app.UseIdentityServer();

            app.UseSession();

            //app.UseMvc(routes =>
            //{
            //    routes.MapRoute(
            //        name: "default",
            //        template: "{controller}/{action=Index}/{id?}");
            //});
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapDefaultControllerRoute();
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller}/{action=Index}/{id?}");
                endpoints.MapRazorPages();
                endpoints.MapHub<ChatHub>("/hubs/chat");
                endpoints.MapHub<LiveClassHub>("/hubs/liveclass");
            });

            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = "ClientApp/build";

                if (env.IsDevelopment())
                {
                    spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
                    // spa.UseReactDevelopmentServer(npmScript: "start");
                }
            });

            //loggerFactory.AddFile(Configuration["FileLogging:Path"], Configuration["Logging:LogLevel:Default"]);

            //CreateUserRoles(services).Wait();
        }

        private async Task CreateUserRoles(IServiceProvider serviceProvider)
        {
            var RoleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var UserManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();

            IdentityResult roleResult;
            //Adding Admin Role
            var roleCheckAdmin = await RoleManager.RoleExistsAsync(EnumsHelper.GetDescription(AcadRoles.Admin)).ConfigureAwait(false);
            if (!roleCheckAdmin)
            {
                //create the roles and seed them to the database
                roleResult = await RoleManager.CreateAsync(new IdentityRole(EnumsHelper.GetDescription(AcadRoles.Admin))).ConfigureAwait(false);
            }
            //Adding Teacher Role
            var roleCheckTeacher = await RoleManager.RoleExistsAsync(EnumsHelper.GetDescription(AcadRoles.Teacher)).ConfigureAwait(false);
            if (!roleCheckTeacher)
            {
                //create the roles and seed them to the database
                roleResult = await RoleManager.CreateAsync(new IdentityRole(EnumsHelper.GetDescription(AcadRoles.Teacher))).ConfigureAwait(false);
            }

            //Assign Admin role to the main User here we have given our newly registered 
            //login id for Admin management
            //ApplicationUser user = await UserManager.FindByEmailAsync("<EMAIL>");
            //var User = new ApplicationUser();
            //await UserManager.AddToRoleAsync(user, EnumsHelper.GetDescription(RoleDefaultEnum.Admin));
        }
    }
}
