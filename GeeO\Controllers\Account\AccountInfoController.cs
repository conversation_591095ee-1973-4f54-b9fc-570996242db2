using GeeO.Data.Dto.Account;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using Microsoft.Extensions.Configuration;
using GeeO.Data.Dto.AccountInfo;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using GeeO.Models;
using GeeO.Model.UserInfo;
using AutoMapper;
using GeeO.Services;
using Microsoft.AspNetCore.Authorization;
using System.Linq;

namespace GeeO.Controllers.Account
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountInfoController : GeoOControllerBase
    {
        private readonly IAccountService _accountService;
        private readonly IConfiguration _config;
        private readonly IFileService _fileService;
        private readonly IMapper _mapper;

        public AccountInfoController(UserManager<ApplicationUser> userManager, IAccountService accountService, IConfiguration config, IMapper mapper, IFileService fileService) : base(userManager)
        {
            _accountService = accountService;
            _config = config;
            _mapper = mapper;
            _fileService = fileService;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            try
            {
                AccountInfoDto result = await _accountService.GetById(id);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("list")]
        public async Task<IActionResult> GetAccountList()
        {
            try
            {
                List<AccountListDto> accounts = await _accountService.GetAccounts();
                return Ok(new { Status = StatusCodes.Status200OK, Data = accounts });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromForm] UserInfoUpdateRequest request)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                bool isAdmin = HttpContext.User.Claims.Any(c => c.Type == ClaimTypes.Role && c.Value == "admin");
                // If the user is not an admin, they can only edit their own data
                if (!isAdmin)
                {
                    if (userId != id)
                    {
                        // If it's not their own data, deny access by returning Unauthorized
                        return Unauthorized(new { Status = StatusCodes.Status401Unauthorized, ErrorMessage = "You are not authorized to update this account." });
                    }
                }
                var dto = _mapper.Map<AccountInfoUpdateDto>(request);
                var result = await _accountService.Update(id, dto);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("file/{userId}/{fileName}")]
        public async Task<IActionResult> GetUserFile(string userId, string fileName)
        {
            return await _fileService.GetUserFileAsync(userId, fileName);
        }
    }
}
