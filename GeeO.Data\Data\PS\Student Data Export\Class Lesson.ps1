﻿###
### GEE-O ENGLISH script for getting student attendance
###

# read settings 
$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

# load common utilities
.$PSScriptRoot/Common.ps1


# declare sql in a variable and pass it to -Query switch
$sqlServer = $settings.database.server
$database = $settings.database.database
$outFolderRoot = $settings.outFolder


function Get-StudentAttendance
{
    $time = Get-Date -Format "yyyyMMdd-HHmmss"
    Write-RunLog "====================== Starting ======================"

	$outFolder = $outFolderRoot
	
	if (!(Test-Path $outFolder))
	{
		New-Item -ItemType Directory -Path $outFolder > $null
		Write-Host "$outFolder folder created successfully"
	}

	$ExcelObject = New-Object -ComObject Excel.Application  
	$ExcelObject.Visible = $false 
	$ExcelObject.DisplayAlerts =$false

    $sqlClass="
    select cls.Id as 'ClassId', cls.[Name] as 'ClassName'
	from ClassCourse cls
	where cls.[Name] not like '%test%' and cls.[Name] not like 'SC%' and cls.[Name] in ('LTTM0118', 'LTTM0218', 'LTTM0418', 'LTTM0518')
    "
	$classList = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlClass

    foreach ($class in $classList)
    {
        $outFile = Join-Path $outFolder "$($class.ClassName).xlsx" # name of file to export
        Write-Host $outFile
		Write-Output "Class: $($class.ClassName)"
		
		# Create Excel file  
		$ActiveWorkbook = $ExcelObject.Workbooks.Add()  
		$ActiveWorksheet = $ActiveWorkbook.Worksheets.Item(1)
		$ActiveWorksheet.Columns(4).NumberFormat = "dd/MM/yyyy HH:mm"
		$ActiveWorksheet.Columns(7).NumberFormat = "dd/MM/yyyy"

		#$sheetName = $lesson.LessonDate.ToString("dd-MM-yyyy")
		#$ActiveWorksheet.Name = $sheetName

		$ActiveWorksheet.Cells.Item(1,1) = "ClassLessonId"
		$ActiveWorksheet.Cells.Item(1,2) = "StudentId"
		$ActiveWorksheet.Cells.Item(1,3) = "Lesson"
		$ActiveWorksheet.Cells.Item(1,4) = "DateTime"
		$ActiveWorksheet.Cells.Item(1,5) = "StudentName"
		$ActiveWorksheet.Cells.Item(1,6) = "EnglishName"
		$ActiveWorksheet.Cells.Item(1,7) = "Birthday"
		$ActiveWorksheet.Cells.Item(1,8) = "Presence"
		$ActiveWorksheet.Cells.Item(1,9) = "StarScore"
		#$ActiveWorksheet.Cells.Item(1,12) = "LogDateTime"
			
		$sqlLesson="
			select cl.Id, lp.Lesson, cl.StartTime as 'LessonDate',
				   '' as 'StudentId', '' as 'StudentName', '' as 'EnglishName', '' as 'Birthday',
				   '' as 'Present', 0 as 'StarScore'
			from ClassLesson cl 
			join LessonPlan lp on cl.LessonId = lp.Id
			where cl.ClassId = '$($class.ClassId)' and cl.StartTime < CONVERT(date, GETDATE())
			order by cl.StartTime
		"

        $classLessons = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlLesson
            
		$rowIndex = 2

        foreach ($lesson in $classLessons)
        {
			$ActiveWorksheet.Cells.Item($rowIndex,1) = "$($lesson.Id)"
			$ActiveWorksheet.Cells.Item($rowIndex,2) = "$($lesson.StudentId)"
			$ActiveWorksheet.Cells.Item($rowIndex,3) = "$($lesson.Lesson)"
			$ActiveWorksheet.Cells.Item($rowIndex,4) = "$($lesson.LessonDate)"
			$ActiveWorksheet.Cells.Item($rowIndex,5) = "$($lesson.StudentName)"
			$ActiveWorksheet.Cells.Item($rowIndex,6) = "$($lesson.EnglishName)"
			$ActiveWorksheet.Cells.Item($rowIndex,7) = $lesson.Birthday
			$ActiveWorksheet.Cells.Item($rowIndex,8) = "$($lesson.Present)"
			$ActiveWorksheet.Cells.Item($rowIndex,9) = "$($lesson.StarScore)"
			#$ActiveWorksheet.Cells.Item($rowIndex,12) = "$($lesson.LogDateTime)"
			
			$rowIndex++
        }
		#$ActiveWorksheet.UsedRange.EntireColumn.AutoFit()
		$ActiveWorksheet.UsedRange.EntireColumn.AutoFilter()
		$ActiveWorksheet.Columns.Item("D:G").EntireColumn.AutoFit()
		$ActiveWorkbook.SaveAs($outFile)
    }

	$ExcelObject.Quit()
    Write-RunLog "====================== Ending ======================"
}

Write-Host "PowerShell version $($PSVersionTable.PSVersion)"

Get-StudentAttendance
