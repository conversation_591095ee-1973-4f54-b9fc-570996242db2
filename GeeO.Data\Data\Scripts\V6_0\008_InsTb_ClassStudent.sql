INSERT INTO [dbo].[ClassStudent]
           ([Id]
           ,[ClassId]
		   ,[StudentId]
		   ,[StudentType]
		   ,[SortOrder])
SELECT
           LOWER(CONVERT(nvarchar(450), NEWID()))
           ,dt.ClassId
		   ,dt.StudentInfoId
		   ,4
		   ,100
FROM (
select distinct sll.StudentInfoId, cl.ClassId, cs.ClassId as ClassStudentId
from StudentLessonLogData sll
join TeacherLessonLog tll on tll.Id = sll.LogId
join ClassLesson cl on cl.Id = tll.ClassLessonId
left join ClassStudent cs on cs.ClassId = cl.ClassId and cs.StudentId = sll.StudentInfoId
where cs.ClassId is null
) dt