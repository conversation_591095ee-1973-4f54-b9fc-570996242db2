import { createMuiTheme } from '@material-ui/core/styles';

export const defaultTheme = createMuiTheme({
  palette: {
    type: 'light',
    primary: {
      main: '#b22c2c',
      light: '#bf3030',
      dark: 'rgb(124, 30, 30)',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#50d282',
      light: 'rgb(115, 219, 155)',
      dark: 'rgb(56, 147, 91)',
      contrastText: '#ffffff'
    },
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.35)',
      hint: 'rgba(0, 0, 0, 0.38)'
    }
  },
  typography: { useNextVariants: true }
});
