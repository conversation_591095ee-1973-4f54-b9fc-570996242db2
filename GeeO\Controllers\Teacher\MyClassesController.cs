﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using GeeO.GridVo;
using System.Security.Claims;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class MyClassesController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public MyClassesController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/MyClasses/GetNotReportedLessons/5
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<MyClassLessonVo>>> GetNotReportedLessons(string userId)
        {
            var result = from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == userId)
                         from cls in _context.ClassCourse.Where(cls => ct.ClassId == cls.Id)
                         from cl in _context.ClassLesson.Where(cl => ct.ClassId == cl.ClassId
                                                                    && cl.EndTime.Value.Date > DateTime.Today.AddDays(-14)
                                                                    && cl.EndTime.Value.Date < DateTime.Today)
                         from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                         where !_context.TeacherLessonLog.Any(tll => tll.ClassLessonId == cl.Id)
                         orderby cl.StartTime descending
                         select new MyClassLessonVo
                         {
                             Id = cl.Id,
                             Class = cls.Name,
                             BeginDate = cl.StartTime.Value,
                             StartTime = cl.StartTime.Value,
                             EndTime = cl.EndTime.Value,
                             StartTimeLocal = cl.StartTimeLocal,
                             EndTimeLocal = cl.EndTimeLocal,
                             Lesson = lp.Lesson,
                             Subject = lp.Subject,
                             Content = lp.Content,
                             Tb = lp.Tb
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/MyClasses/GetMyClassLesson/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<MyClassLessonVo>> GetMyClassLesson(string classLessonId)
        {
            var data = _context.ClassLesson
                    .Where(c => c.Id == classLessonId)
                    .Include(c => c.ClassCourse)
                        .ThenInclude(cl => cl.ClassTeachers)
                            .ThenInclude(ct => ct.Teacher)
                    .Include(c => c.ClassCourse)
                        .ThenInclude(cl => cl.Level)
                    .Include(c => c.Lesson)
                    .AsNoTracking();

            var result = from c in data
                         select new MyClassLessonVo
                         {
                             Id = c.Id,
                             LessonPlan = c.Lesson,
                             ClassTeachers = c.ClassCourse.ClassTeachers.Select(t => t.Teacher).ToList(),
                             Level = c.ClassCourse.Level.Name,
                             Class = c.ClassCourse.Name,
                             Schedule = c.ClassCourse.Schedule != null ? c.ClassCourse.Schedule.ScheduleFormat : "",
                             StartTimeLocal = c.ClassCourse.Schedule != null ? c.ClassCourse.Schedule.StartTimeLocal : "",
                             EndTimeLocal = c.ClassCourse.Schedule != null ? c.ClassCourse.Schedule.EndTimeLocal : "",
                             Lesson = c.Lesson.Lesson,
                             Subject = c.Lesson.Subject,
                             Content = c.Lesson.Content,
                             Tb = c.Lesson.Tb,
                         };
            return await result.FirstAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{userId}/{finishedClasses}")]
        public async Task<ActionResult<IEnumerable<MyClassGrid>>> GetMyClasses(string userId, int finishedClasses)
        {
            try
            {
                bool isFinishedClasses = finishedClasses == 1;
                DateTime currentDate = DateTime.Now.Date;
                var result = from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == userId)
                             from cc in _context.ClassCourse.Where(cc => ct.ClassId == cc.Id)
                             from sl in _context.StudyLevel.Where(sl => cc.LevelId == sl.Id)
                             from sch in _context.Schedule.Where(sch => cc.Id == sch.ClassCourseId &&
                                                                        (isFinishedClasses ? sch.EndDate.Date < currentDate : sch.EndDate.Date >= currentDate))
                             orderby sch.StartDate
                             select new MyClassGrid
                             {
                                 Id = cc.Id,
                                 Class = cc.Name,
                                 Level = sl.Name,
                                 StartDate = sch.StartDate,
                                 EndDate = sch.EndDate
                             };
                return await result.ToListAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        // GET: api/MyClasses/GetMyUpComingClass/5
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<MyClassLessonVo>>> GetMyUpComingClass(string userId)
        {
            DateTime prev7Days = DateTime.Now.Date.AddDays(-7);
            var result = from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == userId)
                         from cc in _context.ClassCourse.Where(cc => ct.ClassId == cc.Id)
                         from sl in _context.StudyLevel.Where(sl => cc.LevelId == sl.Id)
                         from cl in _context.ClassLesson.Where(cl => cc.Id == cl.ClassId && cl.StartTime != null &&
                                                                     cl.StartTime.Value.Date >= prev7Days &&
                                                                     cl.StartTime.Value.Date <= DateTime.Now.Date)
                         from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                         from sch in _context.Schedule.Where(sch => cc.Id == sch.ClassCourseId)
                         orderby cl.StartTime descending
                         select new MyClassLessonVo
                         {
                             Id = cc.Id + cl.Id,
                             SubId = cl.Id,
                             ClassLessonId = cl.Id,
                             Level = sl.Name,
                             Class = cc.Name,
                             ClassId = cc.Id,
                             Schedule = sch.ScheduleFormat,
                             BeginDate = cl.StartTime.Value,
                             StartTime = cl.StartTime.Value,
                             EndTime = cl.EndTime.Value,
                             StartTimeLocal = cl.StartTimeLocal,
                             EndTimeLocal = cl.EndTimeLocal,
                             LessonPlan = lp,
                             Lesson = lp.Lesson,
                             Subject = lp.Subject,
                             Content = lp.Content,
                             Tb = lp.Tb
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/MyClasses/GetMyLesson/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<MyLessonUnitVo>>> GetMyLesson(string classLessonId)
        {
            var data = _context.ClassLesson
                    .Where(c => c.Id == classLessonId)
                    .Include(c => c.ClassCourse)
                        .ThenInclude(c => c.Level)
                    .Include(c => c.Lesson)
                        .ThenInclude(c => c.LessonPlanUnits)
                    .AsNoTracking();

            var result = from c in data
                         from u in c.Lesson.LessonPlanUnits.Where(u => !u.IsDeleted)
                         join tl in _context.TeacherLessonUnit.Where(c => c.ClassLessonId == classLessonId).Include(l => l.Material) on u.Id equals tl.UnitId into t
                         from teacherLesson in t.DefaultIfEmpty()
                         where u.LessonPlanId == c.LessonId
                         orderby u.SortOrder
                         select new MyLessonUnitVo
                         {
                             Id = c.Id + u.Id,
                             ClassLessonId = c.Id,
                             UnitId = u.Id,
                             Time = Convert.ToString(u.Time),
                             Procedures = u.Procedures,
                             Description = u.Description,
                             Materials = u.Materials,
                             TeacherActivities = u.TeacherActivities,
                             LearningOutcome = u.LearningOutcome,
                             Note = teacherLesson != null && teacherLesson.Note != null ? teacherLesson.Note : u.Note,
                             Material = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material : u.Material
                             //MaterialId = u.Material.Id,
                             //MaterialUrl = u.Material.Url,
                             //MaterialFileName = u.Material.FileName
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

    }
}
