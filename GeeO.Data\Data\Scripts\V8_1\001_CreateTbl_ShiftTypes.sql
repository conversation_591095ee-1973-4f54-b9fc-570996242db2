USE [GeeODb]
GO
/****** Object:  Table [dbo].[ShiftTypes]    Script Date: 1/12/2024 10:34:59 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ShiftTypes](
	[Id] [nvarchar](450) NOT NULL,
	[Value] [varchar](50) NOT NULL,
	[Score] [float] NOT NULL,
	[Name] [varchar](50) NOT NULL,
	[Alias] [varchar](30) NOT NULL,
	[Levels] [varchar](255) NULL,
	[Order] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NOT NULL,
	[UpdatedBy] [nvarchar](450) NOT NULL,
 CONSTRAINT [PK__ShiftTyp__3214EC07C041A74B] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'2130b180-001b-4085-acd9-e84e266e1dae', N'alone', 1.95, N'Alone shift', N'Alone', N'''M, V, E''', 5, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'2186ccba-3149-434e-89c9-6c54fe509573', N'main_full', 2, N'Main Full shift', N'MF', N'''A, J, S, U, N''', 2, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'61f909dc-3f18-4ff2-b33d-4ffc4331c54b', N'full', 1.5, N'Full shift', N'Full', N'', 4, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'7317269f-60fc-4dc3-b28e-4222a58ea840', N'expat_half', 0.5, N'Half shift', N'Half', N'', 7, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'81a308d1-867d-457e-af32-a164813ec41a', N'ct_half', 1, N'Half shift - CT', N'Half', N'''M, V, E, A, J, S, U, N''', 6, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'81a308d1-867d-457e-af32-a164813ec41s', N'mt_half', 1, N'Half shift - MT', N'Half', N'''M, V, E, A, J, S, U, N''', 6, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'81a308d1-867d-457e-af32-a164813ec41v', N'mf_half', 0.75, N'Half shift - MF', N'Half', N'''M, V, E, A, J, S, U, N''', 6, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'8832db48-c0ea-4983-a95c-abd7c2ea7b49', N'off', 0, N'OFF', N'Off', N'', 8, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'b584a68b-9509-439a-9142-b80675428bfa', N'co_teacher', 1.5, N'Co-Teacher shift', N'CT', N'''M, V, E''', 3, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
INSERT [dbo].[ShiftTypes] ([Id], [Value], [Score], [Name], [Alias], [Levels], [Order], [CreatedAt], [UpdatedAt], [CreatedBy], [UpdatedBy]) VALUES (N'c519b1a5-a05a-448a-a0e4-ccb01493c60a', N'main_teacher', 1.5, N'Main Teacher shift', N'MT', N'''M, V, E''', 1, CAST(N'2023-12-31T00:03:40.590' AS DateTime), CAST(N'2023-12-31T00:03:40.590' AS DateTime), N'sys', N'sys')
GO
ALTER TABLE [dbo].[ShiftTypes] ADD  CONSTRAINT [DF__ShiftType__Creat__2D7CBDC4]  DEFAULT (getdate()) FOR [CreatedAt]
GO
ALTER TABLE [dbo].[ShiftTypes] ADD  CONSTRAINT [DF__ShiftType__Updat__2E70E1FD]  DEFAULT (getdate()) FOR [UpdatedAt]
GO
ALTER TABLE [dbo].[ShiftTypes] ADD  CONSTRAINT [DF__ShiftType__Creat__2F650636]  DEFAULT ('sys') FOR [CreatedBy]
GO
ALTER TABLE [dbo].[ShiftTypes] ADD  CONSTRAINT [DF__ShiftType__Updat__30592A6F]  DEFAULT ('sys') FOR [UpdatedBy]
GO
