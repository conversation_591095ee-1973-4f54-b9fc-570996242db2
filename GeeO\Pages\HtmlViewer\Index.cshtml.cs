﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazorHtmlEmails.RazorClassLib.Services;
using RazorHtmlEmails.RazorClassLib.Views.Emails.ConfirmAccount;
using RazorHtmlEmails.RazorClassLib.Views.GeeO.HtmlContent;

namespace GeeO.Pages.HtmlViewer
{
    public class IndexModel : PageModel
    {
        private readonly IRazorViewToStringRenderer _razorViewToStringRenderer;

        public IndexModel(IRazorViewToStringRenderer razorViewToStringRenderer)
        {
            _razorViewToStringRenderer = razorViewToStringRenderer;
        }

        [BindProperty(SupportsGet = true)]
        public string ViewName { get; set; }

        public string HtmlContent { get; set; }

        private readonly IDictionary<string, object[]> Templates = new Dictionary<string, object[]>()
        {
            ["LessonContent"] = new object [] {
                "/Views/GeeO/LessonContent/LessonContent.cshtml",
                new LessonContentViewModel()
            },
            ["ConfirmAccountEmail"] = new object [] {
                "/Views/Emails/ConfirmAccount/ConfirmAccountEmail.cshtml", 
                new ConfirmAccountEmailViewModel($"ConfirmAccountEmail/{Guid.NewGuid()}") 
            }
        };

        public async Task<IActionResult> OnGetAsync()
        {
            object[] templateInfo = Templates[ViewName];
            HtmlContent = await _razorViewToStringRenderer.RenderViewToStringAsync((string)templateInfo[0], templateInfo[1]).ConfigureAwait(false);
            return Page();
        }
    }
}