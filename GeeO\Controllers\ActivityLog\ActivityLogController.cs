﻿using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;
using GeeO.Model.ActivityLog;
using GeeO.Data.Dto.ActivityLog;
using System.Linq;
using GeeO.Common;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace GeeO.Controllers.ActivityLog
{
    [Route("api/[controller]")]
    [ApiController]
    public class ActivityLogController : ControllerBase
    {
        private readonly IActivityLogService _activityLogService;
        public ActivityLogController(IActivityLogService activityLogSevice)
        {
            _activityLogService = activityLogSevice;
        }

        [HttpGet("action")]
        public async Task<IActionResult> GetActions()
        {
            try
            {
                var result = await _activityLogService.GetActions();
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Stattus = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("list")]
        public async Task<IActionResult> FilterAll([FromQuery] ActivityLogRequest activityLogRequest)
        {
            try
            {
                var activityLogDto = new ActivityLogDto
                {
                    TableName = JsonConvert.DeserializeObject<List<string>>(activityLogRequest.Table),
                    UserName = JsonConvert.DeserializeObject<List<string>>(activityLogRequest.User),
                    Action = JsonConvert.DeserializeObject<List<string>>(activityLogRequest.Action),
                    Changes = activityLogRequest.Changes,
                    StartDate = activityLogRequest.Start,
                    EndDate = activityLogRequest.End,
                    PageNumber = activityLogRequest.PageNumber,
                    PageSize = activityLogRequest.PageSize
                };
                var result = await _activityLogService.FilterAll(activityLogDto);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Stattus = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}
