﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[WorkLogsSingleLessons]    Script Date: 5/13/2024 11:08:41 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WorkLogsSingleLessons](
	[Id] [nvarchar](450) NOT NULL,
	[WorkLogId] [nvarchar](450) NOT NULL,
	[SingleLessonId] [nvarchar](450) NOT NULL,
	[CreatedAt] [datetime2](7) NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](450) NOT NULL,
	[UpdatedBy] [nvarchar](450) NOT NULL,
 CONSTRAINT [PK__WorkLogs__3214EC0792C3C97E] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WorkLogsSingleLessons]  WITH CHECK ADD  CONSTRAINT [WorkLogsSingleLessons_SingleLessons] FOREIGN KEY([SingleLessonId])
REFERENCES [dbo].[SingleLessons] ([Id])
GO

ALTER TABLE [dbo].[WorkLogsSingleLessons] CHECK CONSTRAINT [WorkLogsSingleLessons_SingleLessons]
GO

ALTER TABLE [dbo].[WorkLogsSingleLessons]  WITH CHECK ADD  CONSTRAINT [WorkLogsSingleLessons_WorkLogs] FOREIGN KEY([WorkLogId])
REFERENCES [dbo].[WorkLogs] ([Id])
GO

ALTER TABLE [dbo].[WorkLogsSingleLessons] CHECK CONSTRAINT [WorkLogsSingleLessons_WorkLogs]
GO


----------------------------------------------------------------------------------------------------------------
USE [GeeODb]
GO

/****** Object:  Table [dbo].[WorkLogsTeacherLessonLogs]    Script Date: 5/13/2024 11:10:04 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WorkLogsTeacherLessonLogs](
	[Id] [nvarchar](450) NOT NULL,
	[WorkLogId] [nvarchar](450) NOT NULL,
	[TeacherLessonLogId] [nvarchar](450) NOT NULL,
	[CreatedAt] [datetime2](7) NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](450) NOT NULL,
	[UpdatedBy] [nvarchar](450) NOT NULL,
 CONSTRAINT [PK__WorkLogs__3214EC0769A78352] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WorkLogsTeacherLessonLogs]  WITH CHECK ADD  CONSTRAINT [WorkLogsTeacherLessonLogs_TeacherLessonLog] FOREIGN KEY([TeacherLessonLogId])
REFERENCES [dbo].[TeacherLessonLog] ([Id])
GO

ALTER TABLE [dbo].[WorkLogsTeacherLessonLogs] CHECK CONSTRAINT [WorkLogsTeacherLessonLogs_TeacherLessonLog]
GO

ALTER TABLE [dbo].[WorkLogsTeacherLessonLogs]  WITH CHECK ADD  CONSTRAINT [WorkLogsTeacherLessonLogs_WorkLogs] FOREIGN KEY([WorkLogId])
REFERENCES [dbo].[WorkLogs] ([Id])
GO

ALTER TABLE [dbo].[WorkLogsTeacherLessonLogs] CHECK CONSTRAINT [WorkLogsTeacherLessonLogs_WorkLogs]
GO


----------------------------------------------------------------------------------------------------------------


-- Migrate data

INSERT INTO WorkLogsTeacherLessonLogs (Id, WorkLogId, TeacherLessonLogId, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy)
SELECT 
    LOWER(CONVERT(NVARCHAR(36), NEWID())), -- Generate new lowercase UUID
    Id, 
    TeacherLessonLogId, 
    CreatedAt, 
    UpdatedAt, 
    CreatedBy, 
    UpdatedBy
FROM 
    WorkLogs;

-- delete FOREIGN KEY
ALTER TABLE WorkLogs
DROP CONSTRAINT FK_WorkLogs_TeacherLessonLogs;

ALTER TABLE WorkLogs
ALTER COLUMN TeacherLessonLogId nvarchar(450) NULL;