SELECT cc.Name AS ClassName, std.StudentName, std.EnglishName, cs.CountStudent, cst.Id AS ClassStudentId, sc.Name AS PaymentName, sc.StartDate AS PaymentStartDate
FROM 
(
SELECT [ClassId]
      ,[StudentId]
	  ,count(StudentId) AS CountStudent
  FROM [GeeODb].[dbo].[ClassStudent]
  group by ClassId, StudentId
  having count(StudentId) > 1
) cs
JOIN ClassCourse cc on cs.ClassId = cc.Id
JOIN Student std on cs.StudentId = std.Id
JOIN ClassStudent cst on cs.ClassId = cst.ClassId and cs.StudentId = cst.StudentId
LEFT OUTER JOIN StudentCourse sc on cst.Id = sc.ClassStudentId
ORDER BY ClassName, EnglishName



SELECT cs.StudentId
	  ,count(sc.Id) AS CountPayment
  FROM StudentCourse sc
  JOIN ClassStudent cs on sc.ClassStudentId = cs.Id
  group by cs.StudentId
  having count(sc.Id) > 1


--- yyyyyyyyyyyyyy
SELECT cc.Name, std.StudentName, std.EnglishName
FROM ClassStudent cs
JOIN ClassCourse cc on cs.ClassId = cc.Id
JOIN Student std on cs.StudentId = std.Id
WHERE cs.StudentId IN
(
select StudentId from
(
  SELECT [StudentId]
--	  ,count(StudentId)
  FROM [GeeODb].[dbo].[ClassStudent]
  group by StudentId
  having count(StudentId) > 1
  ) t 
where t.StudentId not in (
SELECT [StudentId]
  FROM [GeeODb].[dbo].[ClassStudent]
  group by ClassId, StudentId
  having count(StudentId) > 1
)
)
ORDER BY cs.StudentId