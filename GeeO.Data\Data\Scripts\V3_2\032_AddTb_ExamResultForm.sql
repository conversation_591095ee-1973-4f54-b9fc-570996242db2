﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[ExamResultForm]    Script Date: 4/10/2021 4:56:09 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ExamResultForm](
	[Id] [nvarchar](450) NOT NULL,
	LevelId [nvarchar](450) NULL,
	ExamType int NOT NULL DEFAULT 0, --1: 3m; 2: 6m; 3: 9m; 4: 12m
	[ExamFormJson] [nvarchar](MAX) NULL,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_ExamResultForm] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ExamResultForm]  WITH CHECK ADD  CONSTRAINT [FK_ExamResultForm_LevelId] FOREIGN KEY([LevelId])
REFERENCES [dbo].[StudyLevel] ([Id])
GO

ALTER TABLE [dbo].[ExamResultForm] CHECK CONSTRAINT [FK_ExamResultForm_LevelId]
GO


