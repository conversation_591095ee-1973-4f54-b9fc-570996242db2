USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplateProcessApprovers]    Script Date: 3/26/2025 5:35:17 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplateProcessApprovers](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateProcessId] [nvarchar](450) NULL,
	[RoleId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[GroupId] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcessApprover_AspNetRoles] FOREIGN KEY([RoleId])
REFERENCES [dbo].[AspNetRoles] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] CHECK CONSTRAINT [FK_FormTemplateProcessApprover_AspNetRoles]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcessApprover_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] CHECK CONSTRAINT [FK_FormTemplateProcessApprover_AspNetUsers]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcessApprover_FormTemplateProcess] FOREIGN KEY([FormTemplateProcessId])
REFERENCES [dbo].[FormTemplateProcess] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] CHECK CONSTRAINT [FK_FormTemplateProcessApprover_FormTemplateProcess]
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcessApprover_RequestGroups] FOREIGN KEY([GroupId])
REFERENCES [dbo].[RequestGroups] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcessApprovers] CHECK CONSTRAINT [FK_FormTemplateProcessApprover_RequestGroups]
GO


