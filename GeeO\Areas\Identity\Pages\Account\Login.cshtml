﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Login to Gee-O English";
}

<div class="row account--login-pnl">
    <div class="col-sm account--login-left">
        <img src="~/images/geeo-bgn.png" alt="Gee-O English" />
    </div>
    <div class="col-sm account--login-right">
        <section>
            <form id="account" method="post">
                <img src="~/images/logo.svg" alt="Gee-O English" class="gee-o-logo" />
                <h4 class="login-title">@ViewData["Title"]</h4>
                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group">
                    <label asp-for="Input.Email" class="ctrl-label">Email address</label>
                    <input asp-for="Input.Email" class="form-control" />
                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Input.Password" class="ctrl-label"></label>
                    <input asp-for="Input.Password" class="form-control" />
                    <span asp-validation-for="Input.Password" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <div class="checkbox">
                        <label asp-for="Input.RememberMe" class="remember-me geeo-text-secondary">
                            <input asp-for="Input.RememberMe" />
                            Do you want to remember <span class="remember-pwd geeo-text-primary">Password</span>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-secondary btn-login">Login</button>
                </div>
                <div class="form-group">
                    <p>
                        <a id="forgot-password" asp-page="./ForgotPassword" class="forgot-pwd">Forgot your password?</a>
                    </p>
                    @*<p>
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Register as a new user</a>
                    </p>*@
                </div>
            </form>
        </section>
        <section>
            <hr />
            <h6 class="another-login-title">Use another service to log in.</h6>
            @{
                if ((Model.ExternalLogins?.Count ?? 0) == 0)
                {
                    <div>
                        <p>
                            There are no external authentication services configured. See <a href="https://go.microsoft.com/fwlink/?LinkID=532715">this article</a>
                            for details on setting up this ASP.NET application to support logging in via external services.
                        </p>
                    </div>
                }
                else
                {
                    <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="form-horizontal">
                        <div>
                            <p>
                                @foreach (var provider in Model.ExternalLogins)
                                {
                                    if (provider.Name == "Google")
                                    {
                                        <button type="submit" class="btn btn-login-google d-flex" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">
                                            <div class="google-logo">
                                                <img width="20" alt="Google &quot;G&quot; Logo" src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/53/Google_%22G%22_Logo.svg/512px-Google_%22G%22_Logo.svg.png" />
                                            </div>
                                            Login with Google
                                        </button>
                                    }
                                }
                            </p>
                        </div>
                    </form>
                }
            }
        </section>
    </div>
    @*<div class="col-md-6 col-md-offset-2">
        <section>
            <h4>Use another service to log in.</h4>
            <hr />
            @{
                if ((Model.ExternalLogins?.Count ?? 0) == 0)
                {
                    <div>
                        <p>
                            There are no external authentication services configured. See <a href="https://go.microsoft.com/fwlink/?LinkID=532715">this article</a>
                            for details on setting up this ASP.NET application to support logging in via external services.
                        </p>
                    </div>
                }
                else
                {
                    <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="form-horizontal">
                        <div>
                            <p>
                                @foreach (var provider in Model.ExternalLogins)
                                {
                                    <button type="submit" class="btn btn-primary" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">@provider.DisplayName</button>
                                }
                            </p>
                        </div>
                    </form>
                }
            }
        </section>
    </div>*@
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
