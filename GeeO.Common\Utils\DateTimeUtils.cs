﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Common
{
    public static class DateTimeUtils
    {
        public static string FormatDate(DateTime? dt)
        {
            return dt.HasValue ? dt.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture) : string.Empty;
        }

        public static string FormatTime(DateTime dt)
        {
            return dt.ToString("HH:mm");
        }
    }
}
