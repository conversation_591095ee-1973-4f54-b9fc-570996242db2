USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestForms]    Script Date: 3/26/2025 5:54:32 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestForms](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](250) NULL,
	[UserId] [nvarchar](450) NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[FormStatus] [int] NULL,
	[CreatedDate] [datetime] NULL,
	[LastModified] [datetime] NULL,
	[Answers] [nvarchar](max) NULL,
	[ExpiredDate] [datetime] NULL,
	[ExpirationCycle] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
	[NextWorkFlowId] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestForms] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestForms] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestForms]  WITH CHECK ADD  CONSTRAINT [FK_RequestForms_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[RequestForms] CHECK CONSTRAINT [FK_RequestForms_AspNetUsers]
GO

ALTER TABLE [dbo].[RequestForms]  WITH CHECK ADD  CONSTRAINT [FK_RequestForms_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[RequestForms] CHECK CONSTRAINT [FK_RequestForms_FormTemplate]
GO

ALTER TABLE [dbo].[RequestForms]  WITH CHECK ADD  CONSTRAINT [FK_RequestForms_RequestFormHandleWorkFlow] FOREIGN KEY([NextWorkFlowId])
REFERENCES [dbo].[RequestFormHandleWorkFlows] ([Id])
GO

ALTER TABLE [dbo].[RequestForms] CHECK CONSTRAINT [FK_RequestForms_RequestFormHandleWorkFlow]
GO


