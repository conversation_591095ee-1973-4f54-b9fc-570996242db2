import React, { createContext, useEffect, useState } from 'react';
import authService from './../components/api-authorization/AuthorizeService';

export const AuthContext = createContext({
  authenticated: false
});

export const AuthProvider = ({ children }) => {
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const check = async () => {
      try {
        const isAuth = await authService.isAuthenticated();
        setAuthenticated(isAuth);
      } catch (error) {
        console.error(
          'AuthProvider: Error during authentication check:',
          error
        );
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    check();

    const subId = authService.subscribe(check);

    return () => authService.unsubscribe(subId);
  }, []);

  return (
    <AuthContext.Provider value={{ authenticated, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
