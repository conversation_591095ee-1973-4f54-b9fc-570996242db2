﻿USE [Gee<PERSON><PERSON>]
GO

/****** Object:  StoredProcedure [dbo].[sp_UpdateClassLesson]    Script Date: 9/19/2023 10:55:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [dbo].[sp_UpdateClassLesson]
    @LessonId NVARCHAR(450),
    @LevelId NVARCHAR(450),
    @Lesson Int
AS
BEGIN
    UPDATE ClassLesson
    SET LessonId = @LessonId
    FROM ClassLesson AS cl
    INNER JOIN ClassCourse AS cc ON cc.Id = cl.ClassId
    INNER JOIN LessonPlan AS lp ON lp.Id = cl.LessonId
    WHERE cc.LevelId = @LevelId AND lp.Lesson = @Lesson AND cl.Id NOT IN (select ClassLessonId from TeacherLessonLog)
END
GO


