﻿
ALTER TABLE StudentCourse
ADD 
    UserId NVARCHAR(450) NULL,
    ClassId NVARCHAR(450) NULL,
    CoTeacherId NVARCHAR(450) NULL,
    MainTeacherId NVARCHAR(450) NULL,
    CampusId NVARCHAR(450) NULL,
   Type int NULL;


--- Update type
WITH StudentCounts AS (
    SELECT StudentId, COUNT(*) AS CourseCount
    FROM StudentCourse
    GROUP BY StudentId
)


UPDATE StudentCourse
SET Type = CASE 
    WHEN sc.CourseCount = 1 THEN 1
    ELSE 2
END
FROM StudentCounts sc
WHERE StudentCourse.StudentId = sc.StudentId;