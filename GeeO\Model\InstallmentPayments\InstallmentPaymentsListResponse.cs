using System.ComponentModel.DataAnnotations.Schema;
using GeeO.Extensions;
using GeeO.Models;

namespace GeeO.Model.InstallmentPayments
{
    public class InstallmentPaymentsListResponse
    {
        public string Id { get; set; }
        public string StudentCourseId { get; set; }
        public int? Amount { get; set; }
        public int TransferType { get; set; }
        public string? TransferTypeDescription { get; set; }
        public string CreatedAt { get; set; }
        [NotMapped]
        public string TransferTypeStr => (TransferTypeEnum)TransferType == TransferTypeEnum.Other
            ? TransferTypeDescription
            : EnumExtensionMethods.GetDescription((TransferTypeEnum)TransferType); 
    }
}
