﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.GridVo;
using GeeO.Data.Common;
using Microsoft.Extensions.Logging;
using GeeO.Data.Extensions;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class UnitPlansController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly ILogger _logger;

        public UnitPlansController(GeeODbContext context, ILogger<UnitPlansController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/UnitPlans
        [HttpGet]
        public async Task<ActionResult<IEnumerable<LessonPlanUnit>>> GetLessonPlanUnit()
        {
            return await _context.LessonPlanUnit.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/UnitPlans/GetByLesson/5
        [HttpGet("[action]/{lessonPlanId}")]
        public async Task<ActionResult<IEnumerable<LessonPlanUnit>>> GetByLesson(string lessonPlanId)
        {
            return await _context.LessonPlanUnit
                    .Where(u => u.LessonPlanId == lessonPlanId && !u.IsDeleted)
                    .Include(l => l.Material)
                    .OrderBy(u => u.SortOrder)
                    .ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<List<LessonPlan>>> GetRelatedLessonPlans(string id)
        {
            try
            {
                var lessonPlan = await _context.LessonPlan.FindAsync(id);
                var relatedLessonPlans = await _context.LessonPlan
                        .Where(lp => lp.LevelId == lessonPlan.LevelId && lp.Lesson != lessonPlan.Lesson && !lp.IsDeleted)
                        .OrderBy(lp => lp.Lesson)
                        .ToListAsync();

                return Ok(relatedLessonPlans);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> PostMultiple([FromBody] List<LessonPlanUnit> lessonPlanUnits)
        {
            try
            {
                if (lessonPlanUnits == null || lessonPlanUnits.Count == 0)
                {
                    return BadRequest("Invalid data!");
                }
                foreach (var lessonPlanUnit in lessonPlanUnits)
                {
                    if (string.IsNullOrEmpty(lessonPlanUnit.Id))
                    {
                        var newLessonPlanUnit = new LessonPlanUnit
                        {
                            LessonPlanId = lessonPlanUnit.LessonPlanId,
                            MaterialId = lessonPlanUnit.MaterialId,
                            SortOrder = lessonPlanUnit.SortOrder,
                            Time = lessonPlanUnit.Time,
                            Procedures = lessonPlanUnit.Procedures,
                            Description = lessonPlanUnit.Description,
                            Materials = lessonPlanUnit.Materials,
                            TeacherActivities = lessonPlanUnit.TeacherActivities,
                            LearningOutcome = lessonPlanUnit.LearningOutcome,
                            Note = lessonPlanUnit.Note,
                            Category = lessonPlanUnit.Category,
                            IsDeleted = lessonPlanUnit.IsDeleted,
                        };
                        _context.LessonPlanUnit.Add(newLessonPlanUnit);
                        _context.Entry(newLessonPlanUnit).SetActivityState(ActivityActionType.Copied);
                    }
                }
                var lessonPlanUnitData = await _context.SaveChangesAsync();
                return Ok(lessonPlanUnitData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        // GET: api/UnitPlans/5
        [HttpGet("{id}")]
        public async Task<ActionResult<LessonPlanUnit>> GetLessonPlanUnit(string id)
        {
            var lessonPlanUnit = await _context.LessonPlanUnit
                    .Where(u => u.Id == id)
                    .Include(l => l.LessonPlan)
                        .ThenInclude(l => l.Level)
                    .Include(l => l.Material)
                    .FirstAsync().ConfigureAwait(false);

            if (lessonPlanUnit == null)
            {
                return NotFound();
            }

            return lessonPlanUnit;
        }

        // PUT: api/UnitPlans/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutLessonPlanUnit(string id, LessonPlanUnit lessonPlanUnit)
        {
            if (id != lessonPlanUnit.Id)
            {
                return BadRequest();
            }

            _context.Entry(lessonPlanUnit).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LessonPlanUnitExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/UnitPlans
        [HttpPost]
        public async Task<ActionResult<LessonPlanUnit>> PostLessonPlanUnit(LessonPlanUnit lessonPlanUnit)
        {
            int? maxIndex = _context.LessonPlanUnit.Where(u => u.LessonPlan.Id == lessonPlanUnit.LessonPlanId).Max(u => u.SortOrder);
            lessonPlanUnit.SortOrder = maxIndex != null && maxIndex > 0 ? maxIndex + 1 : 1;
            _context.LessonPlanUnit.Add(lessonPlanUnit);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetLessonPlanUnit", new { id = lessonPlanUnit.Id }, lessonPlanUnit);
        }

        // DELETE: api/UnitPlans/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<LessonPlanUnit>> DeleteLessonPlanUnit(string id)
        {
            var lessonPlanUnit = await _context.LessonPlanUnit.FindAsync(id);
            lessonPlanUnit.IsDeleted = true;
            _context.Entry(lessonPlanUnit).SetActivityState(ActivityActionType.Archived);

            await ReorderUnitPlan(lessonPlanUnit.LessonPlanId).ConfigureAwait(false);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return lessonPlanUnit;
        }

        [HttpPost("ids")]
        public async Task<ActionResult<List<string>>> DeleteLessonPlanUnits(List<string> lpuIds)
        {
            try
            {
                var units = await _context.LessonPlanUnit.Where(x => lpuIds.Contains(x.Id)).ToListAsync();
                units.ForEach(x =>
                {
                    x.IsDeleted = true;
                    _context.Entry(x).SetActivityState(ActivityActionType.Archived);
                });
                await _context.SaveChangesAsync().ConfigureAwait(false);

                return lpuIds;
            }
            catch (Exception ex)
            {
                _logger.LogError($"DeleteLessonPlanUnits - {ex.Message}");
                return BadRequest("An error occurred during the process.");
            }
        }

        private async Task SaveHistory(LessonPlanUnit lessonPlanUnit)
        {
            List<LessonPlanUnitHistory> lessonUnitHistories = await SaveLessonPlanUnitHistory(lessonPlanUnit).ConfigureAwait(false);

            Dictionary<string, LessonPlanUnitHistory> lessonHistoryDict =
                lessonUnitHistories.ToDictionary(p => p.HistoryUnitId);

            await SaveTeacherLessonUnitHistory(lessonPlanUnit, lessonHistoryDict).ConfigureAwait(false);
            await UpdateTeacherLessonLog(lessonPlanUnit, lessonHistoryDict).ConfigureAwait(false);
        }

        private async Task UpdateTeacherLessonLog(LessonPlanUnit lessonPlanUnit, Dictionary<string, LessonPlanUnitHistory> lessonHistoryDict)
        {
            var teacherLessonLogs = await _context.TeacherLessonLog
                                                        .Where(l => l.ClassLesson.LessonId == lessonPlanUnit.LessonPlanId && l.HistoryLog == false)
                                                        .Include(l => l.LogDatas)
                                                        .ToListAsync().ConfigureAwait(false);
            foreach (TeacherLessonLog teacherLessonLog in teacherLessonLogs)
            {
                teacherLessonLog.HistoryLog = true;
                _context.Entry(teacherLessonLog).State = EntityState.Modified;

                //var logDatas = await _context.TeacherLessonLogData.Where(l => l.LogId == teacherLessonLog.Id).ToListAsync().ConfigureAwait(false);
                foreach (TeacherLessonLogData logData in teacherLessonLog.LogDatas)
                {
                    logData.HistoryUnitId = lessonHistoryDict[logData.UnitId]?.Id;

                    // TODO: Check if this is needed
                    //logData.UnitId = null;
                }
                _context.UpdateRange(teacherLessonLog.LogDatas);
            }
            //await _context.SaveChangesAsync().ConfigureAwait(false);
        }

        private async Task SaveTeacherLessonUnitHistory(LessonPlanUnit lessonPlanUnit, Dictionary<string, LessonPlanUnitHistory> lessonHistoryDict)
        {
            var teacherLessonUnits = await _context.TeacherLessonUnit
                                                        .Where(l => l.UnitId == lessonPlanUnit.Id)
                                                        .ToListAsync().ConfigureAwait(false);

            var nowTime = DateTime.Now;
            var HistoryUnitId = lessonHistoryDict[lessonPlanUnit.Id]?.Id;
            var teacherLessonHistoryUnits = teacherLessonUnits.Select(l => (TeacherLessonUnitHistory)
                                                    DataModelUtils.CopyProperties(l, new TeacherLessonUnitHistory() { HistoryUnitId = HistoryUnitId, CreatedAt = nowTime }))
                                                .ToList();

            await _context.TeacherLessonUnitHistory.AddRangeAsync(teacherLessonHistoryUnits).ConfigureAwait(false);
            //await _context.SaveChangesAsync().ConfigureAwait(false);
        }

        private async Task<List<LessonPlanUnitHistory>> SaveLessonPlanUnitHistory(LessonPlanUnit lessonPlanUnit)
        {
            var lessonUnits = await _context.LessonPlanUnit.Where(l => l.LessonPlanId == lessonPlanUnit.LessonPlanId).ToListAsync().ConfigureAwait(false);
            //var lessonUnitHistory = lessonUnits.Select(l => new LessonPlanUnitHistory(l));
            var nowTime = DateTime.Now;
            var lessonUnitHistories = lessonUnits.Select(l => (LessonPlanUnitHistory)
                                                    DataModelUtils.CopyProperties(l, new LessonPlanUnitHistory() { CreatedAt = nowTime }))
                                                .ToList();

            await _context.LessonPlanUnitHistory.AddRangeAsync(lessonUnitHistories).ConfigureAwait(false);
            //await _context.SaveChangesAsync().ConfigureAwait(false);
            return lessonUnitHistories;
        }

        private async Task DeleteCascadeObjects(LessonPlanUnit lessonPlanUnit)
        {
            var teacherLessonUnits = await _context.TeacherLessonUnit
                                                        .Where(l => l.UnitId == lessonPlanUnit.Id)
                                                        .ToListAsync().ConfigureAwait(false);
            _context.TeacherLessonUnit.RemoveRange(teacherLessonUnits);
        }

        public async Task ReorderUnitPlan(string lessonId)
        {
            var lessonPlanUnits = await _context.LessonPlanUnit
                                                    .Where(x => x.LessonPlanId == lessonId)
                                                    .OrderBy(u => u.SortOrder)
                                                    .ToListAsync().ConfigureAwait(false);
            int sortOrder = 1;
            foreach (var item in lessonPlanUnits)
            {
                item.SortOrder = sortOrder++;
                //_context.Entry(item).State = EntityState.Modified;
            }
            _context.UpdateRange(lessonPlanUnits);
        }

        private bool LessonPlanUnitExists(string id)
        {
            return _context.LessonPlanUnit.Any(e => e.Id == id);
        }

        [HttpPost("[action]/{lessonId}")]
        public async Task<ActionResult<LessonPlanUnit>> UpdateSortorderUnitPlan(string lessonId, List<LessonPlanUnit> lstSortOrderPlanUnit)
        {
            var lessonPlanUnits = _context.LessonPlanUnit.Where(x => x.LessonPlanId == lessonId);
            foreach (var item in lstSortOrderPlanUnit)
            {
                var objUpdate = lessonPlanUnits.Where(x => x.Id == item.Id).FirstOrDefault();
                if (objUpdate != null)
                {
                    objUpdate.SortOrder = item.SortOrder;
                    _context.Entry(objUpdate).State = EntityState.Modified;
                }
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return NoContent();
        }
        [HttpPost("[action]")]
        public async Task<ActionResult<LessonPlanUnit>> UpdateCreatedTimeByClass(List<LessonPlanUnit> lstSortOrderLessonPlanUnit)
        {
            var unitPlan =
                _context.LessonPlanUnit.Where(x => lstSortOrderLessonPlanUnit.Select(d => d.Id).Contains(x.Id));
            foreach (var item in lstSortOrderLessonPlanUnit)
            {
                var objUpdate = unitPlan.Where(x => x.Id == item.Id).FirstOrDefault();
                if (objUpdate != null)
                {
                    objUpdate.SortOrder = item.SortOrder;
                    _context.Entry(objUpdate).State = EntityState.Modified;
                }
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return NoContent();
        }
        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<UnitSearch>>> UnitSearch(string levelId, string classId, string campusId, DateTime? calendar)
        {

            var data = (from cc in _context.ClassCourse
                        join cl in _context.ClassLesson on cc.Id equals cl.ClassId
                        join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                        join l in _context.StudyLevel on cc.LevelId equals l.Id
                        join s in _context.Schedule on cc.Id equals s.ClassCourseId
                        join cr in _context.ClassRoom on s.ClassRoomId equals cr.Id into ljCR
                        from _cr in ljCR.DefaultIfEmpty()
                        select new UnitSearch()
                        {
                            Id = cl.Id,
                            RouteId = cc.Id,
                            Class = cc.Name,
                            ClassId = cc.Id,
                            Level = l.Name,
                            LevelId = l.Id,
                            Lesson = lp.Lesson,
                            Content = lp.Content,
                            Subject = lp.Subject,
                            Campus = _cr.Campus.Name,
                            CampusId = _cr.CampusId,
                            Calendar = cl.StartTime,
                            CreateTimeLesson = lp.CreatedDate
                        });
            if (!string.IsNullOrEmpty(levelId))
            {
                data = data.Where(x => x.LevelId == levelId);
            }
            if (!string.IsNullOrEmpty(classId))
            {
                data = data.Where(x => x.ClassId == classId);
            }
            if (!string.IsNullOrEmpty(campusId))
            {
                data = data.Where(x => x.CampusId == campusId);
            }
            if (calendar != null)
            {
                data = data.Where(x => x.Calendar.Value.Date == calendar.Value.Date);
            }

            return await data.OrderBy(x => x.Level).ThenBy(x => x.Class).ThenBy(x => x.CreateTimeLesson).ToListAsync().ConfigureAwait(false);
        }
    }
}
