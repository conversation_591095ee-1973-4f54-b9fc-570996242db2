﻿using GeeO.Data.Models;
using GeeO.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using IdentityServer4.Models;
using FormCollection = GeeO.Data.Models.FormCollection;

namespace GeeO.Data
{
    public partial class GeeODbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ProfileDataRequestContext context;

        public GeeODbContext()
        {
        }

        public GeeODbContext(DbContextOptions<GeeODbContext> options, IHttpContextAccessor httpContextAccessor, UserManager<ApplicationUser> userManager)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
            _userManager = userManager;
        }

        [DbFunction("RemoveDiacritics", "dbo")]
        public static string RemoveDiacritics(string input)
        {
            throw new NotImplementedException("This method can only be used with LINQ.");
        }

        [DbFunction("SearchTextRemovedDiacritics", "dbo")]
        public static bool SearchTextRemovedDiacritics(string searchExp, string expToBeSearched)
        {
            throw new NotImplementedException("This method can only be used with LINQ.");
        }

        public virtual DbSet<AspNetRoleClaims> AspNetRoleClaims { get; set; }
        public virtual DbSet<AspNetRoles> AspNetRoles { get; set; }
        public virtual DbSet<AspNetUserClaims> AspNetUserClaims { get; set; }
        public virtual DbSet<AspNetUserLogins> AspNetUserLogins { get; set; }
        public virtual DbSet<AspNetUserRoles> AspNetUserRoles { get; set; }
        public virtual DbSet<AspNetUserTokens> AspNetUserTokens { get; set; }
        public virtual DbSet<AspNetUsers> AspNetUsers { get; set; }
        public virtual DbSet<RequestGroup> RequestGroups { get; set; }
        public virtual DbSet<UserRequestGroups> UserRequestGroups { get; set; }
        public virtual DbSet<FormCollection> FormCollections { get; set; }
        public virtual DbSet<FormTemplate> FormTemplates { get; set; }
        public virtual DbSet<FormTemplateProcess> FormTemplateProcesses { get; set; }
        public virtual DbSet<FormTemplateGroup> FormTemplateGroups { get; set; }
        public virtual DbSet<FormTemplateField> FormTemplateFields { get; set; }
        public virtual DbSet<FieldTemplate> FieldTemplates { get; set; }
        public virtual DbSet<FormTemplateWatcher> FormTemplateWatchers { get; set; }
        public virtual DbSet<FormTemplateProcessApprover> FormTemplateProcessApprovers { get; set; }
        public virtual DbSet<FormTemplateProcessTrigger> FormTemplateProcessTriggers { get; set; }
        public virtual DbSet<FormTemplateCampus> FormTemplateCampuses { get; set; }
        public virtual DbSet<RequestForm> RequestForms { get; set; }
        public virtual DbSet<RequestFormField> RequestFormFields { get; set; }
        public virtual DbSet<RequestFormGroup> RequestFormGroups { get; set; }
        public virtual DbSet<RequestFormHandleWorkFlow> RequestFormHandleWorkFlows { get; set; }
        public virtual DbSet<RequestFormProcess> RequestFormProcesses { get; set; }
        public virtual DbSet<RequestFormWatcher> RequestFormWatchers { get; set; }
        public virtual DbSet<RequestFormProcessApprover> RequestFormProcessApprovers { get; set; }
        public virtual DbSet<RequestFormCommunicate> RequestFormCommunicates { get; set; }
        public virtual DbSet<RequestFormProcessTrigger> RequestFormProcessTriggers { get; set; }
        public virtual DbSet<RequestFormActivityLog> RequestFormActivityLogs { get; set; }
        public virtual DbSet<RequestFormProcessWorkLogField> RequestFormProcessWorkLogFields { get; set; }
        public virtual DbSet<DeviceCodes> DeviceCodes { get; set; }
        public virtual DbSet<PersistedGrants> PersistedGrants { get; set; }
        public virtual DbSet<StudyLevel> StudyLevel { get; set; }
        public virtual DbSet<Material> Material { get; set; }
        public virtual DbSet<LessonPlan> LessonPlan { get; set; }
        public virtual DbSet<LessonPlanUnit> LessonPlanUnit { get; set; }
        public virtual DbSet<LessonPlanUnitHistory> LessonPlanUnitHistory { get; set; }
        public virtual DbSet<ClassCourse> ClassCourse { get; set; }
        public virtual DbSet<Student> Student { get; set; }
        public virtual DbSet<Templates> Templates { get; set; }
        public virtual DbSet<ClassTeacher> ClassTeacher { get; set; }
        public virtual DbSet<ClassLesson> ClassLesson { get; set; }
        public virtual DbSet<TeacherLessonUnit> TeacherLessonUnit { get; set; }
        public virtual DbSet<TeacherLessonUnitHistory> TeacherLessonUnitHistory { get; set; }
        public virtual DbSet<TeacherLessonLog> TeacherLessonLog { get; set; }
        public virtual DbSet<TeacherLessonLogData> TeacherLessonLogData { get; set; }
        public virtual DbSet<StudentLessonLogData> StudentLessonLogData { get; set; }
        public virtual DbSet<RoomType> RoomType { get; set; }
        public virtual DbSet<ClassRoom> ClassRoom { get; set; }
        public virtual DbSet<Parent> Parent { get; set; }
        public virtual DbSet<StudentParent> StudentParent { get; set; }
        public virtual DbSet<ClassStudent> ClassStudent { get; set; }
        public virtual DbSet<CatchUpSchedules> CatchUpSchedules { get; set; }
        public virtual DbSet<SingleLessons> SingleLessons { get; set; }
        public virtual DbSet<SingleLessonStudents> SingleLessonStudents { get; set; }
        public virtual DbSet<SingleLessonTeachers> SingleLessonTeachers { get; set; }
        public virtual DbSet<TaskSys> TaskSys { get; set; }
        public virtual DbSet<Schedule> Schedule { get; set; }
        public virtual DbSet<StudentCourse> StudentCourse { get; set; }
        public virtual DbSet<Campus> Campus { get; set; }
        public virtual DbSet<LessonComment> LessonComment { get; set; }
        public virtual DbSet<LessonContent> LessonContent { get; set; }
        public virtual DbSet<StudentAssessment> StudentAssessment { get; set; }
        public virtual DbSet<AssessmentCriteria> AssessmentCriteria { get; set; }
        public virtual DbSet<StudentClassChange> StudentClassChange { get; set; }
        public virtual DbSet<AcadAnnounce> AcadAnnounce { get; set; }
        public virtual DbSet<ExamResult> ExamResult { get; set; }
        public virtual DbSet<ExamResultForm> ExamResultForm { get; set; }
        public virtual DbSet<Holidays> Holidays { get; set; }
        public virtual DbSet<Events> Events { get; set; }
        public virtual DbSet<Notifications> Notifications { get; set; }
        public virtual DbSet<StudentExternalAccount> StudentExternalAccount { get; set; }
        public virtual DbSet<UserEmergencyContacts> UserEmergencyContacts { get; set; }
        public virtual DbSet<UserIdentifications> UserIdentifications { get; set; }
        public virtual DbSet<UserDocuments> UserDocuments { get; set; }
        public virtual DbSet<UserInfo> UserInfo { get; set; }
        public virtual DbSet<GeeOStory> GeeOStory { get; set; }
        public virtual DbSet<GeeOStoryComment> GeeOStoryComment { get; set; }
        public virtual DbSet<ChatMessage> ChatMessage { get; set; }
        public virtual DbSet<PendingChatMessage> PendingChatMessage { get; set; }
        public virtual DbSet<ParentAuthenticationLog> ParentAuthenticationLog { get; set; }
        public virtual DbSet<LessonMedia> LessonMedia { get; set; }
        public virtual DbSet<VwStudentPayment> StudentPayments { get; set; }
        public virtual DbSet<VwStudentSuspend> StudentSuspends { get; set; }
        public virtual DbSet<WorkingTime> WorkingTimes { get; set; }
        public virtual DbSet<WorkLog> WorkLogs { get; set; }
        public virtual DbSet<ShiftType> ShiftTypes { get; set; }
        public virtual DbSet<WorkLogsSingleLessons> WorkLogsSingleLessons { get; set; }
        public virtual DbSet<WorkLogsTeacherLessonLogs> WorkLogsTeacherLessonLogs { get; set; }
        public virtual DbSet<InstallmentPayments> InstallmentPayments { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.AddInterceptors(new SaveChangesInterceptor(_httpContextAccessor));
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasAnnotation("ProductVersion", "3.0.0-preview4.19216.3");

            modelBuilder.Entity<AspNetRoleClaims>(entity =>
            {
                entity.HasIndex(e => e.RoleId);

                //entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.RoleId).IsRequired();

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AspNetRoleClaims)
                    .HasForeignKey(d => d.RoleId);
            });

            modelBuilder.Entity<AspNetRoles>(entity =>
            {
                entity.HasIndex(e => e.NormalizedName)
                    .HasName("RoleNameIndex")
                    .IsUnique();

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.NormalizedName).HasMaxLength(256);
            });

            modelBuilder.Entity<AspNetUserClaims>(entity =>
            {
                entity.HasIndex(e => e.UserId);

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.UserId).IsRequired();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.AspNetUserClaims)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<AspNetUserLogins>(entity =>
            {
                entity.HasKey(e => new { e.LoginProvider, e.ProviderKey });

                entity.HasIndex(e => e.UserId);

                entity.Property(e => e.LoginProvider).HasMaxLength(128);

                entity.Property(e => e.ProviderKey).HasMaxLength(128);

                entity.Property(e => e.UserId).IsRequired();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.AspNetUserLogins)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<AspNetUserRoles>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.RoleId });

                entity.HasIndex(e => e.RoleId);

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AspNetUserRoles)
                    .HasForeignKey(d => d.RoleId);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.AspNetUserRoles)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<AspNetUserTokens>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.LoginProvider, e.Name });

                entity.Property(e => e.LoginProvider).HasMaxLength(128);

                entity.Property(e => e.Name).HasMaxLength(128);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.AspNetUserTokens)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<AspNetUsers>(entity =>
            {
                entity.HasIndex(e => e.NormalizedEmail)
                    .HasName("EmailIndex");

                entity.HasIndex(e => e.NormalizedUserName)
                    .HasName("UserNameIndex")
                    .IsUnique();

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Email).HasMaxLength(256);

                entity.Property(e => e.NormalizedEmail).HasMaxLength(256);

                entity.Property(e => e.NormalizedUserName).HasMaxLength(256);

                entity.Property(e => e.UserName).HasMaxLength(256);

                entity.Property(e => e.EmployeeCode).HasMaxLength(256);

                entity.Property(e => e.FirstName).HasMaxLength(256);

                entity.Property(e => e.LastName).HasMaxLength(256);

                entity.HasOne(x => x.UserInfo)
                        .WithOne(x => x.User)
                        .HasForeignKey<UserInfo>(d => d.UserId);
            });

            modelBuilder.Entity<RequestGroup>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<UserRequestGroups>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestGroups)
                    .WithMany(p => p.UserRequestGroups)
                    .HasForeignKey(d => d.GroupId);
                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserRequestGroups)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<FormCollection>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<FormTemplate>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormCollections)
                    .WithMany(p => p.FormTemplates)
                    .HasForeignKey(d => d.FormCollectionId);
                entity.HasOne(d => d.Roles)
                    .WithMany(p => p.FormTemplates)
                    .HasForeignKey(d => d.RoleId);
            });

            modelBuilder.Entity<FormTemplateProcess>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FormTemplateProcesses)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.WorkLogTemplates)
                    .WithMany(p => p.FormTemplateProcessWorkLogTemplates)
                    .HasForeignKey(d => d.WorkLogTemplateId);
                entity.HasOne(d => d.NextProcessIfDenied)
                    .WithMany(p => p.InverseNextProcessIfDenieds)
                    .HasForeignKey(d => d.NextProcessIfDeniedId);
            });

            modelBuilder.Entity<FormTemplateGroup>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FormTemplateGroups)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.RequestGroups)
                    .WithMany(p => p.FormTemplateGroups)
                    .HasForeignKey(d => d.GroupId);
            });

            modelBuilder.Entity<FormTemplateField>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FormTemplateFields)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.FieldTemplates)
                    .WithMany(p => p.FormTemplateFields)
                    .HasForeignKey(d => d.FieldTemplateId);
            });

            modelBuilder.Entity<FieldTemplate>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FieldTemplates)
                    .HasForeignKey(d => d.FormTemplateId);
            });

            modelBuilder.Entity<FormTemplateWatcher>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FormTemplateWatchers)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.AspNetUsers)
                    .WithMany(p => p.FormTemplateWatchers)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<FormTemplateProcessApprover>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplateProcesses)
                    .WithMany(p => p.FormTemplateProcessApprovers)
                    .HasForeignKey(d => d.FormTemplateProcessId);
                entity.HasOne(d => d.AspNetUsers)
                    .WithMany(p => p.FormTemplateProcessApprovers)
                    .HasForeignKey(d => d.UserId);
                entity.HasOne(d => d.AspNetRoles)
                    .WithMany(p => p.FormTemplateProcessApprovers)
                    .HasForeignKey(d => d.RoleId);
                entity.HasOne(d => d.RequestGroups)
                    .WithMany(p => p.FormTemplateProcessApprovers)
                    .HasForeignKey(d => d.GroupId);
            });

            modelBuilder.Entity<FormTemplateProcessTrigger>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplateProcesses)
                    .WithMany(p => p.FormTemplateProcessTriggers)
                    .HasForeignKey(d => d.FormTemplateProcessId);
            });

            modelBuilder.Entity<FormTemplateCampus>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.FormTemplates)
                    .WithMany(p => p.FormTemplateCampuses)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.Campus)
                    .WithMany(p => p.FormTemplateCampuses)
                    .HasForeignKey(d => d.CampusId);
            });

            modelBuilder.Entity<RequestForm>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(250);
                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(450);
                entity.HasOne(d => d.FormTemplate)
                    .WithMany(p => p.RequestForms)
                    .HasForeignKey(d => d.FormTemplateId);
                entity.HasOne(d => d.AspNetUser)
                    .WithMany(p => p.RequestForms)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<RequestFormGroup>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForm)
                    .WithMany(p => p.RequestFormGroups)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.RequestGroup)
                    .WithMany(p => p.RequestFormGroups)
                    .HasForeignKey(d => d.GroupId);
            });

            modelBuilder.Entity<RequestFormHandleWorkFlow>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Note).HasMaxLength(100);
                entity.Property(e => e.RequestFormId)
                    .IsRequired()
                    .HasMaxLength(450);
                entity.Property(e => e.RequestFormProcessId)
                    .IsRequired()
                    .HasMaxLength(450);
                entity.HasOne(d => d.RequestForm)
                    .WithMany(p => p.RequestFormHandleWorkFlows)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.RequestFormProcess)
                    .WithMany(p => p.RequestFormHandleWorkFlows)
                    .HasForeignKey(d => d.RequestFormProcessId);
            });

            modelBuilder.Entity<RequestFormProcess>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForms)
                    .WithMany(p => p.RequestFormProcesses)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.WorkLogTemplate)
                    .WithMany(p => p.RequestFormProcesses)
                    .HasForeignKey(d => d.WorkLogTemplateId);
            });

            modelBuilder.Entity<RequestFormWatcher>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForms)
                    .WithMany(p => p.RequestFormWatchers)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.Users)
                    .WithMany(p => p.RequestFormWatchers)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<RequestFormProcessApprover>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestFormProcess)
                    .WithMany(p => p.RequestFormProcessApprovers)
                    .HasForeignKey(d => d.RequestFormProcessId);
                entity.HasOne(d => d.AspNetRoles)
                    .WithMany(p => p.RequestFormProcessApprovers)
                    .HasForeignKey(d => d.RoleId);
                entity.HasOne(d => d.Users)
                    .WithMany(p => p.RequestFormProcessApprovers)
                    .HasForeignKey(d => d.UserId);
                entity.HasOne(d => d.RequestGroups)
                    .WithMany(p => p.RequestFormProcessApprovers)
                    .HasForeignKey(d => d.GroupId);
            });

            modelBuilder.Entity<RequestFormCommunicate>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForms)
                    .WithMany(p => p.RequestFormCommunicates)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.Users)
                    .WithMany(p => p.RequestFormCommunicates)
                    .HasForeignKey(d => d.UserId);
                entity.HasOne(d => d.Root)
                    .WithMany(p => p.InverseRoot)
                    .HasForeignKey(d => d.RootId);
            });

            modelBuilder.Entity<RequestFormProcessTrigger>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestFormProcess)
                    .WithMany(p => p.RequestFormProcessTriggers)
                    .HasForeignKey(d => d.RequestFormProcessId);
            });

            modelBuilder.Entity<RequestFormActivityLog>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForms)
                    .WithMany(p => p.RequestFormActivityLogs)
                    .HasForeignKey(d => d.RequestFormId);
                entity.HasOne(d => d.Users)
                    .WithMany(p => p.RequestFormActivityLogs)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<RequestFormProcessWorkLogField>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestFormProcess)
                    .WithMany(p => p.RequestFormProcessWorkLogFields)
                    .HasForeignKey(d => d.RequestFormProcessId);
            });

            modelBuilder.Entity<RequestFormField>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.RequestForms)
                    .WithMany(p => p.RequestFormFields)
                    .HasForeignKey(d => d.RequestFormId);
            });

            modelBuilder.Entity<DeviceCodes>(entity =>
            {
                entity.HasKey(e => e.UserCode);

                entity.HasIndex(e => e.DeviceCode)
                    .IsUnique();

                entity.HasIndex(e => e.UserCode)
                    .IsUnique();

                entity.Property(e => e.UserCode)
                    .HasMaxLength(200)
                    .ValueGeneratedNever();

                entity.Property(e => e.ClientId)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.DeviceCode)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.SubjectId).HasMaxLength(200);
            });

            modelBuilder.Entity<PersistedGrants>(entity =>
            {
                entity.HasKey(e => e.Key);

                entity.HasIndex(e => new { e.SubjectId, e.ClientId, e.Type });

                entity.Property(e => e.Key)
                    .HasMaxLength(200)
                    .ValueGeneratedNever();

                entity.Property(e => e.ClientId)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.SubjectId).HasMaxLength(200);

                entity.Property(e => e.Type)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<AcadAnnounce>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Content).HasMaxLength(2000);

                entity.HasOne(d => d.ClassCourse)
                    .WithMany(p => p.AcadAnnounces)
                    .HasForeignKey(d => d.ClassId);
            });

            modelBuilder.Entity<ExamResult>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });
            modelBuilder.Entity<ExamResultForm>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<Holidays>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<Events>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<Notifications>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<StudentExternalAccount>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<UserEmergencyContacts>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<UserIdentifications>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<UserDocuments>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.DocumentType)
                    .HasConversion<string>();
            });

            modelBuilder.Entity<UserInfo>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(u => u.MaritalStatus)
                      .HasConversion<string>();
                entity.Property(u => u.Gender)
                      .HasConversion<string>();

                entity.HasOne(x => x.UserEmergencyContacts)
                        .WithOne(x => x.UserInfo)
                        .HasForeignKey<UserEmergencyContacts>(d => d.UserInfoId);
                entity.HasOne(x => x.UserIdentifications)
                       .WithOne(x => x.UserInfo)
                       .HasForeignKey<UserIdentifications>(d => d.UserInfoId);
                entity.HasMany(x => x.UserDocuments)
                        .WithOne(x => x.UserInfo)
                        .HasForeignKey(x => x.UserInfoId);
            });

            modelBuilder.Entity<StudyLevel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Description).HasMaxLength(256);

                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.Entity<Material>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Description).HasMaxLength(256);

                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Url).HasMaxLength(1024);

                entity.Property(e => e.FileName).HasMaxLength(256);

                entity.Property(e => e.TeacherId)
                    .HasMaxLength(450);

                entity.HasOne(d => d.Teacher)
                    .WithMany(p => p.Materials)
                    .HasForeignKey(d => d.TeacherId);
            });

            modelBuilder.Entity<LessonPlan>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Content).HasMaxLength(512);

                entity.Property(e => e.Lesson).HasMaxLength(256);

                entity.Property(e => e.Subject).HasMaxLength(256);

                entity.Property(e => e.Tb).HasMaxLength(256);

                entity.Property(e => e.LevelId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Level)
                    .WithMany(p => p.LessonPlans)
                    .HasForeignKey(d => d.LevelId);
            });

            modelBuilder.Entity<LessonPlanUnit>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Procedures).HasMaxLength(512);

                entity.Property(e => e.Description).HasMaxLength(2048);

                entity.Property(e => e.Materials).HasMaxLength(512);

                entity.Property(e => e.TeacherActivities).HasMaxLength(2048);

                entity.Property(e => e.LearningOutcome).HasMaxLength(2048);

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.LessonPlanId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.Property(e => e.MaterialId).HasMaxLength(450);

                entity.HasOne(d => d.Material)
                    .WithMany(p => p.LessonPlanUnits)
                    .HasForeignKey(d => d.MaterialId);

                entity.HasOne(d => d.LessonPlan)
                    .WithMany(p => p.LessonPlanUnits)
                    .HasForeignKey(d => d.LessonPlanId);
            });

            modelBuilder.Entity<LessonPlanUnitHistory>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Procedures).HasMaxLength(512);

                entity.Property(e => e.Description).HasMaxLength(2048);

                entity.Property(e => e.Materials).HasMaxLength(512);

                entity.Property(e => e.TeacherActivities).HasMaxLength(2048);

                entity.Property(e => e.LearningOutcome).HasMaxLength(2048);

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.LessonPlanId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.Property(e => e.MaterialId).HasMaxLength(450);

                entity.HasOne(d => d.Material)
                    .WithMany(p => p.LessonPlanUnitHistories)
                    .HasForeignKey(d => d.MaterialId);

                entity.HasOne(d => d.LessonPlan)
                    .WithMany(p => p.LessonPlanUnitHistories)
                    .HasForeignKey(d => d.LessonPlanId);
            });

            modelBuilder.Entity<Student>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.StudentName).HasMaxLength(256);

                entity.Property(e => e.EnglishName).HasMaxLength(256);
                entity.Property(e => e.Address).HasMaxLength(1024);
                entity.Property(e => e.FirstCourse).HasMaxLength(256);
                entity.Property(e => e.RenewCourse).HasMaxLength(256);

                entity.Property(e => e.Note).HasMaxLength(1024);
            });

            modelBuilder.Entity<Templates>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<ClassCourse>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Description).HasMaxLength(512);
                entity.Property(e => e.NumberOfPupils);

                entity.Property(e => e.LevelId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Level)
                    .WithMany(p => p.ClassCourses)
                    .HasForeignKey(d => d.LevelId);

                entity.HasOne(d => d.Campus)
                    .WithMany(p => p.ClassCourses)
                    .HasForeignKey(d => d.CampusId);

                entity.HasOne(x => x.Schedule)
                    .WithOne(x => x.ClassCourses)
                    .HasForeignKey<Schedule>(d => d.ClassCourseId);
            });

            modelBuilder.Entity<ClassTeacher>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.ClassId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.ClassCourse)
                    .WithMany(p => p.ClassTeachers)
                    .HasForeignKey(d => d.ClassId);

                entity.Property(e => e.TeacherId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Teacher)
                    .WithMany(p => p.ClassTeachers)
                    .HasForeignKey(d => d.TeacherId);
            });

            modelBuilder.Entity<ClassLesson>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.ClassId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.ClassCourse)
                    .WithMany(p => p.ClassLessons)
                    .HasForeignKey(d => d.ClassId);

                entity.Property(e => e.LessonId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Lesson)
                    .WithMany(p => p.ClassLessons)
                    .HasForeignKey(d => d.LessonId);
            });

            modelBuilder.Entity<TeacherLessonUnit>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.TeacherId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Teacher)
                    .WithMany(p => p.TeacherLessonUnits)
                    .HasForeignKey(d => d.TeacherId);

                entity.Property(e => e.ClassLessonId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.ClassLesson)
                    .WithMany(p => p.TeacherLessonUnits)
                    .HasForeignKey(d => d.ClassLessonId);

                entity.Property(e => e.UnitId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Unit)
                    .WithMany(p => p.TeacherLessonUnits)
                    .HasForeignKey(d => d.UnitId);

                entity.Property(e => e.MaterialId)
                    .IsRequired(false)
                    .HasMaxLength(450);

                entity.HasOne(d => d.Material)
                    .WithMany(p => p.TeacherLessonUnits)
                    .HasForeignKey(d => d.MaterialId).IsRequired(false);
            });

            modelBuilder.Entity<TeacherLessonUnitHistory>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.HistoryUnitId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.HistoryUnit)
                    .WithMany(p => p.TeacherLessonHistoryUnits)
                    .HasForeignKey(d => d.HistoryUnitId);

                entity.Property(e => e.MaterialId)
                    .IsRequired(false)
                    .HasMaxLength(450);

                entity.HasOne(d => d.Material)
                    .WithMany(p => p.TeacherLessonHistoryUnits)
                    .HasForeignKey(d => d.MaterialId).IsRequired(false);
            });

            modelBuilder.Entity<TeacherLessonLog>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.TeacherId)
                .HasMaxLength(450);

                entity.HasOne(d => d.Teacher)
                    .WithMany(p => p.TeacherLessonLogs)
                    .HasForeignKey(d => d.TeacherId);

                entity.Property(e => e.ClassLessonId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.ClassLesson)
                    .WithMany(p => p.TeacherLessonLogs)
                    .HasForeignKey(d => d.ClassLessonId);
            });

            modelBuilder.Entity<TeacherLessonLogData>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.LogId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.LessonLog)
                    .WithMany(p => p.LogDatas)
                    .HasForeignKey(d => d.LogId);

                entity.Property(e => e.UnitId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.Unit)
                    .WithMany(p => p.TeacherLessonLogDatas)
                    .HasForeignKey(d => d.UnitId);

                entity.HasOne(d => d.HistoryUnit)
                    .WithMany(p => p.TeacherLessonLogDatas)
                    .HasForeignKey(d => d.HistoryUnitId);
            });

            modelBuilder.Entity<StudentLessonLogData>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Note).HasMaxLength(1024);

                entity.Property(e => e.LogId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.LessonLog)
                    .WithMany(p => p.StudentLogDatas)
                    .HasForeignKey(d => d.LogId);

                entity.Property(e => e.StudentInfoId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.StudentInfo)
                    .WithMany(p => p.StudentLessonLogDatas)
                    .HasForeignKey(d => d.StudentInfoId);
            });

            modelBuilder.Entity<StudentAssessment>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.StudentLessonLogId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.StudentLessonLog)
                    .WithMany(p => p.StudentAssessments)
                    .HasForeignKey(d => d.StudentLessonLogId);

                entity.Property(e => e.AssessmentCriteriaId)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.HasOne(d => d.AssessmentCriteria)
                    .WithMany(p => p.StudentAssessments)
                    .HasForeignKey(d => d.AssessmentCriteriaId);
            });

            modelBuilder.Entity<AssessmentCriteria>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(500);
            });

            modelBuilder.Entity<RoomType>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
            });

            modelBuilder.Entity<ClassRoom>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.RoomNumber).HasMaxLength(450);
                entity.Property(x => x.CampusId).HasMaxLength(450);
                entity.Property(x => x.RoomTypeId).HasMaxLength(450);
                entity.HasOne(d => d.RoomType)
                    .WithMany(p => p.ClassRooms)
                    .HasForeignKey(d => d.RoomTypeId);
                entity.HasOne(d => d.Campus)
                    .WithMany(p => p.ClassRooms)
                    .HasForeignKey(d => d.CampusId);
            });

            modelBuilder.Entity<Parent>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.Job).HasMaxLength(450);
                entity.Property(x => x.Relation);
                entity.Property(x => x.Email).HasMaxLength(450);
                entity.Property(x => x.PhoneNumber).HasMaxLength(450);
                entity.Property(x => x.Address).HasMaxLength(450);

            });

            modelBuilder.Entity<StudentParent>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.ParentId).HasMaxLength(450);
                entity.Property(x => x.StudentId).HasMaxLength(450);

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.StudentParents)
                    .HasForeignKey(d => d.ParentId);

                entity.HasOne(d => d.Student)
                    .WithMany(p => p.StudentParents)
                    .HasForeignKey(d => d.StudentId);
            });

            modelBuilder.Entity<ClassStudent>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.ClassId).HasMaxLength(450);
                entity.Property(x => x.StudentId).HasMaxLength(450);
                entity.Property(x => x.SortOrder);
                entity.Property(e => e.RegisterDate).HasDefaultValueSql("getdate()");

                entity.HasOne(d => d.ClassCourse)
                    .WithMany(p => p.ClassStudents)
                    .HasForeignKey(d => d.ClassId);

                entity.HasOne(d => d.Student)
                    .WithMany(p => p.ClassStudents)
                    .HasForeignKey(d => d.StudentId);
            });

            modelBuilder.Entity<StudentClassChange>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.StudentId).HasMaxLength(450);
                entity.Property(x => x.PreviousClassId).HasMaxLength(450);

                entity.HasOne(d => d.ClassCourse)
                    .WithMany(p => p.StudentClassChanges)
                    .HasForeignKey(d => d.PreviousClassId);

                entity.HasOne(d => d.Student)
                    .WithMany(p => p.StudentClassChanges)
                    .HasForeignKey(d => d.StudentId);
            });

            modelBuilder.Entity<CatchUpSchedules>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.TeacherId);
                entity.Property(x => x.NewClassId);

                entity.HasOne(d => d.ClassLesson).WithMany(x => x.CatchUpSchedules).HasForeignKey(d => d.ClassLessonId).IsRequired(false);
                entity.HasOne(d => d.AspNetUsers).WithMany(x => x.CatchUpSchedules).HasForeignKey(d => d.TeacherId);
                entity.HasOne(x => x.Student)
                    .WithOne(x => x.CatchUpSchedules)
                    .HasForeignKey<CatchUpSchedules>(d => d.StudentId).IsRequired(false);
            });

            modelBuilder.Entity<SingleLessons>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.IsArchived).HasMaxLength(450);
            });

            modelBuilder.Entity<SingleLessonStudents>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.SingleLessons)
                    .WithMany(p => p.SingleLessonStudents)
                    .HasForeignKey(d => d.SingleLessonId);
                entity.HasOne(d => d.Student)
                    .WithMany(p => p.SingleLessonStudents)
                    .HasForeignKey(d => d.StudentId);
            });

            modelBuilder.Entity<SingleLessonTeachers>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.SingleLessons)
                    .WithMany(p => p.SingleLessonTeachers)
                    .HasForeignKey(d => d.SingleLessonId);
                entity.HasOne(d => d.AspNetUsers)
                    .WithMany(p => p.SingleLessonTeachers)
                    .HasForeignKey(d => d.TeacherId);
            });

            modelBuilder.Entity<TaskSys>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.Note).HasMaxLength(450);
                entity.Property(x => x.UserId).HasMaxLength(450);

                entity.HasOne(d => d.AspNetUsers)
                    .WithMany(p => p.Task)
                    .HasForeignKey(d => d.UserId);
                entity.HasOne(d => d.AssignUser)
                    .WithMany(p => p.AssignTask)
                    .HasForeignKey(d => d.AssignUserId).IsRequired(false);
                entity.HasOne(d => d.ApprovedUser)
                    .WithMany(p => p.ApprovedTask)
                    .HasForeignKey(d => d.ApprovedUserId);
            });

            modelBuilder.Entity<Schedule>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.ClassRoomId).HasMaxLength(450);
                entity.Property(x => x.ClassCourseId).HasMaxLength(450);

                entity.HasOne(d => d.ClassRoom)
                    .WithMany(p => p.Schedules)
                    .HasForeignKey(d => d.ClassRoomId).IsRequired(false);

            });

            modelBuilder.Entity<StudentCourse>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.NumberOfSession);

                entity.HasOne(d => d.Student)
                    .WithMany(p => p.StudentCourses)
                    .HasForeignKey(d => d.StudentId).IsRequired(false);

            });

            modelBuilder.Entity<InstallmentPayments>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.StudentCourse)
                    .WithMany(p => p.InstallmentPayments)
                    .HasForeignKey(d => d.StudentCourseId);
            });

            modelBuilder.Entity<Campus>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.Name).HasMaxLength(450);
                entity.Property(x => x.Address).HasMaxLength(450);
            });

            modelBuilder.Entity<LessonComment>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.ClassLessonId).HasMaxLength(450);
                entity.Property(x => x.UserId).HasMaxLength(450);
            });

            modelBuilder.Entity<LessonContent>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(x => x.LessonPlanId).HasMaxLength(450);
            });

            modelBuilder.Entity<GeeOStory>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Author)
                    .WithMany(p => p.GeeOStories)
                    .HasForeignKey(d => d.AuthorId);

                entity.HasOne(d => d.Class)
                    .WithMany(p => p.Stories)
                    .HasForeignKey(d => d.ClassId);
            });
            modelBuilder.Entity<GeeOStoryComment>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Story)
                    .WithMany(p => p.GeeOStoryComments)
                    .HasForeignKey(d => d.StoryId);

                entity.HasOne(d => d.Commenter)
                    .WithMany(p => p.GeeOStoryComments)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<ChatMessage>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });
            modelBuilder.Entity<PendingChatMessage>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<ParentAuthenticationLog>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<LessonMedia>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<VwStudentPayment>(eb =>
            {
                eb.HasNoKey();
                eb.ToView("View_StudentPayment");
            });

            modelBuilder.Entity<VwStudentSuspend>(eb =>
            {
                eb.HasNoKey();
                eb.ToView("View_StudentSuspend");
            });


            modelBuilder.Entity<WorkLog>().Property(e => e.Id).ValueGeneratedOnAdd();

            modelBuilder.Entity<WorkingTime>(entity =>
            {
                entity.HasIndex(e => e.StaffId);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.HasOne(d => d.Staff)
                    .WithMany(p => p.WorkingTimes)
                    .HasForeignKey(d => d.StaffId);
            });

            modelBuilder.Entity<WorkLogsSingleLessons>().Property(e => e.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<WorkLogsTeacherLessonLogs>().Property(e => e.Id).ValueGeneratedOnAdd();

            modelBuilder.Entity<ActivityLog>(entity =>
           {
               entity.Property(e => e.Id).ValueGeneratedOnAdd();
               entity.HasOne(d => d.User)
                                   .WithMany(p => p.ActivityLogs)
                                   .HasForeignKey(d => d.UserId);
           });
        }
    }
}