﻿@using Microsoft.Extensions.Hosting
@using Microsoft.AspNetCore.Mvc.ViewEngines
@inject IHostEnvironment Environment
@inject ICompositeViewEngine Engine
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Gee-<PERSON> English</title>

    <environment include="Development">
        <link rel="stylesheet" href="~/Identity/lib/bootstrap/dist/css/bootstrap.css" />
    </environment>
    <environment exclude="Development">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
              asp-fallback-href="~/Identity/lib/bootstrap/dist/css/bootstrap.min.css"
              asp-fallback-test-class="sr-only" asp-fallback-test-property="position" asp-fallback-test-value="absolute" />
    </environment>
    <!-- Compiled and minified CSS -->
    @*<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-beta/css/materialize.min.css">*@

    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/font-faces.css" />
    <link rel="stylesheet" href="~/css/typography.css" />
    <link rel="stylesheet" href="~/css/gee-o.css" />
    <link rel="stylesheet" href="~/css/account/login.css" />
    <link rel="shortcut icon" href="~/favicon.ico" />
</head>
<body>
    <div class="container-fluid">
        <partial name="_CookieConsentPartial" optional />
        @RenderBody()
    </div>

    <environment include="Development">
        <script src="~/Identity/lib/jquery/dist/jquery.js"></script>
        <script src="~/Identity/lib/bootstrap/dist/js/bootstrap.bundle.js"></script>
    </environment>
    <environment exclude="Development">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"
                asp-fallback-src="~/Identity/lib/jquery/dist/jquery.min.js"
                asp-fallback-test="window.jQuery"
                crossorigin="anonymous"
                integrity="sha384-tsQFqpEReu7ZLhBV2VZlAu7zcOV+rXbYlF2cqB8txI/8aZajjp4Bqd+V6D5IgvKT">
        </script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"
                asp-fallback-src="~/Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js"
                asp-fallback-test="window.jQuery && window.jQuery.fn && window.jQuery.fn.modal"
                crossorigin="anonymous"
                integrity="sha384-xrRywqdh3PHs8keKZN+8zzc5TX0GRTLCcmivcbNJWm2rs5C8PRhcEn3czEjhAO9o">
        </script>
    </environment>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <!-- Compiled and minified JavaScript -->
    @*<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-beta/js/materialize.min.js"></script>*@

    @RenderSection("Scripts", required: false)
</body>
</html>
