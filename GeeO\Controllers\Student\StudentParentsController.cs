﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StudentParentsController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public StudentParentsController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/StudentParents
        [HttpGet]
        public async Task<ActionResult<IEnumerable<StudentParent>>> GetStudentParent()
        {
            return await _context.StudentParent.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/StudentParents/5
        [HttpGet("{id}")]
        public async Task<ActionResult<StudentParent>> GetStudentParent(string id)
        {
            var studentParent = await _context.StudentParent.FindAsync(id);

            if (studentParent == null)
            {
                return NotFound();
            }

            return studentParent;
        }

        // PUT: api/StudentParents/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutStudentParent(string id, StudentParent studentParent)
        {
            if (id != studentParent.Id)
            {
                return BadRequest();
            }

            _context.Entry(studentParent).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StudentParentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/StudentParents
        [HttpPost("addParent/{studentId}")]
        public async Task<ActionResult> AddParent(string studentId, List<string> lstParentId)
        {
            foreach (var item in lstParentId)
            {
                StudentParent studentParent = new StudentParent()
                {
                    ParentId = item,
                    StudentId = studentId
                };
                _context.StudentParent.Add(studentParent);
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        // POST: api/StudentParents
        [HttpPost("addStudent/{parentId}")]
        public async Task<ActionResult> AddStudent(string parentId, List<string> lstStudentParents)
        {
            foreach (var item in lstStudentParents)
            {
                StudentParent studentParent = new StudentParent()
                {
                    ParentId = parentId,
                    StudentId = item
                };
                _context.StudentParent.Add(studentParent);
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        // POST: api/StudentParents
        [HttpPost]
        public async Task<ActionResult<StudentParent>> PostStudentParent(StudentParent studentParent)
        {
            _context.StudentParent.Add(studentParent);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStudentParent", new { id = studentParent.Id }, studentParent);
        }

        // DELETE: api/StudentParents/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<StudentParent>> DeleteStudentParent(string id)
        {
            var studentParent = await _context.StudentParent.FindAsync(id);
            if (studentParent == null)
            {
                return NotFound();
            }

            _context.StudentParent.Remove(studentParent);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return studentParent;
        }

        private bool StudentParentExists(string id)
        {
            return _context.StudentParent.Any(e => e.Id == id);
        }

        [HttpGet("[action]/{studentId}/{relation}")]
        public async Task<ActionResult<Parent>> GetParentByStudent(string studentId, int relation)
        {
            var data = from prn in _context.Parent.Where(x => x.Relation == relation)
                       from spr in _context.StudentParent.Where(x => x.StudentId == studentId && x.ParentId == prn.Id)
                       select prn;
            return await data.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        [HttpGet("getParentByStudentIds/{studentId}")]
        public async Task<ActionResult<IEnumerable<Parent>>> GetParentByStudentIds(string studentId)
        {
            var data = await _context.Parent.Include(x => x.StudentParents)
                .Where(x => x.StudentParents.Select(c => c.StudentId).Contains(studentId)).ToListAsync().ConfigureAwait(false);
            return data;
        }

        [HttpGet("getStudentByParentIds/{parentId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentByParentIds(string parentId)
        {
            var data = _context.StudentParent.Include(x => x.Parent).Where(x => x.ParentId == parentId);
            var result = from x in data
                select new StudentGrid
                {
                    Id = x.Id,
                    SubId = x.StudentId,
                    StudentName = x.Student.StudentName,
                    EnglishName = x.Student.EnglishName,
                    Birthday = x.Student.Birthday
                };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet]
        [Route("list-filter-parent/{studentId}")]
        public async Task<ActionResult<IEnumerable<Parent>>> GetParentSelect(string studentId)
        {
            var dataTemp = await _context.StudentParent.Where(x => x.StudentId == studentId).Select(x=> x.ParentId).ToListAsync().ConfigureAwait(false);
            var data = _context.Parent.Where(x => !dataTemp.Contains(x.Id));
            return await data.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet]
        [Route("list-filter-student/{parentId}")]
        public async Task<ActionResult<IEnumerable<Student>>> GetStudentSelect(string parentId)
        {
            var dataTemp = await _context.StudentParent.Where(x => x.ParentId == parentId).Select(x => x.StudentId).ToListAsync().ConfigureAwait(false);
            var data = _context.Student.Where(x => !dataTemp.Contains(x.Id));
            return await data.ToListAsync().ConfigureAwait(false);
        }
    }
}
