USE [GeeODb]
GO

INSERT INTO [dbo].[StudentAssessment]
           ([Id]
           ,[StudentLessonLogId]
           ,[AssessmentCriteriaId]
           ,[Score])
SELECT
           LOWER(CONVERT(nvarchar(450), NEWID()))
           ,sll.Id
           ,'7df76aef-7d80-4aa0-b185-a63061a65176'
           ,Cast(FLOOR(RAND()*(3)+1) as int)
FROM StudentLessonLogData sll

INSERT INTO [dbo].[StudentAssessment]
           ([Id]
           ,[StudentLessonLogId]
           ,[AssessmentCriteriaId]
           ,[Score])
SELECT
           LOWER(CONVERT(nvarchar(450), NEWID()))
           ,sll.Id
           ,'8c39ddf2-0ac6-4fe9-9104-85a4f38e4d31'
           ,Cast(FLOOR(RAND()*(3)+1) as int)
FROM StudentLessonLogData sll

INSERT INTO [dbo].[StudentAssessment]
           ([Id]
           ,[StudentLessonLogId]
           ,[AssessmentCriteriaId]
           ,[Score])
SELECT
           LOWER(CONVERT(nvarchar(450), NEWID()))
           ,sll.Id
           ,'b9518bf7-8cb6-41be-866c-01a80eb79a60'
           ,Cast(FLOOR(RAND()*(3)+1) as int)
FROM StudentLessonLogData sll

UPDATE StudentLessonLogData
SET Note='Build stunning cross-platform user interfaces
Xamarin.Forms is a feature of Xamarin, the popular mobile development framework that extends the .NET developer platform with tools and libraries for building mobile apps.

Xamarin.Forms is an open source cross-platform framework from Microsoft for building iOS, Android, & Windows apps with .NET from a single shared codebase.

Use Xamarin.Forms built in pages, layouts, and controls to build and design mobile apps from a single API that is highly extensible. Subclass any control to customize their behavior or define your own controls, layouts, pages, and cells to make your app pixel perfect.'

UPDATE ExamResult
SET TeacherComment='Build stunning cross-platform user interfaces
Xamarin.Forms is a feature of Xamarin, the popular mobile development framework that extends the .NET developer platform with tools and libraries for building mobile apps.

Xamarin.Forms is an open source cross-platform framework from Microsoft for building iOS, Android, & Windows apps with .NET from a single shared codebase.

Use Xamarin.Forms built in pages, layouts, and controls to build and design mobile apps from a single API that is highly extensible. Subclass any control to customize their behavior or define your own controls, layouts, pages, and cells to make your app pixel perfect.'

UPDATE StudentLessonLogData
SET StarScore = Cast(FLOOR(RAND()*(10)+1) as int)
WHERE Id IN 
(
SELECT Id FROM StudentLessonLogData
WHERE 0.01 >= CAST(CHECKSUM(NEWID(),Id) & 0x7fffffff AS float)
              / CAST (0x7fffffff AS int)
--select Id from [StudentLessonLogData] tablesample(1 percent)
)

UPDATE [StudentAssessment]
SET [Score] = Cast(FLOOR(RAND()*(3)+1) as int)
WHERE Id IN 
(
SELECT Id FROM [StudentAssessment]
WHERE 0.01 >= CAST(CHECKSUM(NEWID(),Id) & 0x7fffffff AS float)
              / CAST (0x7fffffff AS int)
)

UPDATE [dbo].[StudentAssessment]
SET [Score] = 
           Cast(FLOOR(RAND()*(3)+1) as int)
WHERE [AssessmentCriteriaId] = 
           '7df76aef-7d80-4aa0-b185-a63061a65176'

UPDATE [dbo].[StudentAssessment]
SET [Score] = 
           Cast(FLOOR(RAND()*(3)+1) as int)
WHERE [AssessmentCriteriaId] = 
           '8c39ddf2-0ac6-4fe9-9104-85a4f38e4d31'

UPDATE [dbo].[StudentAssessment]
SET [Score] = 
           Cast(FLOOR(RAND()*(3)+1) as int)
WHERE [AssessmentCriteriaId] = 
           'b9518bf7-8cb6-41be-866c-01a80eb79a60'

GO


--DECLARE
--	@studentLogId nvarchar(450) = 'ae9fb842-dbcb-4d89-b4a3-4ad25666a3d9',
--	@test1 int = Cast(FLOOR(RAND()*(3)+1) as int),
--	@test2 int = Cast(FLOOR(RAND()*(3)+1) as int),
--	@test3 int = Cast(FLOOR(RAND()*(3)+1) as int);

--select @studentLogId = sll.Id
--from StudentLessonLogData sll
--join TeacherLessonLog tll on sll.LogId = tll.Id and sll.StudentInfoId = '7af5b021-746a-4239-a3b3-d09f1748848d'
--where CONVERT(date, tll.LogDateTime) = '2020-11-15'

--INSERT INTO [dbo].[StudentAssessment]
--           ([Id]
--           ,[StudentLessonLogId]
--           ,[AssessmentCriteriaId]
--           ,[Score])
--     VALUES
--           (LOWER(CONVERT(nvarchar(450), NEWID()))
--           ,@studentLogId
--           ,'7df76aef-7d80-4aa0-b185-a63061a65176'
--           ,@test1)

--INSERT INTO [dbo].[StudentAssessment]
--           ([Id]
--           ,[StudentLessonLogId]
--           ,[AssessmentCriteriaId]
--           ,[Score])
--     VALUES
--           (LOWER(CONVERT(nvarchar(450), NEWID()))
--           ,@studentLogId
--           ,'8c39ddf2-0ac6-4fe9-9104-85a4f38e4d31'
--           ,@test2)

--INSERT INTO [dbo].[StudentAssessment]
--           ([Id]
--           ,[StudentLessonLogId]
--           ,[AssessmentCriteriaId]
--           ,[Score])
--     VALUES
--           (LOWER(CONVERT(nvarchar(450), NEWID()))
--           ,@studentLogId
--           ,'b9518bf7-8cb6-41be-866c-01a80eb79a60'
--           ,@test3)
--GO

