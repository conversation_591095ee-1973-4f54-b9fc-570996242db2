﻿USE [msdb]
GO

/****** Object:  Job [AcademicInfoReminder]    Script Date: 6/21/2021 12:04:01 PM ******/
BEGIN TRANSACTION
DECLARE @ReturnCode INT
SELECT @ReturnCode = 0
/****** Object:  JobCategory [[Uncategorized (Local)]]    Script Date: 6/21/2021 12:04:01 PM ******/
IF NOT EXISTS (SELECT name FROM msdb.dbo.syscategories WHERE name=N'[Uncategorized (Local)]' AND category_class=1)
BEGIN
EXEC @ReturnCode = msdb.dbo.sp_add_category @class=N'JOB', @type=N'LOCAL', @name=N'[Uncategorized (Local)]'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback

END

DECLARE @jobId BINARY(16)
EXEC @ReturnCode =  msdb.dbo.sp_add_job @job_name=N'AcademicInfoReminder', 
		@enabled=1, 
		@notify_level_eventlog=0, 
		@notify_level_email=0, 
		@notify_level_netsend=0, 
		@notify_level_page=0, 
		@delete_level=0, 
		@category_name=N'[Uncategorized (Local)]', 
		@owner_login_name=N'sa', @job_id = @jobId OUTPUT
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [StudentBirthdayReminder]    Script Date: 6/21/2021 12:04:01 PM ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'StudentBirthdayReminder', 
		@step_id=1, 
		@cmdexec_success_code=0, 
		@on_success_action=3, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'DECLARE @TimeCheckBirthday datetime = DATEADD(dd, 7, GETDATE());

INSERT INTO [dbo].[Notifications]
				([Id]
				,[ClassId]
				,[Title]
				,[Content]
				,[StartTime]
				,[EndTime]
				,[CreatedDate]
				,[CreatedBy])
SELECT 
				LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
				,cls.Id
				,N''Sắp đến sinh nhật học sinh '' + IIF(NULLIF(std.EnglishName, '''') IS NULL, '''', std.EnglishName + '', '') + std.StudentName + N'' ('' + FORMAT(std.Birthday, ''dd/MM/yyyy'') + N'') tại lớp '' + cls.[Name]
				,N''Thông báo sinh nhật học sinh''
				,GETDATE()
				,DATEADD(mi, 5, GETDATE())
				,GETDATE()
				,''sysadmin''
FROM ClassStudent cs
JOIN Student std ON cs.StudentId = std.Id AND DAY(std.Birthday) = DAY(@TimeCheckBirthday) AND MONTH(std.Birthday) = MONTH(@TimeCheckBirthday)
JOIN ClassCourse cls ON cs.ClassId = cls.Id
JOIN Schedule sch ON cs.ClassId = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
', 
		@database_name=N'GeeODb', 
		@flags=0
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [PaymentReminder]    Script Date: 6/21/2021 12:04:01 PM ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'PaymentReminder', 
		@step_id=2, 
		@cmdexec_success_code=0, 
		@on_success_action=3, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'--DECLARE @TimeToCheckPayment datetime = DATEADD(dd, 10, GETDATE());

INSERT INTO [dbo].[Notifications]
				([Id]
				,[ClassId]
				,[Title]
				,[Content]
				,[StartTime]
				,[EndTime]
				,[CreatedDate]
				,[CreatedBy])
SELECT 
				LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
				,ClassId
				,N''Học sinh '' + IIF(NULLIF(EnglishName, '''') IS NULL, '''', EnglishName + '', '') + StudentName + N'' tại lớp '' + ClassName + N'' sắp hết gói học phí.''
				,N''Thông báo sắp hết gói học phí.''
				,GETDATE()
				,DATEADD(mi, 5, GETDATE())
				,GETDATE()
				,''sysadmin''
FROM
(
SELECT cls.Id AS ClassId
	  ,cls.[Name] As ClassName
	  ,std.StudentName
	  ,std.EnglishName
	  ,sc.NumberOfSession
	  ,COUNT(cl.Id) AS StudiedSessions
FROM StudentCourse sc 
JOIN ClassStudent cs ON sc.StudentId = cs.StudentId
JOIN ClassLesson cl ON cs.ClassId = cl.ClassId AND cl.EndTime < GETDATE() AND cl.StartTime > CONVERT(date, sc.StartDate)
JOIN Student std ON cs.StudentId = std.Id
JOIN ClassCourse cls ON cs.ClassId = cls.Id
--JOIN Schedule sch ON cs.ClassId = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
WHERE sc.NumberOfSession IS NOT NULL AND sc.NumberOfSession > 0
GROUP BY cls.Id, cls.[Name], std.StudentName, std.EnglishName, sc.NumberOfSession
) t
WHERE t.NumberOfSession - t.StudiedSessions > 0 AND t.NumberOfSession - t.StudiedSessions < 5
', 
		@database_name=N'GeeODb', 
		@flags=0
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [TeacherLessonReminder]    Script Date: 6/21/2021 12:04:01 PM ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'TeacherLessonReminder', 
		@step_id=3, 
		@cmdexec_success_code=0, 
		@on_success_action=3, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'DECLARE @TimeToCheck datetime = DATEADD(dd, 7, GETDATE());

INSERT INTO [dbo].[Notifications]
				([Id]
				,[ClassId]
				,[UserId]
				,[Title]
				,[Content]
				,[StartTime]
				,[EndTime]
				,[CreatedDate]
				,[CreatedBy])
SELECT 
				LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
				,cls.Id AS ClassId
				,usr.Id AS UserId
				,N''Lesson plan buổi '' + lp.Lesson + N'' lớp '' + cls.[Name] + N'' (ngày '' + FORMAT(cl.StartTime, ''dd/MM/yyyy'') + N'') chưa được soạn.''
				,N''Thông báo lesson plan chưa được soạn.''
				,GETDATE()
				,DATEADD(mi, 5, GETDATE())
				,GETDATE()
				,''sysadmin''
FROM LessonPlan lp
JOIN ClassLesson cl ON lp.Id = cl.LessonId 
		AND cl.Id NOT IN (SELECT tlu.ClassLessonId FROM TeacherLessonUnit tlu)
		AND CONVERT(date, cl.StartTime) = CONVERT(date, @TimeToCheck)
JOIN ClassCourse cls ON cl.ClassId = cls.Id
JOIN Schedule sch ON cls.Id = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
JOIN ClassTeacher ct ON cls.Id = ct.ClassId
JOIN AspNetUsers usr ON ct.TeacherId = usr.Id

', 
		@database_name=N'GeeODb', 
		@flags=0
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [StudentAbsenceReminder]    Script Date: 6/21/2021 12:04:01 PM ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'StudentAbsenceReminder', 
		@step_id=4, 
		@cmdexec_success_code=0, 
		@on_success_action=1, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'INSERT INTO [dbo].[Notifications]
						([Id]
						,[ClassId]
						,[Title]
						,[Content]
						,[StartTime]
						,[EndTime]
						,[CreatedDate]
						,[CreatedBy])
SELECT
						 LOWER(CONVERT(nvarchar(450), NEWID()))
						,ClassId
						,N''Ngày '' + LogDate + N'' học sinh '' + StudentName + N'' lớp '' + ClassName + N'' vắng mặt.''
						,N''Thông báo học sinh vắng mặt.''
						,GETDATE()
						,DATEADD(mi, 5, GETDATE())
						,GETDATE()
						,''sysadmin''
FROM
(
	SELECT IIF(NULLIF(std.EnglishName, '''') IS NULL, '''', std.EnglishName + '', '') + std.StudentName AS StudentName,
			cls.Id AS ClassId,
			cls.[Name] AS ClassName,
			FORMAT(tll.LogDateTime, ''dd/MM/yyyy'') AS LogDate,
			sll.Present AS Presence
	FROM Student std WITH(NOLOCK)
	JOIN StudentLessonLogData sll WITH(NOLOCK) ON std.Id = sll.StudentInfoId
	JOIN TeacherLessonLog tll WITH(NOLOCK) ON sll.LogId = tll.Id
	JOIN ClassLesson cl WITH(NOLOCK) ON tll.ClassLessonId = cl.Id
	JOIN ClassCourse cls WITH(NOLOCK) ON cl.ClassId = cls.Id
	WHERE sll.Present = 0 AND CONVERT(date, tll.LogDateTime) = DATEADD(day, -1, CONVERT(date, GETDATE()))
) t
', 
		@database_name=N'GeeODb', 
		@flags=0
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_update_job @job_id = @jobId, @start_step_id = 1
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_add_jobschedule @job_id=@jobId, @name=N'ReminderSchedule', 
		@enabled=1, 
		@freq_type=4, 
		@freq_interval=1, 
		@freq_subday_type=1, 
		@freq_subday_interval=0, 
		@freq_relative_interval=0, 
		@freq_recurrence_factor=0, 
		@active_start_date=20210309, 
		@active_end_date=99991231, 
		@active_start_time=0, 
		@active_end_time=235959, 
		@schedule_uid=N'3284ce52-0f08-4c74-82a4-65db71d28a0e'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_add_jobserver @job_id = @jobId, @server_name = N'(local)'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
COMMIT TRANSACTION
GOTO EndSave
QuitWithRollback:
    IF (@@TRANCOUNT > 0) ROLLBACK TRANSACTION
EndSave:
GO


