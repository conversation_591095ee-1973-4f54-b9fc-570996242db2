import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { AdminPage } from '../ui/page/AdminPage';
import { AdminUtilsActions } from './AdminUtilsConstants';
import ClassLessonSettings from './views/ClassLessonSettings';

const stylesAdminUtils = () => ({});

class AdminUtils extends Component {
  render() {
    let pageTitle = '';
    let content = null;
    switch (this.props.action) {
      case AdminUtilsActions.CheckStudentLog:
        pageTitle = 'Settings';
        content = <ClassLessonSettings />;
        break;
      default:
    }
    return <AdminPage title={pageTitle} content={content} />;
  }
}

AdminUtils.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(stylesAdminUtils)(AdminUtils);
