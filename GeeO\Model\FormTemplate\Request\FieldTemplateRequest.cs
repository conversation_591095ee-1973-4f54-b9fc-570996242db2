using System.Collections.Generic;
using Geeo.Data.Dto.FormTemplate;
using GeeO.Data.Constants;

namespace GeeO.Model.FormTemplate.Request
{
    public class FieldTemplateRequest
    {
        public string Id { get; set; }
        public string FieldName { get; set; }
        public string Description { get; set; }
        public TypeInputEnum TypeInput { get; set; }
        public string InputKey { get; set; }
        public string InputMask { get; set; }
        public string InitialData { get; set; }
        public InitialDataDto InitialDataObj { get; set; }
        public string DefaultDataSource { get; set; }
        public List<DefaultDataSourceDto> DefaultDataSourceObj { get; set; }
        public bool? IsRequired { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? IsHiddenField { get; set; }
        public bool? IsDeactivate { get; set; }
        public string FormTemplateId { get; set; }
    }
}