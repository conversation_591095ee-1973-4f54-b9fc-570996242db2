﻿using System;
using System.Collections.Generic;

namespace GeeO.Data.Dto
{
    public class CampusStudentStatisticsDto
    {
        public int TotalStudents { get; set; }
        public int TotalRegularStudents { get; set; }
        public int TotalDemoStudents { get; set; }
        public int TotalSuspendStudents { get; set; }
        public int TotalTerminateStudents { get; set; }
        public int TotalInStudents { get; set; }
        public int TotalOutStudents { get; set; }
    }

    // TODO: Move this to a separate file
    public class CampusClassStatistic
    {
        public int TotalClasses { get; set; }
        public int TotalFinishedClasses { get; set; }
        public int TotalLessons { get; set; }
        public int TotalDemoSessions { get; set; }
        public int TotalCatchUpSessions { get; set; }
        public List<CampusClassStudents> ClassStudents { get; set; }
    }

    public class CampusClassStudents
    {
        public string Class { get; set; }
        public int NumberOfStudents { get; set; }
    }

    // TODO: Move this to a separate file
    public class CampusLevelStatistic
    {
        public string Level { get; set; }
        public int NumberOfClasses { get; set; }
    }

    public class CampusLevelClasses
    {
        public string Level { get; set; }
        public string Class { get; set; }
    }

    public class LevelClassSchedule
    {
        public string Level { get; set; }
        public string Class { get; set; }
        public DateTime StartDate { get; set; }
    }

    public class LevelClassSeries
    {
        public string Name { get; set; }
        public List<int> Data { get; set; }
    }

    public class StudentSuspendData
    {
        public string StudentId { get; set; }
        public DateTime SuspendDate { get; set; }
    }

    public class StudentSuspendSeries
    {
        public List<int> SuspendData { get; set; }
        public List<int> TerminateData { get; set; }
    }

    public class CampusMonthPayment
    {
        public int Month { get; set; }
        public double Payment { get; set; }
        public double Renews { get; set; }
        public double News { get; set;}
    }

    // TODO: Move this to a separate file
    public class CampusPaymentStatistic
    {
        public List<int> Years { get; set; }
        public List<StudentPaymentStatistics> StudentPayments { get; set; }
        public List<CampusMonthPayment> MonthPayments { get; set; }
        public List<int> Months { get; set; }
    }

    public class RoomSchedule
    {
        public string RoomName { get; set; }
        public DateTime StartTime { get; set; }
        public int Duration { get; set; }
    }

    // TODO: Move this to a separate file
    public class RoomTimeStatistic
    {
        public string RoomName { get; set; }
        public int TotalUsedTime { get; set; }
        public List<int> RoomTimes { get; set; }
    }
}
