﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Net.Mime;
using System.Threading.Tasks;
using System;
using GeeO.Services;
using GeeO.Model;
using GeeO.Data.Dto;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using GeeO.Data.Dto.WorkingTime;
using GeeO.Model.WorkingTime;
using GeeO.Model.Worklog;
using GeeO.Data.Models;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WorkLogController : ControllerBase
    {
        private readonly IWorkLogService _workLogService;
        public WorkLogController(IWorkLogService workLogService)
        {
            _workLogService = workLogService;
        }

        [HttpPost("list")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAll(DateRange dateRange)
        {
            try
            {
                List<WorkLogDto> result = await _workLogService.GetAll(dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            try
            {
                WorkLogDto result = await _workLogService.GetById(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetByLogId(string id)
        {
            try
            {
                List<WorkLogDto> result = await _workLogService.GetByLogId(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("check-exist-event/{id}")]
        public async Task<IActionResult> CheckExistEventByLogId(string id)
        {
            try
            {
                var result = await _workLogService.GetEventByLog(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result != null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Add(WorkLogDto workLogDto)
        {
            try
            {
                WorkLogDto result = await _workLogService.Add(workLogDto);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> Update(WorkLogDto workLogDto)
        {
            try
            {
                bool result = await _workLogService.Update(workLogDto);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                bool result = await _workLogService.Delete(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("shift-options")]
        public async Task<IActionResult> GetShiftTypeOptions()
        {
            try
            {
                var options = await _workLogService.GetShiftTypeOptions();
                return Ok(options);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("shift-options/{classId}")]
        public async Task<IActionResult> GetShiftTypeByLevel(string classId)
        {
            try
            {
                var options = await _workLogService.GetShiftTypeByLevel(classId);
                return Ok(options);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("owner")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAllByOwner([FromBody] GetTimebookRequest timebookRequest)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<TeacherTimebook> result = await _workLogService.GetByOwner(timebookRequest.DateRange.From, timebookRequest.DateRange.To, string.IsNullOrEmpty(timebookRequest.OwnerId) ? userId : timebookRequest.OwnerId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("catchup")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetSingleLessonCatchupByOwner([FromBody] GetTimebookRequest timebookRequest)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<TeacherCatchUpTimebook> result = await _workLogService.GetSingleLessonCatchUpByOwner(timebookRequest.DateRange.From, timebookRequest.DateRange.To, string.IsNullOrEmpty(timebookRequest.OwnerId) ? userId : timebookRequest.OwnerId);

                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("other")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetSingleLessonOtherByOwner([FromBody] GetTimebookRequest timebookRequest)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<TeacherCatchUpTimebook> result = await _workLogService.GetSingleLessonOtherByOwner(timebookRequest.DateRange.From, timebookRequest.DateRange.To, string.IsNullOrEmpty(timebookRequest.OwnerId) ? userId : timebookRequest.OwnerId);

                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("expats")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAllExpat(DateRange dateRange)
        {
            try
            {
                List<ExpatWorkingTime> result = await _workLogService.GetAllExpat(dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("expat-summaries")]
        public async Task<IActionResult> GetAllExpatWorkingSummary(DateRange dateRange)
        {
            try
            {
                ExpatWorkingTimeSummary result = await _workLogService.GetExpatSummary(dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("summary")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetSummaryByOwner([FromBody] GetTimebookRequest timebookRequest)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                TeacherTimebookSummary result = await _workLogService.GetSummaryByOwner(timebookRequest.DateRange.From, timebookRequest.DateRange.To, string.IsNullOrEmpty(timebookRequest.OwnerId) ? userId : timebookRequest.OwnerId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("summaries")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetSummaries(DateRange dateRange)
        {
            try
            {
                TeacherTimebookSummary result = await _workLogService.GetAllSummary(dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("update-status")]
        public async Task<IActionResult> UpdateStatus(UpdateStatusRequest request)
        {
            try
            {
                bool result = await _workLogService.UpdateStatus(request.Id, request.Status);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}
