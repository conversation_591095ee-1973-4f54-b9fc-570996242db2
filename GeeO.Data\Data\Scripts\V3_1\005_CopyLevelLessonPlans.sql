DECLARE @LevelIdSrc nvarchar(450) = 'bd3cf707-0c06-46a1-9d4b-6a30df1be8a7',
		@LevelIdDest nvarchar(450) = '86c5b8d3-f14c-4cea-9bea-dad0e8984c0d'

INSERT INTO [LessonPlan] (
	   [Id]
      ,[LevelId]
      ,[Lesson]
      ,[Subject]
      ,[Content]
      ,[Tb]
      ,[CreatedDate]
      ,[WorksheetFileName]
)
SELECT LOWER(CONVERT(nvarchar(450), NEWID()))
      ,@LevelIdDest
      ,[Lesson]
      ,[Subject]
      ,[Content]
      ,[Tb]
      ,[CreatedDate]
      ,[WorksheetFileName]
  FROM [GeeODb].[dbo].[LessonPlan]
  WHERE LevelId = @LevelIdSrc
  ORDER BY CreatedDate
GO

ResetOrderLessonPlan 8
GO


DECLARE @LessonPlanIdSrc nvarchar(450), @LessonSrc nvarchar(256),
		@LessonPlanIdDest nvarchar(450), @LessonDest nvarchar(256)

DECLARE MY_CURSOR CURSOR 
  LOCAL STATIC READ_ONLY FORWARD_ONLY
FOR 
SELECT lp1.Id AS LessonPlanId1, lp1.[Lesson] AS Lesson1, lp2.Id AS LessonPlanId1, lp2.[Lesson] AS Lesson1
FROM [LessonPlan] lp1
JOIN [LessonPlan] lp2 ON lp1.Lesson = lp2.Lesson
WHERE lp1.LevelId = @LevelIdSrc and lp2.LevelId = @LevelIdDest
ORDER BY lp1.CreatedDate

OPEN MY_CURSOR
FETCH NEXT FROM MY_CURSOR INTO @LessonPlanIdSrc, @LessonSrc, @LessonPlanIdDest, @LessonDest
WHILE @@FETCH_STATUS = 0
BEGIN
	INSERT INTO [LessonPlanUnit] (
		   [Id]
		  ,[LessonPlanId]
		  ,[MaterialId]
		  ,[SortOrder]
		  ,[Time]
		  ,[Procedures]
		  ,[Description]
		  ,[Materials]
		  ,[TeacherActivities]
		  ,[LearningOutcome]
		  ,[Note]
		  ,[Category]
	)
	SELECT LOWER(CONVERT(nvarchar(450), NEWID()))
		  ,@LessonPlanIdDest
		  ,[MaterialId]
		  ,[SortOrder]
		  ,[Time]
		  ,[Procedures]
		  ,[Description]
		  ,[Materials]
		  ,[TeacherActivities]
		  ,[LearningOutcome]
		  ,[Note]
		  ,[Category]
	  FROM [GeeODb].[dbo].[LessonPlanUnit]
	  WHERE LessonPlanId = @LessonPlanIdSrc
	  ORDER BY SortOrder

    FETCH NEXT FROM MY_CURSOR INTO @LessonPlanIdSrc, @LessonSrc, @LessonPlanIdDest, @LessonDest
END
CLOSE MY_CURSOR
DEALLOCATE MY_CURSOR
GO
