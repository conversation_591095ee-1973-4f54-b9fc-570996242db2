﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>9.0</LangVersion>
		<TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
		<TypeScriptToolsVersion>Latest</TypeScriptToolsVersion>
		<IsPackable>false</IsPackable>
		<SpaRoot>ClientApp\</SpaRoot>
		<DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);NU1605</NoWarn>
		<AssemblyName>GeeO</AssemblyName>
		<RootNamespace>GeeO</RootNamespace>
		<UserSecretsId>GeeOReactWebApp</UserSecretsId>
		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<UseRazorSourceGenerator>false</UseRazorSourceGenerator>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="7.0.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.0.0" />
		<PackageReference Include="Azure.Identity" Version="1.2.2" />
		<PackageReference Include="MailKit" Version="4.5.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="6.0.29" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="6.0.29" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="3.1.32" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.29" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="6.0.29" />
		<PackageReference Include="Microsoft.AspNetCore.ApiAuthorization.IdentityServer" Version="3.1.32" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.29" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="3.1.32" />

		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.29" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.29" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.29" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.29">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Microsoft.Graph" Version="4.54.0" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.56.0" />
		<PackageReference Include="Microsoft.Net.Compilers" Version="4.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.16" />
		<PackageReference Include="NSwag.AspNetCore" Version="14.2.0" />
		<PackageReference Include="Select.HtmlToPdf.NetCore" Version="21.1.0" />
		<PackageReference Include="Sentry.AspNetCore" Version="3.33.0" />
		<PackageReference Include="System.Text.Json" Version="8.0.0" />

	</ItemGroup>

	<ItemGroup>
		<!-- Don't publish the SPA source files, but do show them in the project files list -->
		<Content Remove="$(SpaRoot)**" />
		<None Remove="$(SpaRoot)**" />
		<None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="client_id.json" />
	</ItemGroup>

	<ItemGroup>
		<None Include="client_id.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\GeeO.Common\GeeO.Common.csproj" />
		<ProjectReference Include="..\GeeO.Data\GeeO.Data.csproj" />
		<ProjectReference Include="..\GeeO.Services\GeeO.Services.csproj" />
		<ProjectReference Include="..\RazorHtmlEmails.RazorClassLib\RazorHtmlEmails.RazorClassLib.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="certs\GeeOAuth.pfx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="GeeOAuth.pfx">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</None>
		<None Update="localhost.pfx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
		<!-- Ensure Node.js is installed -->
		<Exec Command="node --version" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
		<Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<!-- <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" /> -->
	</Target>

	<Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
		<!-- As part of publishing, ensure the JS resources are freshly built in production mode -->
		<!--<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />-->
		<!-- <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" /> -->

		<!-- Include the newly-built files in the publish output -->
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)build\**" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			</ResolvedFileToPublish>
		</ItemGroup>
	</Target>
</Project>
