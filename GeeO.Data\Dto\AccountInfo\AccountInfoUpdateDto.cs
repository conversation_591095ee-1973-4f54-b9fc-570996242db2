﻿using Microsoft.AspNetCore.Http;
using System;

namespace GeeO.Data.Dto.AccountInfo
{
    public class AccountInfoUpdateDto
    {
        public string Id { get; set; }
        public string RoleId { get; set; }
        public string CampusId { get; set; }
        public string FullName { get; set; }
        public string EnglishName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Gender { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Country { get; set; }
        public string MaritalStatus { get; set; }
        public string PermanentAddress { get; set; }
        public string TemporaryAddress { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone { get; set; }
        public string Relationship { get; set; }
        public string CCCD { get; set; }
        public DateTime? DateOfIssue { get; set; }
        public string PlaceOfIssue { get; set; }
        public IFormFile AvatarImage { get; set; }
        public IFormFile FrontImage { get; set; }
        public IFormFile BackImage { get; set; }
        public string SocialInsuranceNumber { get; set; }
        public DateTime? SocialInsuranceIssueDate { get; set; }
        public string TaxIdentificationNumber { get; set; }
        public DateTime? TaxIssueDate { get; set; }
        public DateTime? JoinAt { get; set; }
        public string EmploymentType { get; set; }
        public IFormFile WorkExperience { get; set; }
        public IFormFile HealthCertificate { get; set; }
        public IFormFile BirthCertificate { get; set; }
        public IFormFile IdCardCopy { get; set; }
        public IFormFile DegreeCertificate { get; set; }
    }
}
