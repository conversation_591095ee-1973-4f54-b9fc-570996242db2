﻿using GeeO.Data;
using GeeO.Data.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class CampusController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public CampusController(GeeODbContext context)
        {
            _context = context;
        }

        [HttpGet("[action]")]
        public async Task<IEnumerable<Campus>> GetCampusList()
        {
            var campuses = from campus in _context.Campus where campus.Name != "HHT"
                           select new Campus
                           {
                               Id = campus.Id,
                               Name = $"Chi Nhánh {campus.Address}".ToUpperInvariant(),
                               Address = campus.FullAddress,
                               Website = campus.Website,
                               Location = campus.Location,
                               Image = campus.Image,
                               IpAddress = campus.IpAddress
                           };
            return await campuses.ToListAsync();
        }
    }
}
