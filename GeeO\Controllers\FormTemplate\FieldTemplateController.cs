using System;
using System.Threading.Tasks;
using GeeO.Data.Dto.FormTemplate;
using GeeO.Model.FormTemplate.Request;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GeeO.Controllers.FormTemplate
{
    [Route("api/[controller]")]
    [ApiController]
    public class FieldTemplateController : ControllerBase
    {
        private readonly IFieldTemplateService _fieldTemplateService;
        public FieldTemplateController(IFieldTemplateService fieldTemplateService)
        {
            _fieldTemplateService = fieldTemplateService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(string formTemplateId, int skip, int take)
        {
            try
            {
                var result = await _fieldTemplateService.GetAll(formTemplateId, skip, take);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var result = await _fieldTemplateService.GetById(id);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });

            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Add([FromBody] FieldTemplateRequest request)
        {
            try
            {
                var fieldTemplate = new FieldTemplateDto
                {
                    Id = request.Id,
                    FieldName = request.FieldName,
                    Description = request.Description,
                    TypeInput = request.TypeInput,
                    InputKey = request.InputKey,
                    InputMask = request.InputMask,
                    InitialData = request.InitialData,
                    InitialDataObj = request.InitialDataObj,
                    DefaultDataSource = request.DefaultDataSource,
                    DefaultDataSourceObj = request.DefaultDataSourceObj,
                    IsRequired = request.IsRequired,
                    DisplayOrder = request.DisplayOrder,
                    IsHiddenField = request.IsHiddenField,
                    IsDeactivate = request.IsDeactivate,
                    FormTemplateId = request.FormTemplateId
                };
                var id = await _fieldTemplateService.Add(fieldTemplate);
                var result = ((OkObjectResult)await GetById(id)).Value;
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] FieldTemplateRequest request)
        {
            try
            {
                var fieldTemplate = new FieldTemplateDto
                {
                    Id = request.Id,
                    FieldName = request.FieldName,
                    Description = request.Description,
                    TypeInput = request.TypeInput,
                    InputKey = request.InputKey,
                    InputMask = request.InputMask,
                    InitialData = request.InitialData,
                    InitialDataObj = request.InitialDataObj,
                    DefaultDataSource = request.DefaultDataSource,
                    DefaultDataSourceObj = request.DefaultDataSourceObj,
                    IsRequired = request.IsRequired,
                    DisplayOrder = request.DisplayOrder,
                    IsHiddenField = request.IsHiddenField,
                    IsDeactivate = request.IsDeactivate,
                    FormTemplateId = request.FormTemplateId
                };
                var result = await _fieldTemplateService.Update(fieldTemplate);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}