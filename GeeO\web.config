﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- To customize the asp.net core module uncomment and edit the following section. 
  For more info see https://go.microsoft.com/fwlink/?linkid=838655 -->
  <!--
  <system.webServer>
    <handlers>
      <remove name="aspNetCore"/>
      <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModule" resourceType="Unspecified"/>
    </handlers>
    <aspNetCore processPath="%LAUNCHER_PATH%" arguments="%LAUNCHER_ARGS%" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" />
  </system.webServer>
  -->
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxQueryString="32768" maxAllowedContentLength="1073741824" />
      </requestFiltering>
    </security>
    <!--<httpProtocol>
      <customHeaders>
        <add name="Content-Security-Policy" value="default-src 'self';" />
      </customHeaders>
    </httpProtocol>-->
    <handlers>
      <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
    </handlers>
    <aspNetCore processPath="dotnet" arguments=".\GeeO.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
  </system.webServer>
  <system.web>
    <httpRuntime maxQueryStringLength="32768" maxUrlLength="65536" />
  </system.web>
  <system.net>
    <mailSettings>
      <smtp deliveryMethod="Network">
        <network clientDomain="acad.gee-o.edu.vn" defaultCredentials="true" enableSsl="true" host="smtp.gmail.com" port="587" userName="string" password="string" />
      </smtp>
    </mailSettings>
  </system.net>
</configuration>
<!--ProjectGuid: d1fab9b7-29f1-4076-8899-9a5bbfb727d5-->