USE [GeeODb]
GO

/****** Object:  Table [dbo].[TeacherLessonLogData]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TeacherLessonLogData](
	[Id] [nvarchar](450) NOT NULL,
	[LogId] [nvarchar](450) NOT NULL,
	[UnitId] [nvarchar](450) NOT NULL,
	[Duration] [int] NULL,
	[Note] [nvarchar](1024) NULL,
 CONSTRAINT [PK_TeacherLessonLogData] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TeacherLessonLogData]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLogData_LogId] FOREIGN KEY([LogId])
REFERENCES [dbo].[TeacherLessonLog] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonLogData] CHECK CONSTRAINT [FK_TeacherLessonLogData_LogId]
GO

ALTER TABLE [dbo].[TeacherLessonLogData]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLogData_UnitId] FOREIGN KEY([UnitId])
REFERENCES [dbo].[LessonPlanUnit] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonLogData] CHECK CONSTRAINT [FK_TeacherLessonLogData_UnitId]
GO


