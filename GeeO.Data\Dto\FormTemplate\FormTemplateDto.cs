using System;
using System.Collections.Generic;
using GeeO.Data.Constants;

namespace Geeo.Data.Dto.FormTemplate
{
    public class FormTemplateDto
    {
        public string CollectionId { get; set; }
        public int Skip { get; set; }
        public int Take { get; set; }
        public bool IsFilter { get; set; }
    }

    public class InitialDataDto
    {

    }

    public class DefaultDataSourceDto
    {

    }

    public class FormTemplateAllDto
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string FormCollectionId { get; set; }
        public string FormCollectionName { get; set; }
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public int ExpirationCycle { get; set; }
        public string Description { get; set; }
        public bool Deactivate { get; set; }
        public int FormStatus { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public DateTime? ExpiratedDate { get; set; }
        public List<FormTemplateCampusDto> Campus { get; set; }
        public List<FormTemplateFieldDto> Fields { get; set; }
        public List<FormTemplateWatcherDto> Watchers { get; set; }
        public List<FormTemplateGroupDto> Groups { get; set; }
        public string TemplateType { get; set; }
        public bool IsPublish { get; set; }
    }

    public class FormTemplateFieldDto
    {
        public string Id { get; set; }
        public string FormTemplateId { get; set; }
        public string FieldTemplateId { get; set; }
        public string FieldName { get; set; }
        public TypeInputEnum TypeInput { get; set; }
        public string InputKey { get; set; }
        public string InputMask { get; set; }
        public string InitialData { get; set; }
        public InitialDataDto? InitialDataObj { get; set; }
        public string DefaultDataSource { get; set; }
        public List<DefaultDataSourceDto>? DefaultDataSourceObj { get; set; }
        public bool? IsRequired { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? IsHiddenField { get; set; }
        public bool? IsDeactivate { get; set; }
        public int FormStatus { get; set; }
    }

    public class FormTemplateWatcherDto
    {
        public string Id { get; set; }
        public string FormTemplateId { get; set; }
        public string UserId { get; set; }
        public string FullName { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
    }

    public class FormTemplateGroupDto
    {
        public string Id { get; set; }
        public string FormTemplateId { get; set; }
        public string GroupId { get; set; }
        public string GroupName { get; set; }
    }

    public class FormTemplateCampusDto
    {
        public string Id { get; set; }
        public string CampusId { get; set; }
        public string CampusName { get; set; }
        public string FormTemplateId { get; set; }
    }

    public class FormTemplateProcessDto
    {
        public string Id { get; set; }
        public string FormTemplateId { get; set; }
        public string ProcessName { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public int? ProcessOrder { get; set; }
        public int? ProcessStatus { get; set; }
        public int? ProcessAction { get; set; }
        public bool IsDeactivate { get; set; }
        public int? ExpirationCycle { get; set; }
        public DateTime? ExpiratedDate { get; set; }
        public int? TypeApprovalId { get; set; }
        public string NextProcessIfDeniedId { get; set; }
        public string WorkLogTemplateId { get; set; }
        public List<FormTemplateProcessApproverDto> Approvers { get; set; }
        public List<FormTemplateProcessTriggerDto> Triggers { get; set; }
    }

    public class FormTemplateProcessApproverDto
    {
        public string Id { get; set; }
        public string FormTemplateProcessId { get; set; }
        public string RoleId { get; set; }
        public string GroupId { get; set; }
        public string GroupName { get; set; }
        public string Name { get; set; }
        public string UserId { get; set; }
        public string FullName { get; set; }
        public string UserName { get; set; }
    }

    public class FormTemplateProcessTriggerDto
    {
        public string Id { get; set; }
        public string FormTemplateProcessId { get; set; }
        public int ProcessAction { get; set; }
        public string TriggerName { get; set; }
        public string ApiUrl { get; set; }
        public string HttpMethod { get; set; }
        public string ContentType { get; set; }
        public string Payload { get; set; }
        public string Description { get; set; }
        public bool IsConditionTrigger { get; set; }
        public List<Dictionary<string, string>>? ConditionTriggers { get; set; }
        public List<Dictionary<string, string>>? Parameters { get; set; }
        public List<Dictionary<string, string>>? Headers { get; set; }

    }
}