using System.Collections.Generic;
using System.Threading.Tasks;
using GeeO.Api.Models;

namespace GeeO.Api.Hubs.Clients
{
    public interface ILiveClassClient
    {
        public Task GetOtherUsersInClass(List<LiveClassUser> otherUsers);
        public Task UserJoined(LiveClassUser loginUser);
        public Task UserLeaved(LiveClassUser loginUser);
        public Task CallRemoteLearning();
        public Task CallSelfLearning();
    }
}