﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Services;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CatchUpSchedulesController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly ISmsApiService _smsApiService;

        public CatchUpSchedulesController(GeeODbContext context, ISmsApiService smsApiService)
        {
            _context = context;
            _smsApiService = smsApiService;
        }

        // GET: api/CatchUpSchedules
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CatchUpSchedules>>> GetCatchUpSchedules()
        {
            return await _context.CatchUpSchedules.OrderByDescending(x => x.CreateDate).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/CatchUpSchedules/5
        [HttpGet("{id}")]
        public async Task<ActionResult<CatchUpSchedules>> GetCatchUpSchedules(string id)
        {
            var catchUpSchedules = await _context.CatchUpSchedules.FindAsync(id);

            if (catchUpSchedules == null)
            {
                return NotFound();
            }
            if (!string.IsNullOrEmpty(catchUpSchedules.NewClassId))
            {
                await ChangeClass(catchUpSchedules.StudentId, catchUpSchedules.NewClassId).ConfigureAwait(false);
            }
            return catchUpSchedules;
        }

        // PUT : api/CatchUpSchedules/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutCatchUpSchedules(string id, CatchUpSchedules catchUpSchedules)
        {
            if (id != catchUpSchedules.Id)
            {
                return BadRequest();
            }
            _context.Entry(catchUpSchedules).State = EntityState.Modified;
            if (!string.IsNullOrEmpty(catchUpSchedules.NewClassId))
            {
                await ChangeClass(catchUpSchedules.StudentId, catchUpSchedules.NewClassId).ConfigureAwait(false);
            }
            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CatchUpSchedulesExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        private async Task ChangeClass(string studentId, string classId)
        {
            var checkExist = await _context.ClassStudent.FirstOrDefaultAsync(x => x.ClassId == classId && x.StudentId == studentId && x.StudentType == ClassType.CatchUp).ConfigureAwait(false);
            var maxSortOrder = await _context.ClassStudent.Where(x => x.ClassId == classId).MaxAsync(x => x.SortOrder).ConfigureAwait(false);
            if (checkExist == null)
            {
                ClassStudent classStudent = new ClassStudent()
                {
                    StudentId = studentId,
                    ClassId = classId,
                    SortOrder = ++maxSortOrder ?? 1,
                    StudentType = ClassType.CatchUp,
                    RegisterDate = DateTime.Now,
                };
                _context.ClassStudent.Add(classStudent);
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
        }

        // POST: api/CatchUpSchedules
        [HttpPost]
        public async Task<ActionResult<CatchUpSchedules>> PostCatchUpSchedules(CatchUpSchedules catchUpSchedules)
        {
            if (catchUpSchedules == null)
            {
                return BadRequest();
            }

            catchUpSchedules.CreateDate = DateTime.Now;
            if (string.IsNullOrEmpty(catchUpSchedules.ClassLessonId))
                catchUpSchedules.ClassLessonId = null;
            _context.CatchUpSchedules.Add(catchUpSchedules);

            await _context.SaveChangesAsync().ConfigureAwait(false);

            await _smsApiService.SendCatchUpSchedules(catchUpSchedules);

            return CreatedAtAction("GetCatchUpSchedules", new { id = catchUpSchedules.Id }, catchUpSchedules);
        }

        // DELETE: api/CatchUpSchedules/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<CatchUpSchedules>> DeleteCatchUpSchedules(string id)
        {
            var catchUpSchedules = await _context.CatchUpSchedules.FindAsync(id);
            if (catchUpSchedules == null)
            {
                return NotFound();
            }

            _context.CatchUpSchedules.Remove(catchUpSchedules);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return catchUpSchedules;
        }

        private bool CatchUpSchedulesExists(string id)
        {
            return _context.CatchUpSchedules.Any(e => e.Id == id);
        }

        // GET: api/CatchUpSchedules/5
        [HttpGet("[action]/{id}/{classId}")]
        public async Task<ActionResult<CatchUpSchedules>> GetCatchUpSchedulesByTeacher(string id, string classId)
        {
            var model = await _context.CatchUpSchedules.FindAsync(id);

            if (model == null)
            {
                return NotFound();
            }

            return model;
        }

        // GET: api/CatchUpSchedules/5
        [HttpGet("[action]/{id}/{classId}")]
        public async Task<ActionResult<CatchUpSchedules>> GetScheduleByTeacherAndClass(string id, string classId)
        {
            var model = await _context.CatchUpSchedules.FindAsync(id);

            if (model == null)
            {
                return NotFound();
            }

            return model;
        }

        // GET: api/CatchUpSchedules/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<List<CatchUpScheduleGrid>>> GetScheduleByTeacher(string id)
        {
            List<CatchUpScheduleGrid> data = await (from c in _context.CatchUpSchedules
                                                     join s in _context.Student on c.StudentId equals s.Id into ljS
                                                     from _s in ljS.DefaultIfEmpty()
                                                     join cl in _context.ClassLesson on c.ClassLessonId equals cl.Id into ljCl
                                                     from _cl in ljCl.DefaultIfEmpty()
                                                     join l in _context.LessonPlan on _cl.LessonId equals l.Id into ljL
                                                     from _l in ljL.DefaultIfEmpty()
                                                     join le in _context.StudyLevel on _l.LevelId equals le.Id into ljLe
                                                     from _le in ljLe.DefaultIfEmpty()
                                                     join cc in _context.ClassCourse on _cl.ClassId equals cc.Id into ljCc
                                                     from _cc in ljCc.DefaultIfEmpty()
                                                     where c.TeacherId == id
                                                     orderby c.CreateDate descending
                                                     select new CatchUpScheduleGrid()
                                                     {
                                                         Id = c.Id,
                                                         Name = c.Name,
                                                         Type = c.CatchUpType,
                                                         StartTime = _cl.StartTime,
                                                         Lesson = _l.Lesson,
                                                         Level = _le.Name,
                                                         Class = _cc.Name,
                                                         StudentName = _s.StudentName,
                                                         CatchUpTime = c.CatchUpTime,
                                                         StartDateOfTheWeek = c.StartDate,
                                                         StartTimeOfTheWeek = c.StartTime
                                                     }).ToListAsync().ConfigureAwait(false);
            return data;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudentCatchUpByTeacherId(string id)
        {
            var data = await (from s in _context.Student.Where(x => x.SuspendDate == null)
                              join cs in _context.ClassStudent on s.Id equals cs.StudentId
                              join cc in _context.ClassCourse on cs.ClassId equals cc.Id
                              join slld in _context.StudentLessonLogData on s.Id equals slld.StudentInfoId
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId
                              where ct.TeacherId == id && slld.Present == 0
                              select new StudentGrid()
                              {
                                  Id = s.Id,
                                  StudentName = s.StudentName,
                                  EnglishName = s.EnglishName,
                                  Class = string.Join(", ", s.ClassStudents.Select(x => x.ClassCourse.Name)),
                                  FatherName = s.StudentParents != null ? s.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.Name).FirstOrDefault() : "",
                                  MotherName = s.StudentParents != null ? s.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.Name).FirstOrDefault() : "",
                                  FatherPhone = s.StudentParents != null ? s.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.PhoneNumber).FirstOrDefault() : "",
                                  MotherPhone = s.StudentParents != null ? s.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.PhoneNumber).FirstOrDefault() : "",
                              }).Distinct().ToListAsync().ConfigureAwait(false);
            data.Sort(delegate (StudentGrid x, StudentGrid y)
            {
                if (x.Class == null && y.Class == null) return 0;
                else if (x.Class == null) return -1;
                else if (y.Class == null) return 1;
                else return string.Compare(x.Class, y.Class, StringComparison.CurrentCulture);
            });
            return data;
        }
    }
}