USE [GeeODb]
GO

/****** Object:  Table [dbo].[LessonPlanUnitHistory]    Script Date: 17-Oct-20 3:57:21 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[LessonPlanUnitHistory](
	[Id] [nvarchar](450) NOT NULL,
	[HistoryUnitId] [nvarchar](450) NULL,
	[LessonPlanId] [nvarchar](450) NULL,
	[MaterialId] [nvarchar](450) NULL,
	[SortOrder] [int] NULL,
	[Time] [int] NULL,
	[Procedures] [nvarchar](2048) NULL,
	[Description] [nvarchar](2048) NULL,
	[Materials] [nvarchar](2048) NULL,
	[TeacherActivities] [nvarchar](4000) NULL,
	[LearningOutcome] [nvarchar](4000) NULL,
	[Note] [nvarchar](2048) NULL,
	[Category] [nvarchar](1024) NULL,
	[CreatedAt] [datetime] NULL,
 CONSTRAINT [PK_LessonPlanUnitHistory] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[LessonPlanUnitHistory] ADD  CONSTRAINT [DF_LessonPlanUnitHistory_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[LessonPlanUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_LessonPlanUnitHistory_LessonPlan_LessonPlanId] FOREIGN KEY([LessonPlanId])
REFERENCES [dbo].[LessonPlan] ([Id])
GO

ALTER TABLE [dbo].[LessonPlanUnitHistory] CHECK CONSTRAINT [FK_LessonPlanUnitHistory_LessonPlan_LessonPlanId]
GO

ALTER TABLE [dbo].[LessonPlanUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_LessonPlanUnitHistory_MaterialId] FOREIGN KEY([MaterialId])
REFERENCES [dbo].[Material] ([Id])
GO

ALTER TABLE [dbo].[LessonPlanUnitHistory] CHECK CONSTRAINT [FK_LessonPlanUnitHistory_MaterialId]
GO


