﻿USE [GeeODb]
GO
/****** Object:  Trigger [dbo].[trg_AddStudentCourse]    Script Date: 12/6/2024 10:06:48 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_AddStudentCourse]
	ON [dbo].[StudentCourse]
	AFTER INSERT 
AS
BEGIN 
	SET NOCOUNT ON;
	DECLARE @numberOfSession NVARCHAR(450),
			@classId NVARCHAR(450),
			@className NVARCHAR(450),
			@userId NVARCHAR(450),
			@firstName NVARCHAR(450),
			@lastName NVARCHAR(450),
			@student NVARCHAR(450),
			@startDate NVARCHAR(450),
			@eventTitle NVARCHAR(1000);

	DECLARE @TimeToCheck datetime = SYSDATETIMEOFFSET() AT TIME ZONE 'SE Asia Standard Time';

	  SELECT 
        @classId = sc.ClassId,
        @numberOfSession = sc.NumberOfSession,
		@startDate = IIF(sc.StartDate IS NULL, '', FORMAT(sc.StartDate, 'dd/MM/yyyy')),
        @className = cc.Name,
        @student = s.StudentName,
		@userId = sc.CreatedBy,
		@firstName = asp.FirstName,
		@lastName = asp.LastName
    FROM INSERTED sc WITH(NOLOCK) 
    JOIN dbo.ClassCourse cc WITH(NOLOCK) ON sc.ClassId = cc.Id
    JOIN dbo.Student s WITH(NOLOCK) ON sc.StudentId = s.Id
	JOIN DBO.AspNetUsers asp WITH(NOLOCK) ON sc.CreatedBy = asp.Id

    SET @eventTitle = N'Học sinh ' + @student +  N' lớp ' + @className + N' đã được bổ sung gói học phí ' + @numberOfSession + N' buổi học vào ngày ' + @startDate + N' bởi ' + @lastName + '' + @firstName;

	IF NOT EXISTS (SELECT * FROM [dbo].[Notifications] WHERE [UserId] = @userId AND [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE()))

		INSERT INTO [dbo].[Notifications]
				   ([Id]
				   ,[ClassId]
				   ,[UserId]
				   ,[Title]
				   ,[Content]
				   ,[StartTime]
				   ,[EndTime]
				   ,[CreatedDate]
				   ,[CreatedBy]
				   ,[Activity]
				   ,[Subject])
			 VALUES
				   (LOWER(CONVERT(NVARCHAR(450), NEWID()))
				   ,@classId
				   ,@userId
				   ,@eventTitle
				   ,N'Thông báo Student đã bổ sung gói học phí.'
				   ,@TimeToCheck
				   ,DATEADD(mi, 5, @TimeToCheck)
				   ,@TimeToCheck
				   ,'sysadmin'
				   ,'Add new payment'
				   ,'Student payment');

END;
