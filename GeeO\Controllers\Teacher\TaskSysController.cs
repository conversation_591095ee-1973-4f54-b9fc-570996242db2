﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TaskSysController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public TaskSysController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/TaskSys
        public async Task<ActionResult<IEnumerable<TaskSys>>> GetTaskSys()
        {
            return await _context.TaskSys.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/TaskSys/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TaskSys>> GetTaskSys(string id)
        {
            var taskSys = await _context.TaskSys.Include(x => x.AspNetUsers).Include(x => x.AssignUser).FirstOrDefaultAsync(x=> x.Id == id).ConfigureAwait(false);

            if (taskSys == null)
            {
                return NotFound();
            }
            return taskSys;
        }

        // PUT: api/TaskSys/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPut("{id}")]
        public async Task<IActionResult> PutTaskSys(string id, TaskSys taskSys)
        {
            if (id != taskSys.Id)
            {
                return BadRequest();
            }

            _context.Entry(taskSys).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TaskSysExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/TaskSys
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPost]
        public async Task<ActionResult<TaskSys>> PostTaskSys(TaskSys taskSys)
        {
            taskSys.Status = StatusTaskSys.Waitting;
            if (!taskSys.IsTeacher)
            {
                taskSys.Status = StatusTaskSys.Accepted;
                if (string.IsNullOrEmpty(taskSys.AssignUserId))
                {
                    taskSys.AssignUserId = null;
                }
            }
            else
            {
                taskSys.AssignUserId = null;
            }
            taskSys.CreateDate = DateTime.Now;
            _context.TaskSys.Add(taskSys);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetTaskSys", new { id = taskSys.Id }, taskSys);
        }

        // DELETE: api/TaskSys/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<TaskSys>> DeleteTaskSys(string id)
        {
            var TaskSys = await _context.TaskSys.FindAsync(id);
            if (TaskSys == null)
            {
                return NotFound();
            }

            _context.TaskSys.Remove(TaskSys);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return TaskSys;
        }

        private bool TaskSysExists(string id)
        {
            return _context.TaskSys.Any(e => e.Id == id);
        }

        // GET: api/TaskSys
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<TaskSys>>> GetTaskSysAssignTask(string id)
        {
            return await _context.TaskSys.Include(x => x.ApprovedUser).Include(x => x.AspNetUsers).Include(x => x.AssignUser).Where(x=>x.UserId == id).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/TaskSys
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<TaskSys>>> GetAllTaskSysAssignTask(string id)
        {
            return await _context.TaskSys.Include(x => x.ApprovedUser).Include(x => x.AspNetUsers).Include(x => x.AssignUser).Where(x=> x.UserId != id).OrderByDescending(x=> x.Status).ToListAsync().ConfigureAwait(false);
        }

        // POST: api/TaskSys
        [HttpPost("[action]/{id}/{userId}")]
        public async Task<IActionResult> ApproveTask(string id, string userId)
        {
            TaskSys model = _context.TaskSys.Find(id);
            if (model == null)
            {
                return NotFound();
            }

            if (string.IsNullOrEmpty(model.ApprovedUserId))
            {
                model.ApprovedUserId = userId;
            }
            model.Status = StatusTaskSys.Accepted;
            _context.Entry(model).State = EntityState.Modified;
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return NoContent();
        }

        // GET: api/TaskSys
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<TaskSys>>> GetTaskSysAssigned(string id)
        {
            return await _context.TaskSys.Include(x => x.ApprovedUser).Include(x => x.AspNetUsers).Include(x => x.AssignUser).Where(x=> x.AssignUserId == id).ToListAsync().ConfigureAwait(false);
        }
    }
}
