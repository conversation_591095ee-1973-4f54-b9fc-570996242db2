﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GeeO.Common
{
    public static class StringUtils
    {
        static readonly string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789~!@#$%^&*";
        static readonly Random _random = new();

        public static string GenerateString(int length)
        {
            StringBuilder builder = new(length);

            for (int i = 0; i < length; i++)
                builder.Append(chars[_random.Next(chars.Length)]);

            return builder.ToString();
        }
    }
}
