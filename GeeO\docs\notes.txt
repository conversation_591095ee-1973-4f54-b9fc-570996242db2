IIS AppPool\GeeO


Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\GeeO\Import\LessonPlans\levelE.xls;Extended Properties="EXCEL 8.0;HDR=YES";
Provider=Microsoft.ACE.OLEDB.12.0;Data Source=C:\GeeO\Import\LessonPlans\levelM.xls;Extended Properties="EXCEL 8.0;HDR=YES";
Provider=Microsoft.ACE.OLEDB.12.0;Data Source=C:\..

"Provider=Microsoft.Jet.OLEDB.4.0;Data Source="+@[User::FileName]+";Extended Properties=\"Excel 8.0;HDR=YES\";"

"TestPackage:Error: The requested OLE DB provider Microsoft.Jet.OLEDB.4.0 is not registered. If the 64-bit driver is not installed, run the package in 32-bit mode. Error code: 0x00000000.
An OLE DB record is available.  Source: ""Microsoft OLE DB Service Components""  Hresult: 0x80040154  Description: ""Class not registered"".
"			
