import React, { Component } from 'react';
import clsx from 'clsx';
import { Grid, Paper, TextField, Typography } from '@material-ui/core';
import { PieArcSeries, PieChart } from 'reaviz';

export class SquareChartView extends Component {
  constructor(...args) {
    super(...args);
    this.state = {
      attendPercentage: 50
    };
  }

  handleChange = event => {
    var target = event.target;
    this.setState({ [target.name]: target.value });
    this.props.updateChartData(target.value);
  };

  render() {
    const {
      title,
      subtitle,
      criteriaLabel,
      chartData,
      numberValue,
      classes
    } = this.props;
    const { attendPercentage } = this.state;

    return (
      <Paper className={classes.chartBox}>
        <Grid container>
          <div className={classes.chartHeadingBox}>
            <Typography variant="h3" className={clsx(classes.heading)}>
              {title}
            </Typography>
            <Typography variant="body2" className={clsx(classes.subtitle)}>
              {subtitle}
            </Typography>
          </div>
          <div className={classes.chartNumberBox}>
            <Typography
              variant="h1"
              className={clsx(
                classes.chartSmallNumber,
                classes.chartLabelOrange
              )}
            >
              {numberValue}
            </Typography>
            <hr
              className={clsx(
                classes.chartNumberLine,
                classes.chartSmallNumberLine,
                classes.chartLabelOrange
              )}
            />
          </div>
        </Grid>
        <Grid container style={{ marginTop: 20 }}>
          <Grid item xs={12} style={{ display: 'flex' }}>
            <Typography
              variant="h3"
              className={clsx(classes.subtitle)}
              style={{ marginTop: 8 }}
            >
              {criteriaLabel}
            </Typography>
            <TextField
              style={{
                marginLeft: 8,
                marginRight: 8,
                width: 50,
                '& .MuiInputBase-input': { fontSize: 10 }
              }}
              size="small"
              type="number"
              name="attendPercentage"
              value={attendPercentage}
              onChange={this.handleChange}
            />
            <Typography
              variant="h3"
              className={clsx(classes.subtitle)}
              style={{ marginTop: 8 }}
            >
              {'%'}
            </Typography>
          </Grid>
        </Grid>
        <div className={classes.chartWrap} style={{ height: '200px' }}>
          <div
            style={{
              position: 'relative',
              height: '200px',
              width: '200px',
              alignItems: 'center',
              display: 'flex',
              justifyContent: 'center',
              margin: 'auto'
            }}
          >
            <div style={{ position: 'absolute', top: 0, left: 0 }}>
              <PieChart
                disabled={true}
                width={200}
                height={200}
                data={chartData}
                series={
                  <PieArcSeries
                    doughnut={true}
                    label={null}
                    colorScheme={['#42a5f6', '#ced5e1']}
                  />
                }
              />
            </div>
            <h2 style={{ margin: '0 5px', padding: 0 }}>{chartData[0].data}</h2>
          </div>
        </div>
      </Paper>
    );
  }
}
