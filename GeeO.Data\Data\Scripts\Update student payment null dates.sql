-- update start date
update [StudentCourse] set [StartDate] = case
											when [CreatedDate] is not null then [CreatedDate]
											else DATEADD(ww, -[NumberOfSession] / 2, [EndDate]) 
										 end
where [StartDate] is null and [EndDate] is not null

-- update end date
update [StudentCourse] set [EndDate] = DATEADD(ww, [NumberOfSession] / 2, [StartDate]) 
where [EndDate] is null and [StartDate] is not null

-- update created date
update [StudentCourse] set [CreatedDate] = StartDate where [CreatedDate] is null and StartDate is not null

