using System;
using System.Collections.Generic;
using Geeo.Data.Dto.FormTemplate;
using GeeO.Data.Constants;

namespace GeeO.Data.Dto.RequestForm
{
    public class RequestFormDto
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string FormTemplateId { get; set; }
        public string FormTemplateName { get; set; }
        public DateTime ExpiratedDate { get; set; }
        public int? ExpirationCycle { get; set; }
        public string FormCollectionName { get; set; }
        public string FormCollectionId { get; set; }
        public bool Deactivate { get; set; }
        public int FormStatus { get; set; }
        public string CreatedByName { get; set; }
        public string UserId { get; set; }
        public string Answers { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public List<RequestFormFieldDto> Fields { get; set; }
        public List<RequestFormProcessDto>? Processes { get; set; }
        public List<RequestFormCommunicateDto> Communicates { get; set; }
        public RequestFormHandleWorkFlowDto NextWorkFlow { get; set; }
        public List<RequestFormWatcherDto> Watchers { get; set; }
        public List<RequestFormGroupDto> Groups { get; set; }
    }

    public class RequestFormFieldDto
    {
        public string Id { get; set; }
        public string RequestFormProcessId { get; set; }
        public string FieldTemplateId { get; set; }
        public string FieldName { get; set; }
        public TypeInputEnum TypeInput { get; set; }
        public string InputKey { get; set; }
        public string InputMask { get; set; }
        public string InitialData { get; set; }
        public InitialDataDto? InitialDataObj { get; set; }
        public string DefaultDataSource { get; set; }
        public List<DefaultDataSourceDto>? DefaultDataSourceObj { get; set; }
        public bool? IsRequired { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? IsHiddenField { get; set; }
        public bool? IsDeactivate { get; set; }
        public int FormStatus { get; set; }
    }

    public class RequestFormCommunicateDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string UserId { get; set; }
        public string FullName { get; set; }
        public string RootId { get; set; }
        public string Content { get; set; }
        public List<string> Attachments { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool? IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public RequestFormCommunicateDto Root { get; set; }
    }

    public class RequestFormWatcherDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string UserId { get; set; }
        public string FullName { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
    }

    public class RequestFormGroupDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string GroupId { get; set; }
        public string GroupName { get; set; }
    }

    public class RequestFormHandleWorkFlowDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string RequestFormProcessId { get; set; }
        public int? StageStatus { get; set; }
        public string Note { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class RequestFormProcessDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string? ProcessName { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public int? ProcessOrder { get; set; }
        public int? ProcessStatus { get; set; }
        public int? ProcessAction { get; set; }
        public bool IsDeactivate { get; set; }
        public int? ExpirationCycle { get; set; }
        public DateTime? ExpiratedDate { get; set; }
        public int? TypeApprovalId { get; set; }

        public string NextProcessIfDeniedId { get; set; }
        public string WorkLogTemplateId { get; set; }
        public string Answers { get; set; }
        public string ProcessById { get; set; }
        public string ProcessByName { get; set; }
        public DateTime? ProcessAt { get; set; }
        public string WorkLogById { get; set; }
        public string WorkLogByName { get; set; }
        public DateTime? WorkLogAt { get; set; }
        public bool NeedToDo => !string.IsNullOrEmpty(WorkLogTemplateId);

        public List<RequestFormProcessApproverDto> Approvers { get; set; }
        public List<RequestFormProcessTriggerDto> Triggers { get; set; }
        public List<RequestFormProcessWorkLogFieldDto> Fields { get; set; }
    }

    public class WorkLogTemplateDto
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<RequestFormProcessWorkLogFieldDto> Fields { get; set; }
    }

    public class RequestFormProcessApproverDto
    {
        public string Id { get; set; }
        public string RequestFormProcessId { get; set; }
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public string GroupId { get; set; }
        public string GroupName { get; set; }
        public string UserId { get; set; }
        public string FullName { get; set; }
        public string UserName { get; set; }
    }

    public class RequestFormProcessTriggerDto
    {
        public string Id { get; set; }
        public string RequestFormProcessId { get; set; }
        public int ProcessAction { get; set; }
        public string TriggerName { get; set; }
        public string ApiUrl { get; set; }
        public string HttpMethod { get; set; }
        public string ContentType { get; set; }
        public string Payload { get; set; }
        public string Description { get; set; }
        public bool IsConditionTrigger { get; set; }
        public string ConditionTrigger { get; set; }
    }

    public class RequestFormActivityLogsDto
    {
        public string Id { get; set; }
        public string RequestFormId { get; set; }
        public string UserId { get; set; }
        public int? FormStatus { get; set; }
        public string Note { get; set; }
        public DateTime CreatedDate { get; set; }
        public string FullName { get; set; }
    }

    public class RequestFormProcessWorkLogFieldDto
    {
        public string Id { get; set; }
        public string RequestFormProcessId { get; set; }
        public string FieldName { get; set; }
        public int TypeInput { get; set; }
        public string InputKey { get; set; }
        public string InputMask { get; set; }
        public string InitialData { get; set; }
        public string DefaultDataSource { get; set; }
        public bool? IsRequired { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? IsHiddenField { get; set; }
        public bool? IsDeactivate { get; set; }
        public int FormStatus { get; set; }
    }

    public class WorkLogDto
    {
        public string? Id { get; set; }
        public string? ProcessId { get; set; }
        public string? Answers { get; set; }
        public List<RequestFormFieldDto> Fields { get; set; }
    }

    public class RequestFormProcessTriggerRequestDto
    {
        public string ApiUrl { get; set; }
        public string HttpMethod { get; set; }
        public List<Dictionary<string, string>> Headers { get; set; }
        public List<Dictionary<string, string>> Parameters { get; set; }
        public object Payload { get; set; }
    }
}