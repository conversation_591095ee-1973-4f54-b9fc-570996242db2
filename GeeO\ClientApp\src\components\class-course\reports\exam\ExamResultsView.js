import React from 'react';
import clsx from 'clsx';
import {
  FormControl,
  Grid,
  MenuItem,
  Paper,
  Select,
  Typography
} from '@material-ui/core';
import NormalTable from '../../../ui/table/normal/NormalTable';
import { examTypes } from '../../../academic-manage/views/stylesExamResults';
import { SelectControlViewBase } from './SelectControlViewBase';

export class ExamResultsView extends SelectControlViewBase {
  render() {
    const { title, subtitle, tableDatas, tableCols, classes } = this.props;
    const { examTypeIdx } = this.state;
    const tableData = tableDatas[examTypeIdx];

    return (
      <Paper className={classes.chartBox}>
        <Grid container>
          <div className={classes.chartHeadingBox}>
            <Typography variant="h3" className={classes.heading}>
              {title}
            </Typography>
            <Typography variant="body2" className={classes.subtitle}>
              {subtitle}
            </Typography>
          </div>
          <div className={classes.chartNumberBox}>
            <FormControl>
              <Select
                name="examTypeIdx"
                value={examTypeIdx}
                onChange={this.handleChange}
                className={classes.chartSelect}
              >
                {examTypes.slice(1).map((examType, idx) => {
                  return (
                    <MenuItem
                      value={idx}
                      key={idx}
                      className={classes.chartMenuItem}
                    >
                      {examType}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </div>
        </Grid>
        <div className={clsx(classes.chartWrap)} style={{ height: 'auto' }}>
          <NormalTable data={tableData} cols={tableCols} />
        </div>
      </Paper>
    );
  }
}
