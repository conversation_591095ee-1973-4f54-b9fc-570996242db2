import React, { Component, Fragment } from 'react';
import { Redirect } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Fab from '@material-ui/core/Fab';
import AddIcon from '@material-ui/icons/Add';
import SaveIcon from '@material-ui/icons/Save';
import CloseOutlinedIcon from '@material-ui/icons/CloseOutlined';
import { ListClassRoom } from './ListClassRoom';
import { ClassRoomActions, ClassRoomPaths } from './ClassRoomConstants';
import { AdminPage } from '../../ui/page/AdminPage';
import { EditClassRoom } from './EditClassRoom';

const stylesClassRoom = theme => ({
  fab: {
    margin: theme.spacing(0, 1, 1)
  }
});

class ClassRoomComp extends Component {
  static displayName = ClassRoomComp.name;
  constructor(...args) {
    super(...args);
    this.child = React.createRef();
    this.state = {
      classRoomId: this.props.match.params.classRoomId,
      action: this.props.action,
      content: null,
      pageTitle: '',
      redirect: null
    };
    switch (this.props.action) {
      case ClassRoomActions.List:
        this.state.pageTitle = 'Rooms';
        this.state.content = <ListClassRoom />;
        break;
      case ClassRoomActions.Create:
        this.state.pageTitle = 'Create Room';
        this.state.content = (
          <EditClassRoom
            onRef={actualChild => (this.child = actualChild)}
            action={this.props.action}
            classRoomId={this.state.classRoomId}
          />
        );
        break;
      case ClassRoomActions.Edit:
        this.state.pageTitle = 'Edit Room';
        this.state.content = (
          <EditClassRoom
            onRef={actualChild => (this.child = actualChild)}
            action={this.props.action}
            classRoomId={this.state.classRoomId}
          />
        );
        break;
      case undefined:
      default:
    }
  }

  handleChildSubmit = () => {
    this.child.submitData();
  };

  handleChildBackToList = () => {
    this.child.backToList();
  };

  redirectTo = toUrl => {
    this.setState({
      redirect: <Redirect to={toUrl} />
    });
  };

  render() {
    const { classes } = this.props;
    const actions =
      this.props.action === 'list' ? (
        <Fab
          aria-label="Add"
          color="primary"
          className={classes.fab}
          onClick={() => this.redirectTo(ClassRoomPaths.Create)}
        >
          <AddIcon />
        </Fab>
      ) : (
        <Fragment>
          <Fab
            aria-label="Save"
            color="primary"
            className={classes.fab}
            onClick={this.handleChildSubmit}
          >
            <SaveIcon />
          </Fab>
          <Fab
            aria-label="Cancel"
            className={classes.fab}
            onClick={this.handleChildBackToList}
          >
            <CloseOutlinedIcon color="action" />
          </Fab>
        </Fragment>
      );
    return (
      <Fragment>
        <AdminPage
          title={this.state.pageTitle}
          content={this.state.content}
          actions={actions}
        />
        {this.state.redirect}
      </Fragment>
    );
  }
}

ClassRoomComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const ClassRooms = withStyles(stylesClassRoom)(ClassRoomComp);
