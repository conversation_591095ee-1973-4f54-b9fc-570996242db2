﻿using System;
using System.Net;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;

using MimeKit;
using GeeO.Data;
using GeeO.Services;
using GeeO.Data.Dto;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmailController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IHtmlTemplateService _htmlTemplateService;
        private readonly IEmailService _emailService;

        public EmailController(GeeODbContext context, IHtmlTemplateService htmlTemplateService, IEmailService emailService)
        {
            _context = context;
            _htmlTemplateService = htmlTemplateService;
            _emailService = emailService;
        }

        // POST: api/Email/GetLessonContentTemplate
        [HttpPost("[action]")]
        public async Task<ActionResult> GetLessonContentTemplateAsync(List<string> lessonIds)
        {
            string htmlContent = await _htmlTemplateService.GetLessonContentTemplate(lessonIds).ConfigureAwait(false);

            return new ContentResult
            {
                ContentType = "text/html",
                StatusCode = (int) HttpStatusCode.OK,
                Content = htmlContent
            };
        }

        // POST: api/Email/SendLessonContent
        [HttpPost("[action]")]
        public async Task<ActionResult> SendLessonContent(LessonContentInfo lessonContentInfo)
        {
            if (lessonContentInfo is null)
            {
                throw new ArgumentNullException(nameof(lessonContentInfo));
            }
            //List<string> toAddresses = new List<string>() { "<EMAIL>" };
            await _emailService.SendEmailLessonContent(lessonContentInfo.ToAddresses, lessonContentInfo.LessonIds, lessonContentInfo.ClassName).ConfigureAwait(false);
            return Ok();
        }

    }
}
