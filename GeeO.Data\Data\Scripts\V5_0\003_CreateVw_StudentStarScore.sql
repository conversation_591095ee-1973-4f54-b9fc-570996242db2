﻿USE [GeeODb]
GO

/****** Object:  View [dbo].[View_StudentStarScore]    Script Date: 1/28/2022 2:19:56 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[View_StudentStarScore]
AS
SELECT Id, StudentName, SUM(StarScore) AS 'StarScore'
FROM (
	SELECT std.Id, std.StudentName, ISNULL(sll.StarScore, 0) AS 'StarScore'
	FROM Student std
	LEFT JOIN StudentLessonLogData sll ON std.Id = sll.StudentInfoId
) scores
GROUP BY Id, StudentName
GO
