USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentLessonLogData]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentLessonLogData](
	[Id] [nvarchar](450) NOT NULL,
	[LogId] [nvarchar](450) NOT NULL,
	[StudentInfoId] [nvarchar](450) NOT NULL,
	[Present] [bit] NOT NULL DEFAULT 0,
	[StarScore] [int] NULL,
	[Note] [nvarchar](1024) NULL,
 CONSTRAINT [PK_StudentLessonLogData] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentLessonLogData]  WITH CHECK ADD  CONSTRAINT [FK_StudentLessonLogData_LogId] FOREIGN KEY([LogId])
REFERENCES [dbo].[TeacherLessonLog] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[StudentLessonLogData] CHECK CONSTRAINT [FK_StudentLessonLogData_LogId]
GO

ALTER TABLE [dbo].[StudentLessonLogData]  WITH CHECK ADD  CONSTRAINT [FK_StudentLessonLogData_StudentId] FOREIGN KEY([StudentInfoId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[StudentLessonLogData] CHECK CONSTRAINT [FK_StudentLessonLogData_StudentId]
GO


