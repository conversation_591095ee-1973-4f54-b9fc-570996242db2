﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[Campus]    Script Date: 11/24/2019 12:15:13 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Campus](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](450) NULL,
	[Address] [nvarchar](450) NULL,
 CONSTRAINT [PK_Campus] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Campus]  WITH CHECK ADD  CONSTRAINT [FK_Campus_Campus1] FOREIGN KEY([Id])
REFERENCES [dbo].[Campus] ([Id])
GO

ALTER TABLE [dbo].[Campus] CHECK CONSTRAINT [FK_Campus_Campus1]
GO
