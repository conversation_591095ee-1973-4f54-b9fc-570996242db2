USE [GeeODb]
GO

/****** Object:  Table [dbo].[[WorkingTimes]]    Script Date: 10/10/2023 20:59:09 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WorkingTimes](
	[Id] [nvarchar](450) NOT NULL,
	[StaffId] [nvarchar](450) NOT NULL,
	[CampusId] [nvarchar](450) NOT NULL,
	[CheckInTime] [datetime2](7) NULL,
	[CheckOutTime] [datetime2](7) NULL,
	[WorkHours] [decimal] default 0,
	[WorkType] [int] NULL,
	[Status] [int] NULL,
	[CreatedAt] [datetime2](7) DEFAULT CURRENT_TIMESTAMP,
	[CreatedBy] [nvarchar](450) NULL
 CONSTRAINT [PK_WorkingTime] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WorkingTimes]  WITH CHECK ADD  CONSTRAINT [FK_WorkingTimes_StaffId] FOREIGN KEY([StaffId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[WorkingTimes] CHECK CONSTRAINT [FK_WorkingTimes_StaffId]
GO
