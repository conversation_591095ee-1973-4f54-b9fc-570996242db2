USE [GeeODb]
GO

/****** Object:  Table [dbo].[AcadAnnounce]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[AcadAnnounce](
	[Id] [nvarchar](450) NOT NULL,
	ClassId [nvarchar](450) NULL,
	Content [nvarchar](2000) NULL,
	Latest [bit] NOT NULL DEFAULT 1,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_AcadAnnounce] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[AcadAnnounce]  WITH CHECK ADD  CONSTRAINT [FK_AcadAnnounce_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
GO
ALTER TABLE [dbo].[AcadAnnounce] CHECK CONSTRAINT [FK_AcadAnnounce_ClassId]
GO

