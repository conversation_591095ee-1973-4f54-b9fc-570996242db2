export const SingleLessonActions = {
  List: 'list',
  Edit: 'edit',
  Create: 'create',
  Delete: 'delete',
  Archive: 'archive',
  Clone: 'clone'
};
const prefix = '/single-lesson';
export const SingleLessonPaths = {
  SingleLessonPrefix: prefix,
  List: `${prefix}/${SingleLessonActions.List}`,
  //ListSingleLesson: `${prefix}/${SingleLessonActions.ListSingleLesson}`,
  EditRouter: `${prefix}/${SingleLessonActions.Edit}/:singleLessonId`,
  CloneRouter: `${prefix}/${SingleLessonActions.Clone}/:singleLessonId`,
  Edit: `${prefix}/${SingleLessonActions.Edit}`,
  Clone: `${prefix}/${SingleLessonActions.Clone}`,
  Create: `${prefix}/${SingleLessonActions.Create}`,
  Delete: 'SingleLessons'
};
