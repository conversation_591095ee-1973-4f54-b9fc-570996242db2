import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import PlayCircleOutlineIcon from '@material-ui/icons/PlayCircleOutline';
import DoneIcon from '@material-ui/icons/Done';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import { red, green } from '@material-ui/core/colors';

const stylesRunStatusBox = theme => ({
  statusColorGood: {
    color: green[500]
  },
  statusColorTimeOut: {
    color: red[500]
  },
  statusColorNotStart: {},
  dummy: {
    margin: theme.spacing(1)
  }
});

class RunStatusBox extends Component {
  static displayName = RunStatusBox.name;

  constructor(...args) {
    super(...args);
    this.state = {
      runState: '',
      timeInSeconds: Number(this.props.timeInMinutes) * 60,
      timeElapsed: 0
    };
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  updateRunStatus(runStateNew) {
    this.setState({ runState: runStateNew });
  }
  updateElapsedTime(elapsedTime) {
    this.setState({ timeElapsed: elapsedTime });
  }

  render() {
    const { classes } = this.props;
    let minutes = ~~(this.state.timeElapsed / 60);
    let formattedMinutes = String(minutes).padStart(2, '0');
    //let formattedMinutes = ("0" + minutes).slice(-2);
    let seconds = this.state.timeElapsed % 60;
    let formattedSeconds = String(seconds).padStart(2, '0');
    //let formattedSeconds = ("0" + seconds).slice(-2);
    const timeElapsed = `${formattedMinutes}:${formattedSeconds}`;
    const statusColor =
      this.state.runState === ''
        ? classes.statusColorNotStart
        : this.state.timeElapsed > this.state.timeInSeconds
        ? classes.statusColorTimeOut
        : classes.statusColorGood;

    return (
      <Fragment>
        {this.state.runState === 'run' ? (
          <PlayCircleOutlineIcon className={statusColor} />
        ) : this.state.runState === 'done' ? (
          <DoneIcon className={statusColor} />
        ) : (
          <PlayArrowIcon />
        )}
        <Typography variant="body2" className={statusColor}>
          {timeElapsed}
        </Typography>
      </Fragment>
    );
  }
}

RunStatusBox.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(stylesRunStatusBox)(RunStatusBox);
