USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormProcessApprovers]    Script Date: 3/26/2025 5:43:12 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormProcessApprovers](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormProcessId] [nvarchar](450) NULL,
	[RoleId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[GroupId] [nvarchar](450) NULL,
	[Approver] [bit] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcessApprovers_AspNetRoles] FOREIGN KEY([RoleId])
REFERENCES [dbo].[AspNetRoles] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] CHECK CONSTRAINT [FK_RequestFormProcessApprovers_AspNetRoles]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcessApprovers_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] CHECK CONSTRAINT [FK_RequestFormProcessApprovers_AspNetUsers]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcessApprovers_RequestFormProcess] FOREIGN KEY([RequestFormProcessId])
REFERENCES [dbo].[RequestFormProcess] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] CHECK CONSTRAINT [FK_RequestFormProcessApprovers_RequestFormProcess]
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcessApprovers_RequestGroup] FOREIGN KEY([GroupId])
REFERENCES [dbo].[RequestGroups] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcessApprovers] CHECK CONSTRAINT [FK_RequestFormProcessApprovers_RequestGroup]
GO


