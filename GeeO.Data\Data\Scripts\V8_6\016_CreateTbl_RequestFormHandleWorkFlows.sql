USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormHandleWorkFlows]    Script Date: 3/26/2025 5:41:37 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormHandleWorkFlows](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[RequestFormProcessId] [nvarchar](450) NULL,
	[StageStatus] [int] NULL,
	[Note] [nvarchar](100) NULL,
	[CreatedDate] [datetime] NULL,
	[LastModified] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormHandleWorkFlows_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows] CHECK CONSTRAINT [FK_RequestFormHandleWorkFlows_RequestForm]
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormHandleWorkFlows_RequestFormProcess] FOREIGN KEY([RequestFormProcessId])
REFERENCES [dbo].[RequestFormProcess] ([Id])
GO

ALTER TABLE [dbo].[RequestFormHandleWorkFlows] CHECK CONSTRAINT [FK_RequestFormHandleWorkFlows_RequestFormProcess]
GO


