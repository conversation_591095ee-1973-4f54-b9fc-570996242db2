using System;
using System.Threading.Tasks;
using AutoMapper;
using GeeO.Data.Dto.RequestForm;
using GeeO.Data.Models;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GeeO.Controllers.RequestForm
{
    [Route("api/[controller]")]
    [ApiController]
    public class RequestFormProcessTriggerController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IRequestFormProcessTriggerService _requestFormProcessTriggerService;

        public RequestFormProcessTriggerController(IRequestFormProcessTriggerService requestFormProcessTriggerService, IMapper mapper)
        {
            _requestFormProcessTriggerService = requestFormProcessTriggerService;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<IActionResult> Trigger([FromBody] RequestFormProcessTriggerRequest request)
        {
            try
            {
                var triggerDto = _mapper.Map<RequestFormProcessTriggerRequestDto>(request);
                await _requestFormProcessTriggerService.Trigger(triggerDto);
                return Ok(new { Message = "Trigger executed successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}