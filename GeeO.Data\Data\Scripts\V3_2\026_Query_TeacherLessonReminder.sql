﻿DECLARE @TimeToCheck datetime = DATEADD(dd, 7, GETDATE());

INSERT INTO [dbo].[Notifications]
				([Id]
				,[ClassId]
				,[UserId]
				,[Title]
				,[Content]
				,[StartTime]
				,[EndTime]
				,[CreatedDate]
				,[CreatedBy])
SELECT 
				LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
				,cls.Id AS ClassId
				,usr.Id AS UserId
				,N'Lesson plan buổi ' + lp.Lesson + N' lớp ' + cls.[Name] + N' (ngày ' + FORMAT(cl.StartTime, 'dd/MM/yyyy') + N') chưa được soạn.'
				,N'Thông báo lesson plan chưa được soạn.'
				,GETDATE()
				,DATEADD(mi, 5, GETDATE())
				,GETDATE()
				,'sysadmin'
FROM LessonPlan lp
JOIN ClassLesson cl ON lp.Id = cl.LessonId 
		AND cl.Id NOT IN (SELECT tlu.ClassLessonId FROM TeacherLessonUnit tlu)
		AND CONVERT(date, cl.StartTime) = CONVERT(date, @TimeToCheck)
JOIN ClassCourse cls ON cl.ClassId = cls.Id
JOIN Schedule sch ON cls.Id = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
JOIN ClassTeacher ct ON cls.Id = ct.ClassId
JOIN AspNetUsers usr ON ct.TeacherId = usr.Id

