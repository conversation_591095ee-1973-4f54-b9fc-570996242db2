using IdentityServer4.Models;
using IdentityServer4.Validation;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using GeeO.Models;
using Newtonsoft.Json;

public class ExternalExtensionGrantValidator : IExtensionGrantValidator
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly SignInManager<ApplicationUser> _signInManager;

    public string GrantType => "external_api";

    public ExternalExtensionGrantValidator(
        UserManager<ApplicationUser> userManager,
        RoleManager<IdentityRole> roleManager,
        SignInManager<ApplicationUser> signInManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
        _signInManager = signInManager;
    }

    public async Task ValidateAsync(ExtensionGrantValidationContext context)
    {
        var externalToken = context.Request.Raw.Get("external_token");

        if (string.IsNullOrWhiteSpace(externalToken))
        {
            context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Missing external_token");
            return;
        }

        // Validate token with Google
        var googleUser = await ValidateGoogleTokenAsync(externalToken);
        if (googleUser == null)
        {
            context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Invalid Google token");
            return;
        }

        // Get or create user
        var email = googleUser.Value<string>("email");
        var firstName = googleUser.Value<string>("given_name");
        var lastName = googleUser.Value<string>("family_name");

        var user = await _userManager.FindByNameAsync(email);
        IdentityResult result = null;

        if (user == null)
        {
            var teacherRole = await _roleManager.FindByNameAsync("Teacher");
            user = new ApplicationUser
            {
                UserName = email,
                Email = email,
                FirstName = firstName,
                LastName = lastName,
                RoleId = teacherRole?.Id ?? string.Empty
            };

            result = await _userManager.CreateAsync(user);
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, "Teacher");
            }
        }

        if (result == null || result.Succeeded)
        {
            user.FirstName = firstName;
            user.LastName = lastName;
            await _userManager.UpdateAsync(user);
        }

        var claims = new List<Claim>
        {
            new Claim("name", $"{firstName} {lastName}"),
            new Claim("email", email),
            new Claim("role", "Teacher"),
            new Claim("userId", user.Id)
        };

        context.Result = new GrantValidationResult(
            subject: user.Id,
            authenticationMethod: GrantType,
            claims: claims
        );
    }

    private async Task<dynamic> ValidateGoogleTokenAsync(string token)
    {
        using var client = new HttpClient();
        var result = await client.GetAsync($"https://www.googleapis.com/oauth2/v2/userinfo?access_token={token}");
        if (result.IsSuccessStatusCode)
        {
            var content = await result.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject(content);
        }
        return null;
    }
}
