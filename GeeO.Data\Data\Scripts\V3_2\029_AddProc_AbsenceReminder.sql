﻿IF EXISTS ( SELECT * 
            FROM   sysobjects 
            WHERE  id = object_id(N'[dbo].[sp_StudentAbsenceNotify]') 
                   and OBJECTPROPERTY(id, N'IsProcedure') = 1 )
BEGIN
    DROP PROCEDURE [dbo].[sp_StudentAbsenceNotify];
END
GO


SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_StudentAbsenceNotify]
	@logId nvarchar(450)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DELETE FROM [dbo].[Notifications] 
	WHERE [Content] = N'Thông báo học sinh vắng mặt.'
		AND [ClassId] = (SELECT ClassId FROM ClassLesson WITH(NOLOCK) WHERE Id = (SELECT ClassLessonId FROM TeacherLessonLog WITH(NOLOCK) WHERE Id = @logId))

	INSERT INTO [dbo].[Notifications]
						([Id]
						,[ClassId]
						,[Title]
						,[Content]
						,[StartTime]
						,[EndTime]
						,[CreatedDate]
						,[CreatedBy])
	SELECT
						LOWER(CONVERT(nvarchar(450), NEWID()))
						,cls.Id
						,N'Ngày ' + FORMAT(tll.LogDateTime, 'dd/MM/yyyy') + 
						 N' học sinh ' + IIF(NULLIF(std.EnglishName, '') IS NULL, '', std.EnglishName + ', ') + std.StudentName + 
						 N' lớp ' + cls.[Name] + N' vắng mặt.'
						,N'Thông báo học sinh vắng mặt.'
						,GETDATE()
						,DATEADD(mi, 5, GETDATE())
						,GETDATE()
						,'sysadmin'
	FROM Student std WITH(NOLOCK)
	JOIN StudentLessonLogData sll WITH(NOLOCK) on std.Id = sll.StudentInfoId and sll.Present = 0
	JOIN TeacherLessonLog tll WITH(NOLOCK) on sll.LogId = tll.Id and tll.Id = @logId
	JOIN ClassLesson cl WITH(NOLOCK) on tll.ClassLessonId = cl.Id
	JOIN ClassCourse cls WITH(NOLOCK) on cl.ClassId = cls.Id

END
GO
