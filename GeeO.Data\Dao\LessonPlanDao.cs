﻿using GeeO.Data;
using GeeO.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Dao
{
    public class LessonPlanDao
    {
        protected readonly GeeODbContext _context;

        public LessonPlanDao(GeeODbContext context)
        {
            _context = context;
        }

        public async Task<LessonPlan> GetLessonPlan(string id)
        {
            var LessonPlan = await _context.LessonPlan.FindAsync(id);

            return LessonPlan;
        }

        public async Task<int> UpdateLessonPlan(LessonPlan LessonPlan)
        {
            _context.Entry(LessonPlan).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LessonPlanExists(LessonPlan.Id))
                {
                    return 0;
                }
                else
                {
                    throw;
                }
            }

            return 0;
        }

        private bool LessonPlanExists(string id)
        {
            return _context.LessonPlan.Any(e => e.Id == id);
        }
    }
}
