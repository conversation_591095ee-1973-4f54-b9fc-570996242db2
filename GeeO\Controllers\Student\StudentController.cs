﻿using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Data.Dto.SingleLesson;
using GeeO.Data.Extensions;
using GeeO.GridVo;
using GeeO.Model;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class StudentController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IAcadManageService _acadManageService;
        private readonly IAzureAdService _azureAdService;

        public StudentController(GeeODbContext context,
            UserManager<ApplicationUser> userManager,
            IAcadManageService acadManageService,
            IAzureAdService azureAdService)
        {
            _context = context;
            _userManager = userManager;
            _acadManageService = acadManageService;
            _azureAdService = azureAdService;
        }

        // GET: api/Student/GetDemoStudents
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetDemoStudents(string userId)
        {
            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = from std in _context.Student
                         from cs in _context.ClassStudent.Where(cs => std.Id == cs.StudentId && cs.StudentType == ClassType.Demo)
                         from cls in _context.ClassCourse.Where(cls => cs.ClassId == cls.Id && (assignedCampus == null || assignedCampus.Contains(cls.CampusId)))
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                         orderby std.BeginDate descending
                         select new StudentGrid
                         {
                             Id = std.Id,
                             Class = cls.Name,
                             StudentName = std.StudentName,
                             EnglishName = std.EnglishName,
                             Birthday = std.Birthday,
                             FatherName = std.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.Name).FirstOrDefault(),
                             FatherPhone = std.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.PhoneNumber).FirstOrDefault(),
                             MotherName = std.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.Name).FirstOrDefault(),
                             MotherPhone = std.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.PhoneNumber).FirstOrDefault(),
                             BeginDate = std.BeginDate,
                             Address = std.Address
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET': api/Student
        [HttpGet]
        public async Task<ActionResult<IEnumerable<StudentGrid>>> GetStudent()
        {
            return await _context.Student.Where(x => x.SuspendDate == null).Select(x => new StudentGrid()
            {
                Id = x.Id,
                StudentName = x.StudentName,
                EnglishName = x.EnglishName,
                Birthday = x.Birthday,
                FatherName = x.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.Name).FirstOrDefault(),
                MotherName = x.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.Name).FirstOrDefault(),
                FatherPhone = x.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.PhoneNumber).FirstOrDefault(),
                MotherPhone = x.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.PhoneNumber).FirstOrDefault(),
                BeginDate = x.BeginDate,
                Address = x.Address
            }).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/Student/GetByClass/5
        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Student>>> GetByClass(string classId)
        {
            return await _context.ClassStudent
                .Where(x => x.ClassId == classId && x.Student.SuspendDate == null)
                .Include(x => x.ClassCourse)
                .OrderBy(x => x.SortOrder)
                .Select(x => new Student
                {
                    Id = x.StudentId,
                    StudentName = x.Student.StudentName,
                    EnglishName = x.Student.EnglishName,
                    BeginDate = x.Student.BeginDate,
                    RenewCourse = x.Student.RenewCourse,
                    Birthday = x.Student.Birthday
                }).ToListAsync().ConfigureAwait(false);
        }

        // GET: api/Student/GetClassList/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<ClassView>>> GetClassList(string id)
        {
            var result = from cs in _context.ClassStudent.Where(cs => cs.StudentId == id)
                         from cls in _context.ClassCourse.Where(cls => cs.ClassId == cls.Id)
                             //from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId)
                             //orderby sch.StartDate descending
                         select new ClassView()
                         {
                             ClassStudentId = cs.Id,
                             StudentId = cs.StudentId,
                             ClassId = cs.ClassId,
                             ClassName = cls.Name,
                             SortOrder = cs.SortOrder.Value,
                             StudentType = cs.StudentType
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/Student/GetClass/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<ClassView>> GetClass(string id)
        {
            var result = from cs in _context.ClassStudent.Where(cs => cs.StudentId == id & cs.StudentType == ClassType.Regular)
                         from cls in _context.ClassCourse.Where(cls => cs.ClassId == cls.Id)
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                         select new ClassView()
                         {
                             ClassStudentId = cs.Id,
                             StudentId = cs.StudentId,
                             ClassId = cs.ClassId,
                             ClassName = cls.Name,
                             SortOrder = cs.SortOrder.Value
                         };

            return await result.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        // GET: api/Student/GetClass/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<ClassView>> GetRegularClass(string id)
        {
            var classStudent = _context.ClassStudent.ToList().LastOrDefault(cs => cs.StudentId == id && cs.StudentType == ClassType.Regular && cs.ClassId != null);
            if (classStudent == null)
            {
                return NotFound();
            }

            var currentClass = await _context.ClassCourse.FirstOrDefaultAsync(cls => classStudent.ClassId == cls.Id);
            var stdClass = new ClassView()
            {
                ClassStudentId = classStudent.Id,
                StudentId = classStudent.StudentId,
                ClassId = classStudent.ClassId,
                ClassName = currentClass.Name,
                SortOrder = classStudent.SortOrder,
            };

            return stdClass;
        }

        // GET: api/Student/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Student>> GetStudent(string id)
        {
            var student = await _context.Student
                .Where(u => u.Id == id)
                .Include(x => x.ClassStudents)
                    .ThenInclude(x => x.ClassCourse)
                .FirstAsync().ConfigureAwait(false);
            if (student == null)
            {
                return NotFound();
            }

            var elAct = await _context.StudentExternalAccount.Where(u => u.StudentId == id).FirstOrDefaultAsync().ConfigureAwait(false);
            student.ElAccount = elAct?.Email ?? string.Empty;

            return student;
        }

        // PUT: api/Student/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutStudent(string id, Student student)
        {
            if (id != student.Id)
            {
                return BadRequest();
            }

            _context.Entry(student).State = EntityState.Modified;

            var elAccount = await _context.StudentExternalAccount.Where(u => u.StudentId == id).FirstOrDefaultAsync().ConfigureAwait(false);
            if (elAccount == null)
            {
                elAccount = new()
                {
                    StudentId = id,
                    AuthenticationScheme = "Microsoft",
                    Email = student.ElAccount,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "sysadmin"
                };
                _context.StudentExternalAccount.Add(elAccount);
            }
            else
            {
                elAccount.Email = student.ElAccount;
                elAccount.ModifiedDate = DateTime.Now;
                _context.Entry(elAccount).State = EntityState.Modified;
            }

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StudentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Student
        [HttpPost]
        public async Task<ActionResult<Student>> PostStudent(Student student)
        {
            _context.Student.Add(student);

            if (string.IsNullOrEmpty(student.ElAccount))
            {
                string newElAccount = _azureAdService.CreateNewElAccount(student.StudentName);
                if (!string.IsNullOrEmpty(newElAccount))
                {
                    _azureAdService.AssignLicenseForNewUser(newElAccount);
                    student.ElAccount = newElAccount;
                    //send email...
                }
            }
            StudentExternalAccount elAccount = new()
            {
                StudentId = student.Id,
                AuthenticationScheme = "Microsoft",
                Email = student.ElAccount ?? string.Empty,
                CreatedDate = DateTime.Now,
                CreatedBy = "sysadmin"
            };
            _context.StudentExternalAccount.Add(elAccount);

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return CreatedAtAction("GetStudent", new { id = student.Id }, student);
        }

        [HttpPost("[action]/{userId}")]
        public async Task<ActionResult<Student>> PostStudentClassProposal(string userId, StudentProposal pram)
        {
            if (pram == null)
            {
                return NotFound();
            }

            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            Student student = pram.Student;
            List<Parent> parents = pram.Parents;

            // Create student
            _context.Student.Add(student);

            // Create parents
            foreach (var parent in parents)
            {
                if (!string.IsNullOrEmpty(parent.Name))
                {
                    _context.Parent.Add(parent);
                    _context.StudentParent.Add(new StudentParent() { ParentId = parent.Id, StudentId = student.Id });
                }
            }

            // Save course info
            StudentCourse studentCourse = pram.StudentCourse;

            var classStudent = await _context.ClassStudent
                                                .FirstOrDefaultAsync(x => x.ClassId == studentCourse.ClassId && x.StudentId == studentCourse.StudentId);

            if (classStudent == null)
            {
                var sortOrder = _context.ClassStudent.Count(x => x.ClassId == studentCourse.ClassId && x.Student.SuspendDate == null);
                classStudent = new()
                {
                    StudentId = student.Id,
                    ClassId = studentCourse.ClassId,
                    SortOrder = sortOrder + 1,
                    StudentType = studentCourse.StudentType
                };
                _context.ClassStudent.Add(classStudent);
            }

            StudentCourse newStudentCourse = new()
            {
                StudentId = classStudent.StudentId,
                Name = studentCourse.Name,
                StartDate = studentCourse.StartDate,
                EndDate = studentCourse.EndDate,
                PaymentType = studentCourse.PaymentType,
                Amount = studentCourse.Amount,
                NumberOfSession = studentCourse.NumberOfSession,
                CreatedDate = DateTime.Now,
                CreatedBy = userId
            };
            _context.StudentCourse.Add(newStudentCourse);

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return student;
        }

        [HttpPut("[action]/{userId}")]
        public async Task<ActionResult<Student>> PutStudentClassProposal(string userId, StudentProposal pram)
        {
            if (pram == null)
            {
                return NotFound();
            }

            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            Student student = pram.Student;
            List<Parent> parents = pram.Parents;

            // Save student
            _context.Entry(student).State = EntityState.Modified;

            // Save parents
            foreach (var parent in parents)
            {
                if (string.IsNullOrEmpty(parent.Id))
                {
                    if (!string.IsNullOrEmpty(parent.Name))
                    {
                        _context.Parent.Add(parent);
                        _context.StudentParent.Add(new StudentParent() { ParentId = parent.Id, StudentId = student.Id });
                    }
                }
                else
                {
                    _context.Entry(parent).State = EntityState.Modified;
                }
            }

            // Save course info
            StudentCourse studentCourse = pram.StudentCourse;
            StudentCourse currentPayment = await _context.StudentCourse.AsNoTracking().FirstOrDefaultAsync(x => x.Id == studentCourse.Id).ConfigureAwait(false);

            if (currentPayment == null)
            {
                return NotFound();
            }

            studentCourse.ModifiedDate = DateTime.Now;
            studentCourse.ModifiedBy = userId;

            _context.Entry(studentCourse).State = EntityState.Modified;

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return student;
        }

        // DELETE: api/Student/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<Student>> DeleteStudent(string id)
        {
            var Student = await _context.Student.FindAsync(id);
            if (Student == null)
            {
                return NotFound();
            }

            _context.Student.Remove(Student);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Student;
        }

        private bool StudentExists(string id)
        {
            return _context.Student.Any(e => e.Id == id);
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<StudentLearningOutcome>>> GetStudentViewLearningOutcomes(string id)
        {
            var result = await (from slld in _context.StudentLessonLogData
                                join tll in _context.TeacherLessonLog on slld.LogId equals tll.Id
                                join cl in _context.ClassLesson on tll.ClassLessonId equals cl.Id
                                join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                join l in _context.LessonPlan on cl.LessonId equals l.Id
                                where slld.StudentInfoId == id
                                select new StudentLearningOutcome()
                                {
                                    Id = slld.Id,
                                    Lesson = l.Lesson,
                                    Subject = l.Subject,
                                    Present = slld.Present,
                                    StarScore = slld.StarScore,
                                    Class = cc.Name,
                                    Level = cc.Level.Name,
                                    LogDateTime = tll.LogDateTime.ToString("MM/dd/yyyy hh:mm tt", CultureInfo.InvariantCulture)
                                }).ToListAsync().ConfigureAwait(false);
            return result;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<StudentGrid>> GetInfoStudent(string id)
        {
            var result = await _context.Student.Where(x => x.Id == id && x.SuspendDate == null).Select(x => new StudentGrid()
            {
                Id = x.Id,
                StudentName = x.StudentName,
                EnglishName = x.EnglishName,
                Birthday = x.Birthday,
                FatherName = x.StudentParents.Where(o => o.Parent.Relation == 1).Select(o => o.Parent.Name).FirstOrDefault(),
                MotherName = x.StudentParents.Where(o => o.Parent.Relation == 2).Select(o => o.Parent.Name).FirstOrDefault()
            }).FirstOrDefaultAsync().ConfigureAwait(false);
            return result;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<StudentSchedule>>> GetScheduleStudent(string id)
        {
            var data = await (from cs in _context.ClassStudent.Where(x => x.Student.SuspendDate == null)
                              join c in _context.ClassCourse.Include(x => x.ClassTeachers).ThenInclude(x => x.Teacher) on cs.ClassId equals c.Id
                              join sc in _context.StudentCourse on cs.StudentId equals sc.StudentId
                              join ls in _context.Schedule on c.Id equals ls.ClassCourseId
                              join cr in _context.ClassRoom on ls.ClassRoomId equals cr.Id into ljCR
                              from _cr in ljCR.DefaultIfEmpty()
                                  //join ct in _context.ClassTeacher on c.Id equals ct.ClassId into ljCT
                                  //from _ct in ljCT.DefaultIfEmpty()
                              where cs.StudentId == id
                              select new StudentSchedule()
                              {
                                  Id = cs.Id,
                                  Class = c.Name,
                                  ClassRoom = _cr.Name,
                                  StartDate = ls.StartDate,
                                  EndDate = ls.EndDate,
                                  StartTime = ls.StartTime,
                                  EndTime = ls.EndTime,
                                  Schedule = ls.ScheduleFormat,
                                  ScheduleObj = ls,
                                  Teacher = c.Teachers,
                                  ClassCourse = c
                              }).ToListAsync().ConfigureAwait(false);
            foreach (var item in data)
            {
                if (item.StartDate < item.EndDate)
                {
                    var schedule = item.ScheduleObj;
                    int studentTime = 0;
                    DateTime dtNow = DateTime.Now;
                    List<DayOfWeek> lstDayOfWeeks = new List<DayOfWeek>();
                    if (schedule.Monday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Monday);
                    }
                    if (schedule.Tuesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Tuesday);
                    }
                    if (schedule.Wednesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Wednesday);
                    }
                    if (schedule.Thursday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Thursday);
                    }
                    if (schedule.Friday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Friday);
                    }
                    if (schedule.Saturday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Saturday);
                    }
                    if (schedule.Sunday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Sunday);
                    }

                    DateTime tempDate;
                    if (schedule.StartDate > dtNow)
                    {
                        studentTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                        tempDate = schedule.StartDate;
                    }
                    else
                    {
                        studentTime = schedule.EndDate.Subtract(dtNow).Days;
                        tempDate = dtNow;
                    }

                    int countDay = 0;
                    for (int i = 0; i < studentTime; i++)
                    {
                        var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (checkDayOfWeeks)
                        {
                            countDay++;
                        }
                        tempDate = tempDate.AddDays(1);
                    }

                    item.LearningProccess = countDay.ToString();
                }
            }
            return data;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<StudentReportGrid>>> GetReportStudentCatchUp(string id)
        {
            var dataCatchUp = await (from cs in _context.ClassStudent.Where(x => x.Student.SuspendDate == null)
                                     join cc in _context.ClassCourse on cs.ClassId equals cc.Id
                                     join cl in _context.ClassLesson on cc.Id equals cl.ClassId
                                     join cu in _context.CatchUpSchedules on cl.Id equals cu.ClassLessonId
                                     join sl in _context.StudyLevel on cc.LevelId equals sl.Id
                                     where cs.StudentId == id
                                     group cc by new { cc.Id, cc.Name, LevelName = sl.Name } into g
                                     select new StudentReportGrid()
                                     {
                                         Id = g.Key.Id,
                                         Class = g.Key.Name,
                                         Level = g.Key.LevelName,
                                         NumberOfCatchUp = g.Count()
                                     }).ToListAsync().ConfigureAwait(false);
            var dataLog = await (from s in _context.Student.Where(x => x.SuspendDate == null)
                                 join slld in _context.StudentLessonLogData on s.Id equals slld.StudentInfoId
                                 join tll in _context.TeacherLessonLog on slld.LogId equals tll.Id
                                 join cl in _context.ClassLesson on tll.ClassLessonId equals cl.Id
                                 join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                 join sl in _context.StudyLevel on cc.LevelId equals sl.Id
                                 where s.Id == id
                                 group cl by new { cl.ClassId, cc.Name, LevelName = sl.Name } into g
                                 select new StudentReportGrid()
                                 {
                                     Id = g.Key.ClassId,
                                     Class = g.Key.Name,
                                     Level = g.Key.LevelName,
                                     NumberOfClassSession = g.Count()
                                 }).ToListAsync().ConfigureAwait(false);

            var dataNumberClassSession = await (from s in _context.Student.Where(x => x.SuspendDate == null)
                                                join cs in _context.ClassStudent on s.Id equals cs.StudentId
                                                join sc in _context.StudentCourse on s.Id equals sc.StudentId
                                                join cc in _context.ClassCourse on cs.ClassId equals cc.Id
                                                join sch in _context.Schedule on cc.Id equals sch.ClassCourseId
                                                join sl in _context.StudyLevel on cc.LevelId equals sl.Id
                                                where s.Id == id
                                                select new StudentReportGrid()
                                                {
                                                    Id = cc.Id,
                                                    Class = cc.Name,
                                                    Level = sl.Name,
                                                    StartDateCourse = sc.StartDate.GetValueOrDefault(),
                                                    EndDateCourse = sc.EndDate.GetValueOrDefault(),
                                                    Schedule = sch
                                                }).ToListAsync().ConfigureAwait(false);
            DateTime dtCurrent = DateTime.Now;
            foreach (var item in dataNumberClassSession)
            {
                List<DayOfWeek> lstDayOfWeeks = new List<DayOfWeek>();
                var schedule = item.Schedule;
                if (schedule.Monday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Monday);
                }
                if (schedule.Tuesday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Tuesday);
                }
                if (schedule.Wednesday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Wednesday);
                }
                if (schedule.Thursday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Thursday);
                }
                if (schedule.Friday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Friday);
                }
                if (schedule.Saturday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Saturday);
                }
                if (schedule.Sunday)
                {
                    lstDayOfWeeks.Add(DayOfWeek.Sunday);
                }

                int studentTime = 0;
                DateTime tempDate = new DateTime();
                if (item.StartDateCourse != null && item.EndDateCourse != null)
                {
                    if (item.StartDateCourse > dtCurrent)
                    {
                        studentTime = item.EndDateCourse.Subtract(item.StartDateCourse).Days;
                        tempDate = schedule.StartDate;
                    }
                    else
                    {
                        studentTime = item.EndDateCourse.Subtract(dtCurrent).Days;
                        tempDate = dtCurrent;
                    }
                }

                int countDay = 0;
                for (int i = 0; i < studentTime; i++)
                {
                    var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                    if (checkDayOfWeeks)
                    {
                        countDay++;
                    }
                    tempDate = tempDate.AddDays(1);
                }
                item.NumberOfRemainingSession = countDay;
            }

            Dictionary<string, int> lstDateLargest = new Dictionary<string, int>
            {
                { "DataLog", dataLog.Count },
                { "DataCatchUp", dataCatchUp.Count },
                { "dataNumberClassSession", dataNumberClassSession.Count }
            };
            var numberLargest = lstDateLargest.Aggregate((x, y) => x.Value > y.Value ? x : y).Key;
            if (numberLargest == "DataLog")
            {
                foreach (var item in dataLog) // check obj isset
                {
                    var temp = dataCatchUp.FirstOrDefault(x => x.Id == item.Id);
                    var tempNumberClassSession = dataNumberClassSession.FirstOrDefault(x => x.Id == item.Id);
                    if (temp != null)
                    {
                        item.NumberOfCatchUp = temp.NumberOfCatchUp;
                    }
                    if (tempNumberClassSession != null)
                    {
                        item.NumberOfRemainingSession = tempNumberClassSession.NumberOfRemainingSession;
                    }
                }

                var checkCatchUp = dataCatchUp.Where(x => !dataLog.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfCatchUp = x.NumberOfCatchUp
                    });
                foreach (var item in checkCatchUp)
                {
                    dataLog.Add(item);
                }

                var checkDataNumberClassSession = dataNumberClassSession.Where(x => !dataLog.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfRemainingSession = x.NumberOfRemainingSession
                    });
                foreach (var item in checkDataNumberClassSession)
                {
                    dataLog.Add(item);
                }

                return dataLog;
            }
            else if (numberLargest == "DataCatchUp")
            {
                foreach (var item in dataCatchUp) // check obj isset
                {
                    var temp = dataLog.FirstOrDefault(x => x.Id == item.Id);
                    if (temp != null)
                    {
                        item.NumberOfClassSession = temp.NumberOfClassSession;
                    }
                }
                var checkDataLog = dataLog.Where(x => !dataCatchUp.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfClassSession = x.NumberOfClassSession
                    });
                foreach (var item in checkDataLog)
                {
                    dataCatchUp.Add(item);
                }
                var checkDataNumberClassSession = dataNumberClassSession.Where(x => !dataCatchUp.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfRemainingSession = x.NumberOfRemainingSession
                    });
                foreach (var item in checkDataNumberClassSession)
                {
                    dataCatchUp.Add(item);
                }

                return dataCatchUp;
            }
            else
            {
                foreach (var item in dataNumberClassSession) // check obj isset
                {
                    var temp = dataCatchUp.FirstOrDefault(x => x.Id == item.Id);
                    var tempDataLog = dataLog.FirstOrDefault(x => x.Id == item.Id);
                    if (temp != null)
                    {
                        item.NumberOfCatchUp = temp.NumberOfCatchUp;
                    }
                    if (tempDataLog != null)
                    {
                        item.NumberOfClassSession = tempDataLog.NumberOfClassSession;
                    }
                }

                var checkCatchUp = dataCatchUp.Where(x => !dataNumberClassSession.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfCatchUp = x.NumberOfCatchUp
                    });
                foreach (var item in checkCatchUp)
                {
                    dataNumberClassSession.Add(item);
                }

                var checkDataLog = dataLog.Where(x => !dataLog.Select(d => d.Id).Contains(x.Id)).Select(x =>
                    new StudentReportGrid()
                    {
                        Id = x.Id,
                        Class = x.Class,
                        Level = x.Level,
                        NumberOfClassSession = x.NumberOfClassSession
                    });
                foreach (var item in checkDataLog)
                {
                    dataNumberClassSession.Add(item);
                }

                return dataNumberClassSession;
            }
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<StudentReportGrid>>> GetClassPropose(string userId)
        {
            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = from lv in _context.StudyLevel
                         from cls in _context.ClassCourse.Where(cls => lv.Id == cls.LevelId && (assignedCampus == null || assignedCampus.Contains(cls.CampusId)))
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId)
                         from rm in _context.ClassRoom.Where(rm => sch.ClassRoomId == rm.Id).DefaultIfEmpty()
                         from cps in _context.Campus.Where(cps => cls.CampusId == cps.Id).DefaultIfEmpty()
                         orderby sch.StartDate descending
                         select new StudentReportGrid()
                         {
                             Id = cls.Id,
                             Level = lv.Name,
                             LevelId = lv.Id,
                             Class = cls.Name,
                             ScheduleFormat = sch.ScheduleFormat,
                             Room = rm.Name,
                             Schedule = sch,
                             StartDate = sch.StartDate,
                             EndDate = sch.EndDate,
                             LearningProccess = (from cl in _context.ClassLesson.Where(cl => cl.ClassId == cls.Id && cl.StartTime <= DateTime.Today) select cl).Count().ToString(),
                             NumberOfPupils = (from ct in _context.ClassStudent.Where(ct => ct.ClassId == cls.Id && ct.StudentType == ClassType.Regular) select ct).Count(),
                             Campus = cps.Name ?? "",
                             CampusId = cps.Id ?? "",
                             Address = cps.Address ?? ""
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpPost("[action]/{id}")]
        public async Task<ActionResult<ClassStudent>> ChangeClassStudent(string id, [FromBody] dynamic changeClassData)
        {
            string currentClassId = changeClassData.currentClassId;
            ClassStudent currentClass = await _context.ClassStudent.Where(x => x.StudentId == id && x.ClassId == currentClassId)
                                                          .FirstOrDefaultAsync().ConfigureAwait(false);
            if (currentClass == null)
            {
                return NotFound();
            }

            currentClass.StudentType = ClassType.Suspended;
            currentClass.SortOrder = 100;
            _context.Entry(currentClass).SetActivityState(ActivityActionType.Performed);

            ClassStudent newClass = new() { StudentId = id, ClassId = changeClassData.changeClassId, StudentType = (ClassType)changeClassData.studentType, SortOrder = 50, RegisterDate = DateTime.Now };
            _context.ClassStudent.Add(newClass);

            StudentClassChange studentClassChange = new() { StudentId = id, ClassChangeDate = DateTime.Now, PreviousClassId = currentClassId };
            _context.StudentClassChange.Add(studentClassChange);

            await _context.SaveChangesAsync().ConfigureAwait(false);

            return newClass;
        }
        [HttpPost("[action]/{id}")]
        public async Task<ActionResult<ClassStudent>> ParallelClassStudent(string id, [FromBody] dynamic changeClassData)
        {
            string currentClassId = changeClassData.currentClassId;
            ClassStudent currentClass = await _context.ClassStudent.Where(x => x.StudentId == id && x.ClassId == currentClassId)
                                                          .FirstOrDefaultAsync().ConfigureAwait(false);
            if (currentClass == null)
            {
                return NotFound();
            }

            currentClass.StudentType = ClassType.Regular;
            currentClass.SortOrder = 100;
            _context.Entry(currentClass).SetActivityState(ActivityActionType.Performed);

            ClassStudent newClass = new ClassStudent { StudentId = id, ClassId = changeClassData.changeClassId, StudentType = (ClassType)changeClassData.studentType, SortOrder = 50, RegisterDate = DateTime.Now };
            _context.ClassStudent.Add(newClass);

            StudentClassChange studentClassChange = new() { StudentId = id, ClassChangeDate = DateTime.Now, PreviousClassId = currentClassId };
            _context.StudentClassChange.Add(studentClassChange);

            await _context.SaveChangesAsync().ConfigureAwait(false);

            return newClass;
        }

        [HttpPost("[action]/{id}")]
        public async Task<ActionResult<Student>> TerminateStudent(string id, [FromBody] dynamic bodyData)
        {
            ClassStudent currentClass = await _context.ClassStudent.Where(x => x.StudentId == id && x.StudentType != ClassType.Suspended && x.StudentType != ClassType.Terminated)
                                                          .FirstOrDefaultAsync().ConfigureAwait(false);
            if (currentClass != null)
            {
                currentClass.StudentType = ClassType.Terminated;
                currentClass.SortOrder = 100;
                _context.Entry(currentClass).SetActivityState(ActivityActionType.Terminated);
            }

            var student = await _context.Student.Where(u => u.Id == id).FirstAsync().ConfigureAwait(false);
            if (student == null)
            {
                return NotFound();
            }

            student.TerminateDate = DateTime.Now;
            student.TerminateReason = bodyData.reason;
            _context.Entry(student).SetActivityState(ActivityActionType.Terminated);

            await _context.SaveChangesAsync().ConfigureAwait(false);
            await UpdateClassStudentStatus(id, ClassType.Terminated).ConfigureAwait(false);

            return student;
        }

        [HttpPost("[action]/{id}")]
        public async Task<ActionResult<Student>> SuspendStudent(string id, [FromBody] dynamic suspendData)
        {
            ClassStudent currentClass = await _context.ClassStudent.Where(x => x.StudentId == id && x.StudentType != ClassType.Suspended && x.StudentType != ClassType.Terminated)
                                                          .FirstOrDefaultAsync().ConfigureAwait(false);
            if (currentClass != null)
            {
                currentClass.StudentType = ClassType.Suspended;
                currentClass.SortOrder = 100;
                _context.Entry(currentClass).SetActivityState(ActivityActionType.Suspended);
            }

            var student = await _context.Student.Where(u => u.Id == id).FirstAsync().ConfigureAwait(false);
            if (student == null)
            {
                return NotFound();
            }

            student.SuspendDate = DateTime.Now;
            student.SuspendMonths = suspendData.suspendMonths;
            student.SuspendReason = suspendData.suspendReason;

            _context.Entry(student).SetActivityState(ActivityActionType.Suspended);
            await _context.SaveChangesAsync().ConfigureAwait(false);
            await UpdateClassStudentStatus(id, ClassType.Suspended).ConfigureAwait(false);

            return student;
        }

        public async Task UpdateClassStudentStatus(string studentId, ClassType studentStatus)
        {
            var classStudent = await _context.ClassStudent.Where(x => x.StudentId == studentId && x.StudentType != ClassType.Suspended && x.StudentType != ClassType.Terminated)
                                                          .FirstOrDefaultAsync().ConfigureAwait(false);

            classStudent.StudentType = studentStatus;
            classStudent.SortOrder = 100;
            _context.Entry(classStudent).State = EntityState.Modified;
            await _context.SaveChangesAsync().ConfigureAwait(false);
        }

        [Authorize]
        [HttpGet("branch")]
        public async Task<ActionResult<IEnumerable<StudentByBranchResponse>>> GetStudentByBranch()
        {
            string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);
            return await _context.Student
                        .Where(s => s.ClassStudents.Any(cs => assignedCampus == null || assignedCampus.Contains(cs.ClassCourse.CampusId))).Select(x => new StudentByBranchResponse
                        {
                            Id = x.Id,
                            StudentName = x.StudentName,
                            EnglishName = x.EnglishName,
                            Branchs = x.ClassStudents.Select(cs => cs.ClassCourse.CampusId).Distinct().ToList()
                        }).ToListAsync();
        }
    }
}
