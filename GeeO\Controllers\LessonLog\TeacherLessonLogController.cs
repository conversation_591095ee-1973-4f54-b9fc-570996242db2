﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using GeeO.GridVo;
using System.Globalization;
using GeeO.Common;
using Microsoft.Data.SqlClient;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class TeacherLessonLogController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;
        private readonly ISSISService _ssisService;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly ILogger<TeacherLessonLogController> _logger;

        public TeacherLessonLogController(
            GeeODbContext context,
            IAcadManageService acadManageService,
            IUploadService uploadService,
            IFileService fileService,
            ISSISService ssisService,
            IStudentPaymentService studentPaymentService,
            ILogger<TeacherLessonLogController> logger)
        {
            _context = context;
            _acadManageService = acadManageService;
            _fileService = fileService;
            _uploadService = uploadService;
            _ssisService = ssisService;
            _studentPaymentService = studentPaymentService;
            _logger = logger;
        }

        // GET: api/TeacherLessonLog/GetClassStudentReport/5
        [HttpGet("[action]/{classLogId}")]
        public async Task<ActionResult<IEnumerable<MyClassStudentLogVo>>> GetClassStudentReport(string classLogId)
        {
            var result = from tll in _context.TeacherLessonLog.Where(tll => tll.Id == classLogId)
                         from sll in _context.StudentLessonLogData.Where(sll => sll.LogId == tll.Id)
                         from std in _context.Student.Where(std => std.Id == sll.StudentInfoId)
                         orderby std.StudentName
                         select new MyClassStudentLogVo
                         {
                             Id = sll.Id,
                             LogId = tll.Id,
                             StudentInfoId = sll.StudentInfoId,
                             StudentName = std.StudentName,
                             EnglishName = std.EnglishName,
                             Present = (AttendanceType)sll.Present,
                             StarScore = sll.StarScore,
                             Note = sll.Note
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/TeacherLessonLog/GetClassLessonReport/5
        [HttpGet("[action]/{classLogId}")]
        public async Task<ActionResult<IEnumerable<MyLessonLogVo>>> GetClassLessonReport(string classLogId)
        {
            var classLog = await _context.TeacherLessonLog.Where(c => c.Id == classLogId).FirstOrDefaultAsync().ConfigureAwait(false);

            if (classLog?.HistoryLog == true)
            {
                var historyLog = from tll in _context.TeacherLessonLogData
                                 join lj_lph in _context.LessonPlanUnitHistory on tll.HistoryUnitId equals lj_lph.Id into _lph
                                 from lph in _lph.DefaultIfEmpty()
                                 from tlh in _context.TeacherLessonUnitHistory
                                    .Where(tl => tl.ClassLessonId == tll.LessonLog.ClassLessonId && tl.HistoryUnitId == tll.HistoryUnitId)
                                    .Include(tl => tl.Material)
                                    .DefaultIfEmpty()
                                 where tll.LogId == classLogId
                                 orderby lph.SortOrder
                                 select new MyLessonLogVo
                                 {
                                     Id = tll.Id,
                                     SortOrder = lph.SortOrder ?? 0,
                                     Time = Convert.ToString(lph.Time, CultureInfo.CurrentCulture),
                                     Procedures = lph.Procedures,
                                     Material = tlh.Material ?? lph.Material,
                                     ActualTime = TimeSpan.FromSeconds(tll.Duration).ToString(@"mm\:ss", CultureInfo.CurrentCulture),
                                     Note = tll.Note
                                 };
                return await historyLog.ToListAsync().ConfigureAwait(false);
            }

            var data = _context.TeacherLessonLog
                    .Include(c => c.LogDatas)
                    .Where(c => c.Id == classLogId)
                    .AsNoTracking();

            var result = from c in data
                         from l in c.LogDatas
                         from tl in _context.TeacherLessonUnit
                            .Where(tl => l.LessonLog.ClassLessonId == tl.ClassLessonId && l.UnitId == tl.UnitId)
                            .Include(u => u.Material)
                            .DefaultIfEmpty()
                         orderby l.Unit.SortOrder
                         select new MyLessonLogVo
                         {
                             Id = l.Id,
                             SortOrder = (int)l.Unit.SortOrder,
                             Time = Convert.ToString(l.Unit.Time, CultureInfo.CurrentCulture),
                             Procedures = l.Unit.Procedures,
                             Material = tl != null && tl.Material != null ? tl.Material : l.Unit.Material,
                             ActualTime = TimeSpan.FromSeconds(l.Duration).ToString(@"mm\:ss", CultureInfo.CurrentCulture),
                             Note = l.Note,
                             IsDeleted = l.Unit.IsDeleted
                         };
            return await result.Where(x => !x.IsDeleted).ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classLogId}")]
        public async Task<ActionResult<IEnumerable<MyLessonLogVo>>> GetLessonReport(string classLogId)
        {
            var data = _context.TeacherLessonLog
                    .Where(c => c.Id == classLogId)
                    .Include(c => c.LogDatas)
                    .AsNoTracking();

            var result = await (from c in data
                                from l in c.LogDatas
                                join tl in _context.TeacherLessonUnit.Include(u => u.Material).Where(x => !x.Unit.IsDeleted) on l.UnitId equals tl.UnitId into t
                                from teacherLesson in t.DefaultIfEmpty()
                                orderby l.Unit.SortOrder
                                select new MyLessonLogVo
                                {
                                    Id = l.Id,
                                    SortOrder = (int)l.Unit.SortOrder,
                                    Time = Convert.ToString(l.Unit.Time, CultureInfo.CurrentCulture),
                                    Procedures = l.Unit.Procedures,
                                    Material = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material : l.Unit.Material,
                                    ActualTime = TimeSpan.FromSeconds(l.Duration).ToString(@"mm\:ss", CultureInfo.CurrentCulture),
                                    Note = l.Note
                                }).FirstOrDefaultAsync().ConfigureAwait(false);
            List<MyLessonLogVo> arrResult = new List<MyLessonLogVo>();
            if (result != null)
            {
                arrResult.Add(result);
            }
            return arrResult.ToList();
        }

        // GET: api/TeacherLessonLog/GetClassTeacherLessonReport/5
        [HttpGet("[action]/{classLogId}")]
        public async Task<ActionResult<MyLessonLogVo>> GetClassTeacherLessonReport(string classLogId)
        {
            var data = _context.TeacherLessonLog
                    .Where(c => c.Id == classLogId)
                    .Include(c => c.LogDatas)
                    .AsNoTracking();

            var result = from c in data
                         from l in c.LogDatas
                         join tl in _context.TeacherLessonUnit.Include(u => u.Material).Where(x => !x.Unit.IsDeleted) on l.UnitId equals tl.UnitId into t
                         from teacherLesson in t.DefaultIfEmpty()
                         orderby l.Unit.SortOrder
                         select new MyLessonLogVo
                         {
                             Id = l.Id,
                             SortOrder = (int)l.Unit.SortOrder,
                             Time = Convert.ToString(l.Unit.Time, CultureInfo.CurrentCulture),
                             Procedures = l.Unit.Procedures,
                             Material = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material : l.Unit.Material,
                             ActualTime = TimeSpan.FromSeconds(l.Duration).ToString(@"mm\:ss", CultureInfo.CurrentCulture),
                             Note = l.Note
                         };
            return await result.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        // GET: api/TeacherLessonLog/GetClassLessonInfo/5
        [HttpGet("[action]/{classLogId}")]
        public async Task<ActionResult<MyClassLessonVo>> GetClassLessonInfo(string classLogId)
        {
            var data = _context.TeacherLessonLog
                    .Where(c => c.Id == classLogId)
                    .Include(c => c.Teacher)
                    .Include(c => c.ClassLesson)
                        .ThenInclude(cl => cl.ClassCourse)
                            .ThenInclude(cc => cc.ClassTeachers)
                                .ThenInclude(ct => ct.Teacher)
                    .AsNoTracking();

            var result = await (from c in data
                                select new MyClassLessonVo
                                {
                                    Id = c.Id,
                                    ClassTeachers = c.ClassLesson.ClassCourse.ClassTeachers.Select(t => t.Teacher).ToList(),
                                    Level = c.ClassLesson.ClassCourse.Level.Name,
                                    Class = c.ClassLesson.ClassCourse.Name,
                                    Schedule = c.ClassLesson.ClassCourse.Schedule.ScheduleFormat,
                                    StartTimeLocal = c.ClassLesson.ClassCourse.Schedule.StartTimeLocal,
                                    EndTimeLocal = c.ClassLesson.ClassCourse.Schedule.EndTimeLocal,
                                    StartTime = c.ClassLesson.StartTime.Value,
                                    Lesson = c.ClassLesson.Lesson.Lesson,
                                    Subject = c.ClassLesson.Lesson.Subject,
                                    Content = c.ClassLesson.Lesson.Content,
                                    Tb = c.ClassLesson.Lesson.Tb,
                                    LogDateTime = c.LogDateTime,
                                    ClassId = c.ClassLesson.ClassId
                                }).FirstAsync().ConfigureAwait(false);

            if (!string.IsNullOrEmpty(result.UserLoggingId))
            {
                var u = _context.AspNetUsers.Where(x => x.Id == result.UserLoggingId).FirstOrDefault();
                result.ReportBy = u != null ? u.FullName : result.CreatedBy;
            }
            else
            {
                result.ReportBy = result.CreatedBy;
            }

            return result;
        }

        // GET: api/TeacherLessonLog/GetLessonLogs/5
        [HttpPost("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<MyClassLessonVo>>> GetLessonLogs(string userId, AcademicFilterParams @params)
        {
            var user = await _context.AspNetUsers.Where(x => x.Id == userId).Include(x => x.Role).FirstOrDefaultAsync();
            if (user == null)
            {
                return NotFound();
            }

            var assignedCampus = string.IsNullOrEmpty(@params.Campus)
                                        ? await _acadManageService.GetAssignedCampus(userId)
                                        : new List<string> { @params.Campus };

            var teachLogs = await (from tll in _context.TeacherLessonLog/*.Where(tll => tll.HistoryLog == false)*/
                                   from cl in _context.ClassLesson.Where(cl => tll.ClassLessonId == cl.Id)
                                   from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id &&
                                                                                 (user.Role.Name != EnumsHelper.GetDescription(AcadRoles.Teacher) ||
                                                                                  cls.ClassTeachers.Select(x => x.TeacherId).Contains(userId)))
                                   from sch in _context.Schedule.Where(sch => cl.ClassId == sch.ClassCourseId)
                                   where
                                      (assignedCampus == null || assignedCampus.Contains(cls.CampusId)) &&
                                      (string.IsNullOrEmpty(@params.Level) || cls.LevelId == @params.Level) &&
                                      (string.IsNullOrEmpty(@params.Class) || cls.Id == @params.Class) &&
                                      (string.IsNullOrEmpty(@params.SearchText) ||
                                       EF.Functions.Like(cls.Name, $"%{@params.SearchText}%"))
                                   orderby cl.StartTime descending
                                   select new MyClassLessonVo
                                   {
                                       Id = tll.Id,
                                       RouteId = cl.Id,
                                       ClassId = cls.Id,
                                       ClassLessonId = cl.Id,
                                       Level = cls.Level.Name,
                                       Class = cls.Name,
                                       Schedule = sch.ScheduleFormat,
                                       StartTimeLocal = sch.StartTimeLocal,
                                       EndTimeLocal = sch.EndTimeLocal,
                                       StartTime = cl.StartTime.Value,
                                       Lesson = cl.Lesson.Lesson,
                                       Subject = cl.Lesson.Subject,
                                       Content = cl.Lesson.Content,
                                       Tb = cl.Lesson.Tb,
                                       CreatedBy = tll.CreatedBy,
                                       CreatedDate = tll.CreatedDate,
                                       ModifiedBy = tll.ModifiedBy,
                                       ModifiedDate = tll.ModifiedDate,
                                       LogDateTime = tll.LogDateTime
                                   }).ToListAsync().ConfigureAwait(false);

            foreach (var teachLog in teachLogs)
            {
                if (!string.IsNullOrEmpty(teachLog.UserLoggingId))
                {
                    var u = _context.AspNetUsers.Where(x => x.Id == teachLog.UserLoggingId).FirstOrDefault();
                    teachLog.ReportBy = u != null ? u.FullName : teachLog.CreatedBy;
                }
                else
                {
                    teachLog.ReportBy = teachLog.CreatedBy;
                }
            }

            return teachLogs;
        }

        [HttpGet("[action]/{userId}/{classId}")]
        public async Task<ActionResult<IEnumerable<MyClassLessonVo>>> GetLessonLogsByClassId(string userId, string classId)
        {
            var result = await (from tll in _context.TeacherLessonLog
                                from tch in _context.AspNetUsers.Where(tch => tll.TeacherId == tch.Id).DefaultIfEmpty()
                                from cl in _context.ClassLesson.Where(cl => tll.ClassLessonId == cl.Id)
                                from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                                from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id && cls.Id == classId)
                                from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId)
                                orderby tll.LogDateTime descending
                                select new MyClassLessonVo
                                {
                                    Id = tll.Id,
                                    RouteId = cl.Id,
                                    Level = cls.Level.Name,
                                    Class = cls.Name,
                                    Schedule = sch.ScheduleFormat,
                                    StartTimeLocal = sch.StartTimeLocal,
                                    EndTimeLocal = sch.EndTimeLocal,
                                    StartTime = cl.StartTime.Value,
                                    Lesson = lp.Lesson,
                                    Subject = lp.Subject,
                                    Content = lp.Content,
                                    Tb = lp.Tb,
                                    LogDateTime = tll.LogDateTime,
                                    ClassId = cl.ClassId,
                                }).ToListAsync().ConfigureAwait(false);

            foreach (var teachLog in result)
            {
                if (!string.IsNullOrEmpty(teachLog.UserLoggingId))
                {
                    var u = _context.AspNetUsers.Where(x => x.Id == teachLog.UserLoggingId).FirstOrDefault();
                    teachLog.ReportBy = u != null ? u.FullName : teachLog.CreatedBy;
                }
                else
                {
                    teachLog.ReportBy = teachLog.CreatedBy;
                }
            }
            return result;
        }

        // GET: api/TeacherLessonLog/GetMyClassStudentLog/5
        [HttpGet("[action]/{userId}/{classLessonId}/{classId}")]
        public async Task<ActionResult<IEnumerable<MyClassStudentLogVo>>> GetMyClassStudentLog(string userId, string classLessonId, string classId)
        {
            var data = _context.TeacherLessonLog
                    .Where(c => c.TeacherId == userId && c.ClassLessonId == classLessonId && c.LogDateTime.Date == DateTime.Now.Date)
                    .Include(c => c.StudentLogDatas)
                    //.Include(c => c.StudentLogDatas.Where(x=> x.StudentInfo.SuspendDate == null))
                    .AsNoTracking();

            var result = from c in data
                         from l in c.StudentLogDatas.Where(x => x.StudentInfo.SuspendDate == null)
                         orderby l.StudentInfo.ClassStudents.Where(x => x.ClassId == classId).FirstOrDefault().SortOrder
                         select new MyClassStudentLogVo
                         {
                             Id = l.Id,
                             LogId = c.Id,
                             StudentInfoId = l.StudentInfoId,
                             SortOrder = l.StudentInfo.ClassStudents.Count > 0 ? (int)l.StudentInfo.ClassStudents.Where(x => x.ClassId == classId).FirstOrDefault().SortOrder : 0,
                             StudentName = l.StudentInfo.StudentName,
                             EnglishName = l.StudentInfo.EnglishName,
                             Present = (AttendanceType)l.Present,
                             StarScore = l.StarScore,
                             Note = l.Note
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/TeacherLessonLog/GetMyLessonLog/5
        [HttpGet("[action]/{userId}/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<MyLessonLogVo>>> GetMyLessonLog(string userId, string classLessonId)
        {
            var data = _context.TeacherLessonLog
                    .Where(c => c.TeacherId == userId && c.ClassLessonId == classLessonId && c.LogDateTime.Date == DateTime.Now.Date)
                    .Include(c => c.LogDatas)
                    .AsNoTracking();

            var result = from c in data
                         from l in c.LogDatas
                         join tl in _context.TeacherLessonUnit.Where(c => c.ClassLessonId == classLessonId).Include(l => l.Material).Where(x => !x.Unit.IsDeleted) on l.UnitId equals tl.UnitId into t
                         from teacherLesson in t.DefaultIfEmpty()
                         orderby l.Unit.SortOrder
                         select new MyLessonLogVo
                         {
                             Id = l.Id,
                             SortOrder = (int)l.Unit.SortOrder,
                             Procedures = l.Unit.Procedures,
                             Material = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material : l.Unit.Material,
                             Time = TimeSpan.FromSeconds(l.Duration).ToString(@"mm\:ss", CultureInfo.CurrentCulture),
                             Note = l.Note
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // PGET: api/TeacherLessonLog/InitStudentLog/5
        [HttpGet("[action]/{classLessonId}/{lessonLogId}")]
        public async Task<IActionResult> InitStudentLog(string classLessonId, string lessonLogId)
        {
            //var classData = _context.ClassLesson
            //            .Where(c => c.Id == classLessonId)
            //            .Include(c => c.ClassCourse)
            //                .ThenInclude(c => c.ClassDatas)
            //            .AsNoTracking();
            var classData = _context.ClassLesson
                .Where(c => c.Id == classLessonId)
                .AsNoTracking();
            var result = await (from c in classData
                                    //from l in c.ClassCourse.ClassDatas
                                from l in c.ClassCourse.ClassStudents
                                select new StudentLessonLogData
                                {
                                    LogId = lessonLogId,
                                    StudentInfoId = l.StudentId,
                                    Present = -1,
                                    StarScore = 0,
                                    Note = string.Empty
                                }).ToListAsync().ConfigureAwait(false);

            foreach (var lessonLogData in result)
            {
                await AddTodayStudentLessonLog(lessonLogData).ConfigureAwait(false);
            }
            //var queryCatchUpStudent = _context.CatchUpSchedules
            //    .Include(x => x.ClassLesson)
            //    .Where(x => x.ClassLesson.Id == classLessonId && x.CatchUpType != 1 && x.CatchUpType != 2)
            //    .Select(x => new StudentLessonLogData()
            //    {
            //        LogId = lessonLogId,
            //        StudentInfoId = x.StudentId,
            //        Present = -1,
            //        StarScore = 0,
            //        Note = string.Empty
            //    });
            //foreach (var item in queryCatchUpStudent)
            //{
            //    await AddTodayStudentLessonLog(item).ConfigureAwait(false);
            //}

            return NoContent();
        }

        // GET: api/TeacherLessonLog/GetStudentLogDataOfClass/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<StudentLessonLogData>>> GetStudentLogDataOfClass(string classLessonId)
        {
            var lessonLogData = await _context.StudentLessonLogData
                                                .Where(l => l.LessonLog.ClassLesson.Id == classLessonId)
                                                .ToListAsync().ConfigureAwait(false);
            return lessonLogData;
        }

        // GET: api/TeacherLessonLog/GetLessonLogReport/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<StudentLessonLogData>>> GetLessonLogReport(string classLessonId)
        {
            var lessonLogData = _context.StudentLessonLogData.Where(l => l.LessonLog.ClassLesson.Id == classLessonId);

            return await lessonLogData.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/TeacherLessonLog/GetLessonLog/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<TeacherLessonLog>> GetLessonLog(string classLessonId)
        {
            var lessonLog = _context.TeacherLessonLog.Where(l => l.ClassLessonId == classLessonId /*&& l.HistoryLog == false*/);

            return await lessonLog.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        // GET: api/TeacherLessonLog/GetStudentLessonLog/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<StudentLessonLogData>> GetStudentLessonLog(string id)
        {
            var lessonLogData = await _context.StudentLessonLogData.FindAsync(id);

            if (lessonLogData == null)
            {
                return NotFound();
            }

            return lessonLogData;
        }

        // POST: api/TeacherLessonLog/AddStudentLessonLog
        [HttpPost("[action]")]
        public async Task<ActionResult<StudentLessonLogData>> AddStudentLessonLog(StudentLessonLogData lessonLogData)
        {
            if (lessonLogData == null)
            {
                return BadRequest();
            }

            _context.StudentLessonLogData.Add(lessonLogData);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStudentLessonLog", new { id = lessonLogData.Id }, lessonLogData);
        }

        // POST: api/TeacherLessonLog/AddTodayStudentLessonLog
        [HttpPost("[action]")]
        [Authorize]
        public async Task<ActionResult<StudentLessonLogData>> AddTodayStudentLessonLog(StudentLessonLogData lessonLogDataNew)
        {
            var lessonLogData = await _context.StudentLessonLogData.Where(l => l.LogId == lessonLogDataNew.LogId &&
                                                                               l.StudentInfoId == lessonLogDataNew.StudentInfoId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (lessonLogData == null)
            {
                return await AddStudentLessonLog(lessonLogDataNew).ConfigureAwait(false);
            }

            return CreatedAtAction("GetStudentLessonLog", new { id = lessonLogData.Id }, lessonLogData);
        }

        // PUT: api/TeacherLessonLog/SaveStudentLessonLog/5
        [HttpPut("[action]/{id}")]
        [Authorize]
        public async Task<IActionResult> SaveStudentLessonLog(string id, StudentLessonLogData lessonLogData)
        {
            try
            {
                if (lessonLogData == null || id != lessonLogData.Id)
                {
                    return BadRequest();
                }

                var teacherLog = await _context.TeacherLessonLog.FindAsync(lessonLogData.LogId);
                teacherLog.ModifiedBy = (await CurrentUser()).Id;
                teacherLog.ModifiedDate = DateTime.Now;

                var entity = await _context.StudentLessonLogData.FindAsync(id);
                if (lessonLogData.Present == (int)AttendanceType.Present || lessonLogData.Present == (int)AttendanceType.Absent)
                {
                    var currentSessionFee = await _studentPaymentService.CalculateCurrentSessionFee(lessonLogData.StudentInfoId);
                    if (currentSessionFee != null)
                    {
                        var (paymentId, feePerSession) = currentSessionFee.Value;
                        entity.Fee = feePerSession;
                        entity.StudentCourseId = paymentId;
                    }
                }
                else if (!string.IsNullOrEmpty(lessonLogData.StudentCourseId))
                {
                    await _studentPaymentService.RecalculateFeesForSubsequentLogs(lessonLogData.StudentInfoId, lessonLogData.CreatedAt);
                    entity.Fee = null;
                    entity.StudentCourseId = null;
                }
                entity.Present = lessonLogData.Present;
                entity.Note = lessonLogData.Note;
                entity.StarScore = lessonLogData.StarScore;

                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SaveStudentLessonLog - {ex.Message} - {ex.StackTrace}");
                throw;
            }

            return NoContent();
        }

        // GET: api/TeacherLessonLog/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TeacherLessonLog>> GetTeacherLessonLog(string id)
        {
            var lessonLog = await _context.TeacherLessonLog.FindAsync(id);

            if (lessonLog == null)
            {
                return NotFound();
            }

            return lessonLog;
        }

        // GET: api/TeacherLessonLog/GetLessonLogData/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<TeacherLessonLogData>> GetLessonLogData(string id)
        {
            var lessonLogData = await _context.TeacherLessonLogData.FindAsync(id);

            if (lessonLogData == null)
            {
                return NotFound();
            }

            return lessonLogData;
        }

        // POST: api/TeacherLessonLog
        [HttpPost]
        public async Task<ActionResult<TeacherLessonLog>> PostTeacherLessonLog(TeacherLessonLog lessonLog)
        {
            if (lessonLog == null)
            {
                return BadRequest();
            }

            lessonLog.LogDateTime = DateTime.Now;
            lessonLog.CreatedBy = "system";
            lessonLog.CreatedDate = DateTime.Now;
            _context.TeacherLessonLog.Add(lessonLog);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetTeacherLessonLog", new { id = lessonLog.Id }, lessonLog);
        }

        // POST: api/TeacherLessonLog/AddStudentAbsenceNotify
        [HttpPost("[action]/{logId}")]
        public ActionResult AddStudentAbsenceNotify(string logId)
        {
            var parameters = new object[]
            {
                new SqlParameter("logId", logId)
            };
            _context.Database.ExecuteSqlRaw(@"sp_StudentAbsenceNotify @logId=@logId", parameters);

            return Ok();
        }

        // POST: api/TeacherLessonLog/AddTodayLessonLog
        [HttpPost("[action]")]
        public async Task<ActionResult<TeacherLessonLog>> AddTodayLessonLog(TeacherLessonLog lessonLogNew)
        {
            var lessonLog = await _context.TeacherLessonLog
                                        .Where(l => /*(lessonLogNew.TeacherId == null || l.TeacherId == lessonLogNew.TeacherId) &&*/
                                                    l.ClassLessonId == lessonLogNew.ClassLessonId
                                                    /*&& l.LogDateTime.Date == lessonLogNew.LogDateTime.Date*/)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (lessonLog == null)
            {
                return await PostTeacherLessonLog(lessonLogNew).ConfigureAwait(false);
            }
            else
            {
                lessonLog.LogDateTime = DateTime.Now;
                _context.Entry(lessonLog).State = EntityState.Modified;
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }

            return CreatedAtAction("GetTeacherLessonLog", new { id = lessonLog.Id }, lessonLog);
        }

        // POST: api/TeacherLessonLog/AddLessonLog (to be removed)
        [HttpPost("[action]")]
        public async Task<ActionResult<TeacherLessonLog>> AddLessonLog(TeacherLessonLog lessonLogNew)
        {
            var lessonLog = await _context.TeacherLessonLog
                                        .Where(l => /*(lessonLogNew.TeacherId == null || l.TeacherId == lessonLogNew.TeacherId) &&*/
                                                    l.ClassLessonId == lessonLogNew.ClassLessonId &&
                                                    l.LogDateTime.Date == lessonLogNew.LogDateTime.Date)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (lessonLog == null)
            {
                return await PostTeacherLessonLog(lessonLogNew).ConfigureAwait(false);
            }
            else
            {
                lessonLog.TeacherId = lessonLogNew?.TeacherId;
                _context.Entry(lessonLog).State = EntityState.Modified;
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }

            return CreatedAtAction("GetTeacherLessonLog", new { id = lessonLog.Id }, lessonLog);
        }

        // POST: api/TeacherLessonLog/AddLogData
        [HttpPost("[action]")]
        public async Task<ActionResult<TeacherLessonLogData>> AddLogData(TeacherLessonLogData lessonLogData)
        {
            if (lessonLogData == null)
            {
                return BadRequest();
            }

            _context.TeacherLessonLogData.Add(lessonLogData);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetLessonLogData", new { id = lessonLogData.Id }, lessonLogData);
        }

        // POST: api/TeacherLessonLog/AddTodayLogData
        [HttpPost("[action]")]
        public async Task<ActionResult<TeacherLessonLogData>> AddTodayLogData(TeacherLessonLogData lessonLogDataNew)
        {
            if (lessonLogDataNew == null)
            {
                return BadRequest();
            }

            var lessonLogData = await _context.TeacherLessonLogData.Where(l => l.LogId == lessonLogDataNew.LogId &&
                                                                               l.UnitId == lessonLogDataNew.UnitId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (lessonLogData == null)
            {
                return await AddLogData(lessonLogDataNew).ConfigureAwait(false);
            }

            lessonLogData.Duration = lessonLogDataNew.Duration;
            lessonLogData.Note = lessonLogDataNew.Note;
            await SaveLogData(lessonLogData.Id, lessonLogData).ConfigureAwait(false);
            return CreatedAtAction("GetLessonLogData", new { id = lessonLogData.Id }, lessonLogData);
        }

        // PUT: api/TeacherLessonLog/SaveLogData/5
        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> SaveLogData(string id, TeacherLessonLogData lessonLogData)
        {
            if (lessonLogData == null || id != lessonLogData.Id)
            {
                return BadRequest();
            }

            _context.Entry(lessonLogData).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LogDataExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // PUT: api/TeacherLessonLog/SaveLogDataNote/5
        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> SaveLogDataNote(string id, TeacherLessonLogData lessonLogDataNew)
        {
            if (lessonLogDataNew == null)
            {
                return BadRequest();
            }

            var lessonLogData = await _context.TeacherLessonLogData.FindAsync(id);

            if (lessonLogData == null)
            {
                return NotFound();
            }

            lessonLogData.Note = lessonLogDataNew.Note;
            return await SaveLogData(id, lessonLogData).ConfigureAwait(false);
        }

        // PUT: api/TeacherLessonLog/DeleteLessonLog/5
        [HttpDelete("[action]/{id}")]
        public async Task<IActionResult> DeleteLessonLog(string id)
        {
            TeacherLessonLog lessonLog = await _context.TeacherLessonLog.FindAsync(id);

            if (lessonLog == null)
            {
                return NotFound();
            }

            _context.TeacherLessonLog.Remove(lessonLog);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return NoContent();
        }

        [HttpPost("[action]/{importType}")]
        public async Task<IActionResult> UploadImportFile(string importType, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(importType))
            {
                throw new ArgumentNullException(nameof(importType));
            }
            if (files is null)
            {
                throw new ArgumentNullException(nameof(files));
            }

            _fileService.BackupImportFile(importType);
            await _uploadService.UploadLessonImport(importType, files).ConfigureAwait(false);
            _fileService.RenameImportFile(importType, files[0].FileName);
            _ssisService.RunPackage(importType);

            return Ok(new { count = files.Count });
        }

        [HttpGet("campus/{campusId}")]
        public async Task<ActionResult<object>> GetClassTeacherLessonByCampus(string campusId)
        {
            var result = from tll in _context.TeacherLessonLog
                         join cl in _context.ClassLesson on tll.ClassLessonId equals cl.Id
                         join cc in _context.ClassCourse.Where(c => c.CampusId.Equals(campusId)) on cl.ClassId equals cc.Id
                         group new { cc.Id, cc.Name, cc.CampusId } by new { cc.Id, cc.Name, cc.CampusId } into groupedData
                         orderby groupedData.Key.Name
                         select new
                         {
                             ClassId = groupedData.Key.Id,
                             ClassName = groupedData.Key.Name,
                             TotalClassLessonIds = groupedData.Count()
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        private bool LogDataExists(string id)
        {
            return _context.TeacherLessonLogData.Any(e => e.Id == id);
        }

        private async Task<AspNetUsers> CurrentUser()
        {
            var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            if (userId == null) throw new UnauthorizedAccessException();

            var user = await _context.AspNetUsers.FindAsync(userId.Value);
            return user;
        }

    }
}