﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Mobile.Models;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class StudentController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly IAzureAdService _azureAdService;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;

        public StudentController(GeeODbContext context,
            IStudentPaymentService studentPaymentService,
            IAzureAdService azureAdService,
            IUploadService uploadService,
            IFileService fileService)
        {
            _context = context;
            _studentPaymentService = studentPaymentService;
            _azureAdService = azureAdService;
            _uploadService = uploadService;
            _fileService = fileService;
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<IEnumerable<DailyAssessment>>> GetStudentAssessment(string studentId, string classId)
        {
            var result = from cl in _context.ClassLesson.Where(cl => cl.ClassId == classId)
                         from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id)
                         from sll in _context.StudentLessonLogData.Where(sll => sll.LogId == tll.Id && sll.StudentInfoId == studentId)
                         from sa in _context.StudentAssessment.Where(sa => sa.StudentLessonLogId == sll.Id)
                         from ac in _context.AssessmentCriteria.Where(ac => ac.Id == sa.AssessmentCriteriaId)
                         select new DailyAssessment()
                         {
                             LogId = sll.Id,
                             CriteriaId = ac.Id,
                             CriteriaNumber = ac.CriteriaNumber,
                             CriteriaName = ac.Name,
                             Score = sa.Score
                         };
            
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<IEnumerable<DailyResult>>> GetStudentReport(string studentId, string classId)
        {
            try
            {
                var result = from cl in _context.ClassLesson.Where(cl => cl.StartTime.Value.Date <= DateTime.Now.Date)
                             from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                             from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id && cls.Id == classId)
                             from tll in _context.TeacherLessonLog.Where(tll => cl.Id == tll.ClassLessonId).DefaultIfEmpty()
                             from sll in _context.StudentLessonLogData.Where(sll => tll.Id == sll.LogId && sll.StudentInfoId == studentId).DefaultIfEmpty()
                             orderby cl.StartTime
                             select new DailyResult()
                             {
                                 LogId = sll.Id,
                                 LessonDate = cl.StartTime != null ? cl.StartTime.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture) : string.Empty,
                                 Lesson = lp != null ? lp.Lesson : string.Empty,
                                 LessonNumber = lp != null ? int.Parse(lp.Lesson) : 0,
                                 Present = sll != null ? sll.Present : -1,
                                 StarScore = sll != null ? sll.StarScore : 0,
                                 TeacherComment = sll != null ? sll.Note : string.Empty,
                                 LogDateTime = tll.LogDateTime.ToString("dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture),
                                 LevelName = cls.Level != null ? cls.Level.Name : string.Empty,
                             };
                return await result.ToListAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("studied-session/{studentId}/{classId}")]
        public async Task<ActionResult<int>> GetNumberOfStudiedSessionsByClass(string studentId, string classId)
        {
            return await _studentPaymentService.GetNumberOfStudiedSessionsByClass(studentId, classId);
        }

        [HttpGet("absence-session/{studentId}/{classId}")]
        public async Task<ActionResult<int>> GetNumberOfAbsencesByClass(string studentId, string classId)
        {
            return await _studentPaymentService.GetNumberOfAbsencesByClass(studentId, classId);
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<StudentPaymentInfo>> GetPaymentInfo(string studentId)
        {
            var studentPayments = await _studentPaymentService.GetPaymentInfo(studentId);

            var totalSessions = await _studentPaymentService.GetNumberOfSessions(studentId);

            var totalRemainSessions = await _studentPaymentService.GetNumberOfRemainSessions(studentId);

            var paymentEstimatedEndDate = await _studentPaymentService.CalculateEndDateAsync(studentId, totalRemainSessions);

            var totalAbsences = await _studentPaymentService.GetNumberOfAbsences(studentId);

            var totalFrequentAbsences = await _studentPaymentService.NumberOfFrequentAbsencesAsync(studentId);

            var lastAttendanceDate = await _studentPaymentService.GetLastAttendanceDateAsync(studentId);

            var studentPaymentInfo = new StudentPaymentInfo()
            {
                NumberOfLessons = totalSessions,
                NumberOfRemainSessions = totalRemainSessions,
                NumberOfAbsences = totalAbsences,
                NumberOfFrequentAbsences = totalFrequentAbsences,
                PaymentInfoList = studentPayments,
                LastAttendanceDate = DateTimeUtils.FormatDate(lastAttendanceDate),
                PaymentEstimatedEndDate = DateTimeUtils.FormatDate(paymentEstimatedEndDate),
            };

            return studentPaymentInfo;
        }

        [HttpGet("[action]/{principal}")]
        public async Task<ActionResult<Student>> GetStudent(string principal)
        {
            if (string.IsNullOrEmpty(principal))
            {
                return BadRequest();
            }

            var student = await (from std in _context.Student
                                 from ste in _context.StudentExternalAccount.Where(u => std.Id == u.StudentId && u.Email.ToUpper() == principal.ToUpper())
                                 select std)
                                 .FirstOrDefaultAsync().ConfigureAwait(false);

            if (student == null)
            {
                return NotFound();
            }

            student.ImageBase64 = await _azureAdService.GetUserPhotoAsync(principal);
            return student;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<Student>> GetStudentInfo(string studentId)
        {
            var student = await _context.Student
                                        .Where(u => u.Id == studentId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (student == null)
            {
                return NotFound();
            }

            return student;
        }

    }
}
