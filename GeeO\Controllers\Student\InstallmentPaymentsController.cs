﻿using GeeO.Data.Models;
using GeeO.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using GeeO.Common.Exceptions;
using GeeO.Model.InstallmentPayments;
using Microsoft.Extensions.Logging;
using System;
using GeeO.Models;
using Microsoft.AspNetCore.Authorization;
using System.Linq;
using GeeO.Common;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class InstallmentPaymentsController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly ILogger<InstallmentPaymentsController> _logger;

        public InstallmentPaymentsController(GeeODbContext context, ILogger<InstallmentPaymentsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("list/{studentCourseId}")]
        public async Task<IActionResult> GetByStudentCourse(string studentCourseId)
        {
            var payments = await _context.InstallmentPayments.Where(x => x.StudentCourseId.Equals(studentCourseId))
            .Select(x => new InstallmentPaymentsListResponse
            {
                Id = x.Id,
                StudentCourseId = x.StudentCourseId,
                Amount = x.Amount,
                TransferType = x.TransferType,
                TransferTypeDescription = x.TransferTypeDescription,
                CreatedAt = DateTimeUtils.FormatDate(x.CreatedAt)
            }).ToListAsync();

            return Ok(payments);
        }

        [HttpPost]
        public async Task<ActionResult<InstallmentPayments>> Post(CreateInstallmentPaymentsRequest request)
        {
            var installmentPayment = new InstallmentPayments
                {
                    StudentCourseId = request.StudentCourseId,
                    Amount = request.Amount,
                    TransferType = request.TransferType,
                    TransferTypeDescription = request.TransferTypeDescription
                };

            var entry = _context.InstallmentPayments.Add(installmentPayment);

            await _context.SaveChangesAsync().ConfigureAwait(false);

            return entry.Entity;
        }

        // PUT: api/InstallmentPayments/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> Put(string id, UpdateInstallmentPaymentsRequest request)
        {
            try
            {
                InstallmentPayments record = await _context.InstallmentPayments.FindAsync(id);
                if (record == null)
                {
                    throw new NotFoundException($"InstallmentPayment with ID {id} not found.");
                }

                record.Amount = request.Amount;
                record.TransferType = request.TransferType;
                record.TransferTypeDescription = request.TransferTypeDescription;
                record.StudentCourseId = request.StudentCourseId;

                _context.Entry(record).State = EntityState.Modified;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"PutInstallmentPayments - {ex.Message} - {ex.StackTrace}");
                throw ex;
            }

            return NoContent(); 
        }
    }
}
