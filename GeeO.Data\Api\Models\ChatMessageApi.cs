using System;
using System.Collections.Generic;

namespace GeeO.Api.Models
{
    public class ChatMessageApi
    {
        public string UserId { get; set; }
        public string UserEmail { get; set; }
        public string UserName { get; set; }
        public int MessageType { get; set; } // 0: text, 1: photo
        public string Message { get; set; }
        public DateTime SentTime { get; set; }
        public int SenderType { get; set; } // 0: Acad user (<PERSON><PERSON>, Teacher), 1: Student
        public string Type { get; set; } // Ad<PERSON>, Teacher, Class
        public string ClassId { get; set; }
        public IEnumerable<string> Recipients { get; set; }
    }
}