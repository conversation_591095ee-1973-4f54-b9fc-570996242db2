import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import CommonDataTable from '../ui/table/CommonDataTable';
import CommonSearchToolbar from '../ui/table/CommonSearchToolbar';
import authService from '../api-authorization/AuthorizeService';
import { Loading } from '../ui/Loading';

const styles = theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
    marginBottom: theme.spacing(2),
    overflowX: 'auto'
  },
  cell: {
    padding: theme.spacing(1, 0, 1, 2)
  }
});

class StudentSelectComp extends Component {
  static displayName = StudentSelectComp.name;
  constructor(...args) {
    super(...args);
    this.state = {
      data: [],
      lstSelected: [],
      studentId: this.props.studentId,
      filterText: '',
      loading: true
    };
  }

  componentDidMount() {
    this.populateParentData();
  }

  async populateParentData() {
    const token = await authService.getAccessToken();
    const response = await fetch(
      `api/StudentParents/list-filter-parent/${this.state.studentId}`,
      {
        headers: !token ? {} : { Authorization: `Bearer ${token}` }
      }
    );
    const data = await response.json();
    this.setState({ data, loading: false }, () => this.setChildData(''));
  }

  callbackValueCheckboxTabl = e => {
    this.props.callbackValueCheckboxTabl(e);
  };

  handleChange = filterText => {
    this.setChildData(filterText);
  };

  setChildData = filterText => {
    const { data } = this.state;
    const searchTerm = filterText.toLowerCase();
    let filteredRows = data.filter(item => {
      const itemText = (
        item.name +
        item.email +
        item.phoneNumber +
        item.relationFormat
      ).toLowerCase();
      return itemText.indexOf(searchTerm) !== -1;
    });
    this.child.setData(filteredRows);
  };

  render() {
    const { data } = this.state;
    const cols = [
      { name: 'name', header: 'Name', align: 'left' },
      { name: 'email', header: 'Email', align: 'right' },
      { name: 'phoneNumber', header: 'Phone', align: 'right' },
      { name: 'relationFormat', header: 'Relation', align: 'left' }
    ];
    let contents = this.state.loading ? (
      <Loading />
    ) : (
      <Paper>
        <CommonSearchToolbar handleChange={this.handleChange} />
        <CommonDataTable
          onRef={actualChild => (this.child = actualChild)}
          rows={data}
          cols={cols}
          isShowCheckbox={true}
          callbackFromParent={this.callbackValueCheckboxTabl}
        />
      </Paper>
    );
    return <Fragment>{contents}</Fragment>;
  }
}

StudentSelectComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const StudentSelect = withStyles(styles)(StudentSelectComp);
