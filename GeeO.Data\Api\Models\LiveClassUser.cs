using System;
using System.Collections.Generic;

namespace GeeO.Api.Models
{
    public class LiveClassUser
    {
        public string ConnectionId { get; set; }
        public string ClassLessonId { get; set; }
        public string UserRole { get; set; }
        public string UserId { get; set; }
        public string UserName { get; set; }
        public string UserEmail { get; set; }
    }

    public class LiveClassMessage : LiveClassUser
    {
        public string Message { get; set; }
        public DateTime SentTime { get; set; }
        public IEnumerable<string> Recipients { get; set; }
    }
}