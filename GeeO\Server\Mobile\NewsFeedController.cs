﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Mobile.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class NewsFeedController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IFileService _fileService;
        private readonly INewsFeedService _newsFeedService;

        public NewsFeedController(GeeODbContext context,
                                  IFileService fileService,
                                  INewsFeedService newsFeedService)
        {
            _context = context;
            _fileService = fileService;
            _newsFeedService = newsFeedService;
        }

        [HttpPost("[action]/{storyId}/{studentId}/{isLike}")]
        public async Task<ActionResult> Like(string storyId, string studentId, int isLike)
        {
            var story = await _context.GeeOStory.FindAsync(storyId);
            var student = await _context.Student.FindAsync(studentId);

            var likes = story.Likes == null ? new List<StoryLikes>() : JsonConvert.DeserializeObject<List<StoryLikes>>(story.Likes);

            if (isLike == 1)
            {
                if (!likes.Any(x => x.UserId == studentId))
                    likes.Add(new StoryLikes { UserId = studentId, UserName = student.StudentName });
            }
            else
            {
                likes.Remove(likes.First(x => x.UserId == studentId));
            }

            story.Likes = JsonConvert.SerializeObject(likes);
            _context.Entry(story).State = EntityState.Modified;
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok();
        }

        [HttpGet("[action]/{classId}")]
        public async Task<IEnumerable<Story>> GetStories(string classId)
        {
            var newsFeed = await _newsFeedService.GetStories(classId);
            return newsFeed;
        }

        [HttpGet("[action]/{id}/{filename}")]
        public Stream GetForView(string id, string filename)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(filename))
            {
                return null;
            }

            return _fileService.ViewDownloadStoryPhoto(id, filename, false)?.FileStream;
        }
    }
}
