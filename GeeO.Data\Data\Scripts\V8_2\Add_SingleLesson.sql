﻿																							  USE [GeeODb]
GO

/****** Object:  Table [dbo].[SingleLessons]    Script Date: 5/7/2024 10:19:11 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SingleLessons](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](450) NULL,
	[Type] [int] NULL,
	[StartTime] [datetime] NULL,
	[EndTime] [datetime] NULL,
	[IsArchived] [bit] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
	[RoomId] [nvarchar](450) NULL,
	[BranchId] [nvarchar](450) NULL,
 CONSTRAINT [PK__SingleLe__3214EC0784F9EB22] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SingleLessons] ADD  CONSTRAINT [DF__SingleLesson__Id__1699586C]  DEFAULT (newid()) FOR [Id]
GO

ALTER TABLE [dbo].[SingleLessons] ADD  CONSTRAINT [DF__SingleLes__Creat__178D7CA5]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[SingleLessons] ADD  CONSTRAINT [DF__SingleLes__Updat__1881A0DE]  DEFAULT (getdate()) FOR [UpdatedAt]
GO


----------------------

USE [GeeODb]
GO

/****** Object:  Table [dbo].[SingleLessonStudents]    Script Date: 5/7/2024 10:21:11 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SingleLessonStudents](
	[Id] [nvarchar](450) NOT NULL,
	[SingleLessonId] [nvarchar](450) NULL,
	[StudentId] [nvarchar](450) NULL,
 CONSTRAINT [PK__SingleLe__3214EC078C45445E] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SingleLessonStudents] ADD  CONSTRAINT [DF__SingleLesson__Id__2E70E1FD]  DEFAULT (newid()) FOR [Id]
GO

ALTER TABLE [dbo].[SingleLessonStudents]  WITH CHECK ADD  CONSTRAINT [FK__SingleLes__Stude__2F650636] FOREIGN KEY([SingleLessonId])
REFERENCES [dbo].[SingleLessons] ([Id])
GO

ALTER TABLE [dbo].[SingleLessonStudents] CHECK CONSTRAINT [FK__SingleLes__Stude__2F650636]
GO

ALTER TABLE [dbo].[SingleLessonStudents]  WITH CHECK ADD  CONSTRAINT [FK__SingleLes__Stude__30592A6F] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[SingleLessonStudents] CHECK CONSTRAINT [FK__SingleLes__Stude__30592A6F]
GO


----------------------
USE [GeeODb]
GO

/****** Object:  Table [dbo].[SingleLessonTeachers]    Script Date: 5/13/2024 11:07:25 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SingleLessonTeachers](
	[Id] [nvarchar](450) NOT NULL,
	[SingleLessonId] [nvarchar](450) NULL,
	[TeacherId] [nvarchar](450) NULL,
 CONSTRAINT [PK__SingleLe__3214EC07901EB91C] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SingleLessonTeachers] ADD  CONSTRAINT [DF__SingleLesson__Id__29AC2CE0]  DEFAULT (newid()) FOR [Id]
GO

ALTER TABLE [dbo].[SingleLessonTeachers]  WITH CHECK ADD  CONSTRAINT [FK__SingleLes__Teach__2AA05119] FOREIGN KEY([SingleLessonId])
REFERENCES [dbo].[SingleLessons] ([Id])
GO

ALTER TABLE [dbo].[SingleLessonTeachers] CHECK CONSTRAINT [FK__SingleLes__Teach__2AA05119]
GO

ALTER TABLE [dbo].[SingleLessonTeachers]  WITH CHECK ADD  CONSTRAINT [FK__SingleLes__Teach__2B947552] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[SingleLessonTeachers] CHECK CONSTRAINT [FK__SingleLes__Teach__2B947552]
GO



