USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormCommunicates]    Script Date: 3/26/2025 5:38:59 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormCommunicates](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[RootId] [nvarchar](450) NULL,
	[Content] [nvarchar](max) NULL,
	[Attachment] [nvarchar](max) NULL,
	[CreatedDate] [datetime] NULL,
	[IsRead] [bit] NULL,
	[ReadAt] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormCommunicates] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormCommunicates] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormCommunicates]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormCommunicates_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[RequestFormCommunicates] CHECK CONSTRAINT [FK_RequestFormCommunicates_AspNetUsers]
GO

ALTER TABLE [dbo].[RequestFormCommunicates]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormCommunicates_Parent] FOREIGN KEY([RootId])
REFERENCES [dbo].[RequestFormCommunicates] ([Id])
GO

ALTER TABLE [dbo].[RequestFormCommunicates] CHECK CONSTRAINT [FK_RequestFormCommunicates_Parent]
GO

ALTER TABLE [dbo].[RequestFormCommunicates]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormCommunicates_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormCommunicates] CHECK CONSTRAINT [FK_RequestFormCommunicates_RequestForm]
GO


