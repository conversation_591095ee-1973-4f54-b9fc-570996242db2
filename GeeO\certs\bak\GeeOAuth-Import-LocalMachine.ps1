$CertExists = $false;
#Set-Location 'C:/GeeO/certs';
$NewCert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2;
$NewCert.Import('C:/GeeO/certs/GeeOAuth.pfx', 'Gee<PERSON>@Local', 'DefaultKeySet');
Get-ChildItem -Path 'Cert:/LocalMachine/My' | ForEach-Object {
	If ($_.Subject -like $NewCert.Subject -and $_.Thumbprint -eq $NewCert.Thumbprint)
	{
		$CertExists = $true
	}
};
If ($CertExists -eq $false)
{
	Import-PfxCertificate -FilePath 'C:/GeeO/certs/GeeOAuth.pfx' -CertStoreLocation 'Cert:/LocalMachine/My' -Password $(convertto-securestring 'GeeO@Local' -asplaintext -force) | ForEach-Object {
		$_.Thumbprint
	}
}
Else
{
	$NewCert.Thumbprint
}