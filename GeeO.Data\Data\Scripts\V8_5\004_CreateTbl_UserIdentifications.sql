﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[UserIdentifications]    Script Date: 11/25/2024 11:49:10 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[UserIdentifications](
	[Id] [nvarchar](450) NOT NULL PRIMARY KEY,
	[UserInfoId] [nvarchar](450) NULL,
	[CCCD] [nvarchar](450) NULL,
	[DateOfIssue] [datetime] NULL,
	[PlaceOfIssue] [nvarchar](450) NULL,
	[FrontImage] [nvarchar](255) NULL,
	[BackImage] [nvarchar](255) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
)
GO

ALTER TABLE [dbo].[UserIdentifications]  WITH CHECK ADD  CONSTRAINT [FK_UserIdentifications_UserInfo] FOREIGN KEY([UserInfoId])
REFERENCES [dbo].[UserInfo] ([Id])
GO

ALTER TABLE [dbo].[UserIdentifications] CHECK CONSTRAINT [FK_UserIdentifications_UserInfo]
GO

