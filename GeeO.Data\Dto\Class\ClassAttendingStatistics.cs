﻿using System;

namespace GeeO.Data.Dto
{
    public class ClassAttendingStatistics
    {
        public string Id { get; set; }
        public string LessonId { get; set; }
        public DateTime? LessonDate { get; set; }
        public int AttendingCount { get; set; }
        public int MinAttendingCount { get; set; }
        public int MaxAttendingCount { get; set; }
        public int TerminateCount { get; set; }
        public int TerminateOrSuspendedCount { get; set; }
        public int MovedInCount { get; set; }
        public int NewRegisterCount { get; set; }

        public string LessonDateText => LessonDate.HasValue ? LessonDate.Value.ToString("dd/MM/yyyy") : string.Empty;

    }
}
