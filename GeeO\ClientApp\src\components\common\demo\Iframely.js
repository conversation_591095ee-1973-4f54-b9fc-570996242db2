import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';

const stylesIframely = theme => ({
  root: {
    height: '100%',
    margin: theme.spacing(1, 3, 0, 3)
  },
  iframe: {
    width: '100%',
    height: '100%',
    backgroundColor: theme.palette.common.white
  }
});

class Iframely extends Component {
  constructor(...args) {
    super(...args);
    this.state = {
      iframelyEmbedHtmlCode: this.props.src
    };
  }

  componentDidMount() {
    // This is required if you use pure embed.js approach
    // Or if you use lazy-loading of iFrames
    // It initiates the iFrame and adjusts its height if required
    window.iframely && window.iframely.load();
  }

  // Depending on your components, you may need to move iframely.load()
  // from `componentDidMount` to `componentDidUpdate`. Quote from React docs:
  // "Do note that componentDidMount will however not be called on component updates"

  getIframelyHtml() {
    // If you use embed code from API
    return { __html: this.state.iframelyEmbedHtmlCode };

    // Alternatively, if you use plain embed.js approach without API calls:
    // return {__html: '<a href="' + this.url + '" data-iframely-url></a>'};
    // no title inside <a> eliminates the flick

    // but getting actual HTML from our APIs is still recommended
    // as it will have better sizing initially
  }
  render() {
    //const { classes } = this.props;

    return (
      <div dangerouslySetInnerHTML={this.getIframelyHtml()} />
      // <div className={classes.root}>
      //   <iframe
      //     title={this.props.title}
      //     src={this.props.src}
      //     className={classes.iframe}
      //     allow="autoplay; encrypted-media"
      //     width={this.props.width}
      //     height={this.props.height}
      //     //allowFullScreen
      //     //frameBorder="0"
      //     // style={{
      //     //     backgroundColor: '#fff',
      //     //     width: "100%",
      //     //     height: "100%"
      //     //   }}
      //   />
      // </div>
    );
  }
}
Iframely.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(stylesIframely)(Iframely);
