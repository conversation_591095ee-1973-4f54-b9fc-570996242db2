CREATE VIEW View_StudentPayment AS
SELECT std.Id AS StudentId,
	   cls.Id AS ClassId,
	   cls.LevelId,
	   cls.CampusId,
	   (SELECT TOP 1 sc.[Name] FROM StudentCourse sc WHERE sc.StudentId = std.Id ORDER BY sc.StartDate DESC) AS PaymentName,
	   (SELECT SUM(sc.NumberOfSession) FROM StudentCourse sc WHERE sc.StudentId = std.Id) AS NumberOfSessions,
	   (SELECT COUNT(sll.Id) 
	    FROM StudentLessonLogData sll
		JOIN TeacherLessonLog tll ON tll.Id = sll.LogId
		WHERE sll.StudentInfoId = std.Id AND sll.Present > -1 AND tll.HistoryLog = 0) AS StudiedSessions,
	   (SELECT MAX(sc.StartDate) FROM StudentCourse sc WHERE sc.StudentId = std.Id) AS StartDate,
	   (SELECT MAX(sc.EndDate) FROM StudentCourse sc WHERE sc.StudentId = std.Id) AS EndDate,
	   cls.[Name] AS ClassName,
	   std.StudentName,
	   std.EnglishName,
	   cs.StudentType
FROM Student std
JOIN ClassStudent cs on cs.StudentId = std.Id
JOIN ClassCourse cls on cls.Id = cs.ClassId
JOIN Schedule sch on sch.ClassCourseId = cls.Id
WHERE std.SuspendDate IS NULL AND std.TerminateDate IS NULL
  AND sch.EndDate >= CONVERT(date, GETDATE())