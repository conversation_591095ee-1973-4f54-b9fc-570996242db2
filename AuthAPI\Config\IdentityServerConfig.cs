using IdentityServer4;
using IdentityServer4.Models;
using System.Security.Claims;

namespace AuthAPI.Config
{

    public static class IdentityServerConfig
    {
        public static IEnumerable<IdentityResource> GetIdentityResources()
        {
            return new IdentityResource[]
            {
            new IdentityResources.OpenId(),
            new IdentityResources.Email(),
            new IdentityResources.Profile(),
            new IdentityResource("roles", new[] { ClaimTypes.Role }),
            new IdentityResource("name", new[] { ClaimTypes.Name })
            };
        }

        public static IEnumerable<Client> GetClients()
        {
            return new Client[]
            {
                new Client
                {
                    ClientId = "react_client_id",
                    ClientName = "React SPA",
                    RequireClientSecret = false,
                    AllowedGrantTypes = new List<string> { "external_api", "password" },
                    AllowedScopes = new List<string>
                    {
                        "openid", "profile", "email", "roles", "name", "geeo_api", "offline_access",
                        IdentityServerConstants.LocalApi.ScopeName
                    },
                    AllowOfflineAccess = true,
                    AccessTokenLifetime = 86400
                },
                new Client
                {
                    ClientId = "internal_clients",
                    ClientName = "Internal Client Suite",
                    RequireClientSecret = false,
                    AllowedGrantTypes = GrantTypes.ResourceOwnerPassword,
                    AllowedScopes = new List<string>
                    {
                        "openid", "profile", "email", "roles", "name", "geeo_api", "offline_access"
                    },
                    AllowOfflineAccess = true,
                    AccessTokenLifetime = 86400
                },
            };
        }


        public static IEnumerable<ApiScope> GetApiScopes()
        {
            return new[]
            {
                new ApiScope("geeo_api", "GeeO API Scope")
            };
        }

        public static IEnumerable<ApiResource> GetApiResources()
        {
            return new[]
            {
                new ApiResource("geeo_api", "GeeO API")
                {
                    Scopes = { "geeo_api" }
                }
            };
        }
    }
}