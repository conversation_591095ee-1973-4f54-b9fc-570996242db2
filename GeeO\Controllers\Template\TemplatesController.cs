﻿using DocumentFormat.OpenXml.Office2021.DocumentTasks;
using GeeO.Data.Dto.Template;
using GeeO.Model.Template.Response;
using GeeO.Models.Template.Request;
using GeeO.Models.Template.Response;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers.Template
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TemplatesController : ControllerBase
    {
        private readonly ITemplateService _templateService;
        public TemplatesController(ITemplateService templateService)
        {
            _templateService = templateService;
        }

        [HttpGet("list")]
        public async Task<IActionResult> Get()
        {
            try
            {
                List<TemplateDto> result = await _templateService.GetAll();
                var response = result.Select(x => new TemplateListResponse
                {
                    Id = x.Id,
                    TemplateId = x.TemplateId,
                    FunctionName = x.FunctionName,
                    TemplateName = x.TemplateName,
                    Notes = x.Notes
                }).ToList();
                return Ok(new { Status = StatusCodes.Status200OK, Data = response });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            try
            {
                TemplateDto result = await _templateService.GetById(id);
                var response = new TemplateGetResponse
                {
                    Id = result.Id,
                    TemplateId = result.TemplateId,
                    FunctionName = result.FunctionName,
                    TemplateName = result.TemplateName,
                    Notes = result.Notes
                };
                return Ok(new { Status = StatusCodes.Status200OK, Data = response });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet()]
        public async Task<IActionResult> GetByFunctionName(string functionName)
        {
            try
            {
                var template = await _templateService.GetByFunctionName(functionName);
                if (template == null)
                {
                    return NotFound();
                }
                return Ok(new { Status = StatusCodes.Status200OK, Data = template });
            }
            catch(Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] TemplatePostRequest templatePostRequest)
        {
            try
            {
                var templatePostDto = new TemplatePostDto
                {
                    TemplateId = templatePostRequest.TemplateId,
                    TemplateName = templatePostRequest.TemplateName,
                    Notes = templatePostRequest.Notes
                };
                var templateId = await _templateService.Create(templatePostDto);
                var result = await _templateService.GetById(templateId);
                var response = new TemplatePostResponse
                {
                    Id = result.Id,
                    TemplateId = result.TemplateId,
                    FunctionName = result.FunctionName,
                    TemplateName = result.TemplateName,
                    Notes = result.Notes
                };
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Put(string id, [FromBody] TemplateUpdateRequest templateUpdateRequest)
        {
            try
            {
                var templateDto = new TemplateDto
                {
                    TemplateId = templateUpdateRequest.TemplateId,
                    FunctionName = templateUpdateRequest.FunctionName,
                    TemplateName = templateUpdateRequest.TemplateName,
                    Notes = templateUpdateRequest.Notes
                };
                var result = await _templateService.Update(id,templateDto);
                var response = new TemplateUpdateResponse
                {
                    Id = result.Id,
                    TemplateId = result.TemplateId,
                    FunctionName = result.FunctionName,
                    TemplateName = result.TemplateName,
                    Notes = result.Notes
                };
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
        
    }
}
