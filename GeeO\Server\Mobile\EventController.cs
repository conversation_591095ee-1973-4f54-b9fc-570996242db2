﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Mobile.Models;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class EventController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public EventController(GeeODbContext context)
        {
            _context = context;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<IEnumerable<EventModel>> GetThisWeekUpcomingEvents(string studentId)
        {
            DateTime sunday = DateUtils.GetSundayThisWeek().AddHours(23).AddMinutes(23).AddSeconds(59);

            var lessonEvents = from cl in _context.ClassLesson.Where(cl => cl.EndTime > DateTime.Today && cl.EndTime < sunday)
                               from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                               from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                               from cps in _context.Campus.Where(cps => cls.CampusId == cps.Id)
                               from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                               from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today && sch.EndedDate == null)
                               orderby sch.StartDate descending, cl.StartTime
                               select new EventModel()
                               {
                                   EventTitle = string.Empty,
                                   EventContent = string.Empty,
                                   StartTime = cl.StartTime.Value,
                                   EndTime = cl.EndTime.Value,
                                   ClassName = cls.Name,
                                   Lesson = lp.Lesson,
                                   CampusName = cps.Name,
                                   CampusAddress = cps.Address,
                                   IsToday = cl.StartTime.Value.Date == DateTime.Today
                               };
            var classEvents = from evn in _context.Events.Where(evn => evn.StartTime > DateTime.Today && evn.StartTime < sunday)
                              from cls in _context.ClassCourse.Where(cls => evn.ClassId == cls.Id)
                              from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                              from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                              orderby sch.StartDate descending, evn.StartTime
                              select new EventModel()
                              {
                                  EventTitle = evn.Title,
                                  EventContent = evn.Content,
                                  StartTime = evn.StartTime,
                                  EndTime = evn.EndTime,
                                  ClassName = string.Empty,
                                  Lesson = string.Empty,
                                  CampusName = string.Empty,
                                  CampusAddress = string.Empty,
                                  IsToday = evn.StartTime.Date == DateTime.Today
                              };
            var lessonEventList = await lessonEvents.ToListAsync().ConfigureAwait(false);
            var classEventList = await classEvents.ToListAsync().ConfigureAwait(false);
            return lessonEventList.Union(classEventList).OrderBy(x => x.StartTime);
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<IEnumerable<EventModel>> GetAllEvents(string studentId)
        {
            var lessonEvents = from cl in _context.ClassLesson/*.Where(cl => cl.StartTime.Value.Date >= DateTime.Today.AddDays(-7))*/
                               from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                               from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                               from cps in _context.Campus.Where(cps => cls.CampusId == cps.Id)
                               from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                               from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                               orderby sch.StartDate descending, cl.StartTime
                               select new EventModel()
                               {
                                   EventTitle = string.Empty,
                                   EventContent = string.Empty,
                                   StartTime = cl.StartTime.Value,
                                   EndTime = cl.EndTime.Value,
                                   ClassName = cls.Name,
                                   Lesson = lp.Lesson,
                                   CampusName = cps.Name,
                                   CampusAddress = cps.Address,
                                   IsToday = cl.StartTime.Value.Date == DateTime.Today
                               };
            var classEvents = from evn in _context.Events/*.Where(evn => evn.StartTime.Date >= DateTime.Today.AddDays(-7))*/
                              from cls in _context.ClassCourse.Where(cls => evn.ClassId == cls.Id)
                              from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                              from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                              orderby sch.StartDate descending, evn.StartTime
                              select new EventModel()
                              {
                                  EventTitle = evn.Title,
                                  EventContent = evn.Content,
                                  StartTime = evn.StartTime,
                                  EndTime = evn.EndTime,
                                  ClassName = string.Empty,
                                  Lesson = string.Empty,
                                  CampusName = string.Empty,
                                  CampusAddress = string.Empty,
                                  IsToday = evn.StartTime.Date == DateTime.Today
                              };
            var lessonEventList = await lessonEvents.ToListAsync().ConfigureAwait(false);
            var classEventList = await classEvents.ToListAsync().ConfigureAwait(false);
            return lessonEventList.Union(classEventList).OrderBy(x => x.StartTime);
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<IEnumerable<EventModel>> GetTeacherEvents(string teacherId)
        {
            var lessonEvents = from cl in _context.ClassLesson.Where(cl => cl.StartTime.Value.Date >= DateTime.Today.AddDays(-7))
                               from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                               from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                               from cps in _context.Campus.Where(cps => cls.CampusId == cps.Id)
                               from cs in _context.ClassTeacher.Where(cs => cls.Id == cs.ClassId && cs.TeacherId == teacherId)
                               from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                               orderby sch.StartDate descending, cl.StartTime
                               select new EventModel()
                               {
                                   EventTitle = string.Empty,
                                   EventContent = string.Empty,
                                   StartTime = cl.StartTime.Value,
                                   EndTime = cl.EndTime.Value,
                                   ClassName = cls.Name,
                                   Lesson = lp.Lesson,
                                   CampusName = cps.Name,
                                   CampusAddress = cps.Address,
                                   IsToday = cl.StartTime.Value.Date == DateTime.Today
                               };
            var classEvents = from evn in _context.Events.Where(evn => evn.StartTime.Date >= DateTime.Today.AddDays(-7))
                              from cls in _context.ClassCourse.Where(cls => evn.ClassId == cls.Id)
                              from cs in _context.ClassTeacher.Where(cs => cls.Id == cs.ClassId && cs.TeacherId == teacherId)
                              from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                              orderby sch.StartDate descending, evn.StartTime
                              select new EventModel()
                              {
                                  EventTitle = evn.Title,
                                  EventContent = evn.Content,
                                  StartTime = evn.StartTime,
                                  EndTime = evn.EndTime,
                                  ClassName = string.Empty,
                                  Lesson = string.Empty,
                                  CampusName = string.Empty,
                                  CampusAddress = string.Empty,
                                  IsToday = evn.StartTime.Date == DateTime.Today
                              };
            var lessonEventList = await lessonEvents.ToListAsync().ConfigureAwait(false);
            var classEventList = await classEvents.ToListAsync().ConfigureAwait(false);
            return lessonEventList.Union(classEventList).OrderBy(x => x.StartTime);
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<IEnumerable<EventModel>> GetTeacherWeekUpcomeEvents(string teacherId)
        {
            DateTime sunday = DateUtils.GetSundayThisWeek().AddHours(23).AddMinutes(23).AddSeconds(59);

            var lessonEvents = from cl in _context.ClassLesson.Where(cl => cl.EndTime > DateTime.Today && cl.EndTime < sunday)
                               from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                               from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                               from cps in _context.Campus.Where(cps => cls.CampusId == cps.Id)
                               from cs in _context.ClassTeacher.Where(cs => cls.Id == cs.ClassId && cs.TeacherId == teacherId)
                               from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                               orderby sch.StartDate descending, cl.StartTime
                               select new EventModel()
                               {
                                   EventTitle = string.Empty,
                                   EventContent = string.Empty,
                                   StartTime = cl.StartTime.Value,
                                   EndTime = cl.EndTime.Value,
                                   ClassName = cls.Name,
                                   Lesson = lp.Lesson,
                                   CampusName = cps.Name,
                                   CampusAddress = cps.Address,
                                   IsToday = cl.StartTime.Value.Date == DateTime.Today
                               };
            var classEvents = from evn in _context.Events.Where(evn => evn.StartTime > DateTime.Today && evn.StartTime < sunday)
                              from cls in _context.ClassCourse.Where(cls => evn.ClassId == cls.Id)
                              from cs in _context.ClassTeacher.Where(cs => cls.Id == cs.ClassId && cs.TeacherId == teacherId)
                              from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                              orderby sch.StartDate descending, evn.StartTime
                              select new EventModel()
                              {
                                  EventTitle = evn.Title,
                                  EventContent = evn.Content,
                                  StartTime = evn.StartTime,
                                  EndTime = evn.EndTime,
                                  ClassName = string.Empty,
                                  Lesson = string.Empty,
                                  CampusName = string.Empty,
                                  CampusAddress = string.Empty,
                                  IsToday = evn.StartTime.Date == DateTime.Today
                              };
            var lessonEventList = await lessonEvents.ToListAsync().ConfigureAwait(false);
            var classEventList = await classEvents.ToListAsync().ConfigureAwait(false);
            return lessonEventList.Union(classEventList).OrderBy(x => x.StartTime);
        }

    }
}
