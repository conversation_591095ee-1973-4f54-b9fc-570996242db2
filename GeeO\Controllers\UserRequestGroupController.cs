
using System;
using System.Threading.Tasks;
using AutoMapper;
using Geeo.Data.Models;
using GeeO.Data.Dto;
using GeeO.Model;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserRequestGroupController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IUserRequestGroupService _userRequestGroupService;
        public UserRequestGroupController(IUserRequestGroupService userRequestGroupService, IMapper mapper)
        {
            _userRequestGroupService = userRequestGroupService;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var result = await _userRequestGroupService.GetAll();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var result = await _userRequestGroupService.GetById(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] GroupViewModel viewModel)
        {
            try
            {
                var id = await _userRequestGroupService.Create(_mapper.Map<GroupDto>(viewModel));
                return Ok(((OkObjectResult)await GetById(id)).Value);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("add-user-groups")]
        public async Task<IActionResult> AddUserGroups([FromBody] AddUserGroupRequest request)
        {
            try
            {
                var result = await _userRequestGroupService.AddUserGroups(request.Id, request.UserIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-user-groups/{id}")]
        public async Task<IActionResult> GetUserGroups(string id)
        {
            try
            {
                var result = await _userRequestGroupService.GetUserGroups(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("remove-groups/{id}")]
        public async Task<IActionResult> RemoveGroup(string id)
        {
            try
            {
                var result = await _userRequestGroupService.RemoveGroup(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("remove-user-groups")]
        public async Task<IActionResult> RemoveUserGroups(string id, string userId)
        {
            try
            {
                var result = await _userRequestGroupService.RemoveUserGroups(id, userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("mark-admin")]
        public async Task<IActionResult> MarkAdmin(string id, string userId)
        {
            try
            {
                var result = await _userRequestGroupService.MarkAdmin(id, userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}