USE [GeeODb]
GO

/****** Object:  Table [dbo].[TeacherLessonLogData_LOAD]    Script Date: 11-Oct-20 9:53:28 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TeacherLessonLogData_LOAD](
	[Id] [nvarchar](450) NOT NULL,
	[LogId] [nvarchar](450) NOT NULL,
	[UnitId] [nvarchar](450) NULL,
	[HistoryUnitId] [nvarchar](450) NULL,
	[Duration] [int] NULL,
	[Note] [nvarchar](1024) NULL,
 CONSTRAINT [PK_TeacherLessonLogData_V31] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLogData_LogId_V31] FOREIGN KEY([LogId])
REFERENCES [dbo].[TeacherLessonLog] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD] CHECK CONSTRAINT [FK_TeacherLessonLogData_LogId_V31]
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLogData_UnitId_V31] FOREIGN KEY([UnitId])
REFERENCES [dbo].[LessonPlanUnit] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD] CHECK CONSTRAINT [FK_TeacherLessonLogData_UnitId_V31]
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLogData_HistoryUnitId_V31] FOREIGN KEY([HistoryUnitId])
REFERENCES [dbo].[LessonPlanUnitHistory] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonLogData_LOAD] CHECK CONSTRAINT [FK_TeacherLessonLogData_HistoryUnitId_V31]
GO



