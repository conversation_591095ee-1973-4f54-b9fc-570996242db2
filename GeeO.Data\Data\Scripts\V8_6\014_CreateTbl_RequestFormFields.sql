USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormFields]    Script Date: 3/26/2025 5:40:12 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormFields](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[FieldName] [nvarchar](100) NULL,
	[TypeInput] [int] NULL,
	[InputKey] [varchar](200) NULL,
	[InputMask] [varchar](200) NULL,
	[InitialData] [nvarchar](max) NULL,
	[DefaultDataSource] [nvarchar](max) NULL,
	[IsRequired] [bit] NULL,
	[DisplayOrder] [int] NULL,
	[IsHiddenField] [bit] NULL,
	[IsDeactivate] [bit] NULL,
	[FormStatus] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormFields] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormFields] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormFields]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormFields_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormFields] CHECK CONSTRAINT [FK_RequestFormFields_RequestForm]
GO