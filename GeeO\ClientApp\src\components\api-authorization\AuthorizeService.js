import { jwtDecode } from 'jwt-decode';
import cookie from 'react-cookies';

export const CookieNames = {
  AUTH: 'GEEO.AUTH',
  USER: 'GEEO.USER'
};

const _callbacks = [];

function getDecodedToken(token) {
  try {
    return jwtDecode(token);
  } catch (err) {
    console.warn('[authService] Lỗi giải mã token:', err);
    return null;
  }
}

function getAuth() {
  return cookie.load(CookieNames.AUTH);
}

function getAccessToken() {
  const auth = getAuth();
  return auth?.access_token || null;
}

function isAuthenticated() {
  const token = getAccessToken();
  if (!token) return false;

  const payload = getDecodedToken(token);
  if (!payload) return false;

  const now = Math.floor(Date.now() / 1000);
  return payload.exp > now;
}

function getUser() {
  const token = getAccessToken();
  const payload = getDecodedToken(token);
  return payload || null;
}

function signOut() {
  cookie.remove(CookieNames.AUTH);
  cookie.remove(CookieNames.USER);

  notifySubscribers();
}

function subscribe(callback) {
  const id = _callbacks.length;
  _callbacks.push(callback);
  return id;
}

function unsubscribe(id) {
  _callbacks.splice(id, 1);
}

function notifySubscribers() {
  _callbacks.forEach(cb => {
    try {
      cb();
    } catch (e) {
      console.error('[authService] Lỗi callback:', e);
    }
  });
}

const authService = {
  getAuth,
  getAccessToken,
  isAuthenticated,
  getUser,
  signOut,
  subscribe,
  unsubscribe
};

export default authService;
