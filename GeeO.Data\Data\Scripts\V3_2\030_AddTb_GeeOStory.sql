USE [GeeODb]
GO

/****** Object:  Table [dbo].[Gee<PERSON><PERSON>]    Script Date: 21-Mar-21 6:37:26 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[GeeOS<PERSON>](
	[Id] [nvarchar](450) NOT NULL,
	[ClassId] [nvarchar](450) NULL,
	[AuthorId] [nvarchar](450) NULL,
	[ImageName] [nvarchar](1000) NULL,
	[Caption] [nvarchar](1000) NULL,
	[Text] [nvarchar](MAX) NULL,
	[Likes] [nvarchar](MAX) NULL,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_GeeOStory] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[GeeOStory]  WITH CHECK ADD  CONSTRAINT [FK_GeeOStory_AuthorId] FOREIGN KEY([AuthorId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO
ALTER TABLE [dbo].[GeeOStory] CHECK CONSTRAINT [FK_GeeOStory_AuthorId]
GO

ALTER TABLE [dbo].[GeeOStory]  WITH CHECK ADD  CONSTRAINT [FK_GeeOStory_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
GO
ALTER TABLE [dbo].[GeeOStory] CHECK CONSTRAINT [FK_GeeOStory_ClassId]
GO

-- add columns
ALTER TABLE dbo.GeeOStory ADD
	[ClassId] [nvarchar](450) NULL,
	Likes nvarchar(MAX) NULL
GO

ALTER TABLE [dbo].[GeeOStory]  WITH CHECK ADD  CONSTRAINT [FK_GeeOStory_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
GO
ALTER TABLE [dbo].[GeeOStory] CHECK CONSTRAINT [FK_GeeOStory_ClassId]
GO


/****** Object:  Table [dbo].[GeeOStoryComment]    Script Date: 21-Mar-21 6:37:26 AM ******/

CREATE TABLE [dbo].[GeeOStoryComment](
	[Id] [nvarchar](450) NOT NULL,
	[StoryId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[Text] [nvarchar](MAX) NULL,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_GeeOStoryComment] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[GeeOStoryComment]  WITH CHECK ADD  CONSTRAINT [FK_GeeOStoryComment_StoryId] FOREIGN KEY([StoryId])
REFERENCES [dbo].[GeeOStory] ([Id])
GO
ALTER TABLE [dbo].[GeeOStoryComment] CHECK CONSTRAINT [FK_GeeOStoryComment_StoryId]
GO

ALTER TABLE [dbo].[GeeOStoryComment]  WITH CHECK ADD  CONSTRAINT [FK_GeeOStoryComment_UserId] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO
ALTER TABLE [dbo].[GeeOStoryComment] CHECK CONSTRAINT [FK_GeeOStoryComment_UserId]
GO



