USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplate]    Script Date: 3/26/2025 5:20:38 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplate](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](250) NULL,
	[FormCollectionId] [nvarchar](450) NULL,
	[RoleId] [nvarchar](450) NULL,
	[ExpirationCycle] [int] NULL,
	[Description] [nvarchar](500) NULL,
	[Deactivate] [bit] NULL,
	[FormStatus] [int] NULL,
	[CreatedDate] [datetime] NULL,
	[LastModified] [datetime] NULL,
	[ExpiratedDate] [datetime] NULL,
	[TemplateType] [varchar](50) NULL,
	[IsPublish] [bit] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplate] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FormTemplate] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FormTemplate]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplate_AspNetRoles] FOREIGN KEY([RoleId])
REFERENCES [dbo].[AspNetRoles] ([Id])
GO

ALTER TABLE [dbo].[FormTemplate] CHECK CONSTRAINT [FK_FormTemplate_AspNetRoles]
GO

ALTER TABLE [dbo].[FormTemplate]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplate_FormCollection] FOREIGN KEY([FormCollectionId])
REFERENCES [dbo].[FormCollection] ([Id])
GO

ALTER TABLE [dbo].[FormTemplate] CHECK CONSTRAINT [FK_FormTemplate_FormCollection]
GO


