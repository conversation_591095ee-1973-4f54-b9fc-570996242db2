DECLARE @CheckDay DateTime = CONVERT(date, GETDATE())

UPDATE Student SET SuspendDate = @CheckDay, SuspendReason = N'System - Not join any new class'
--SELECT * FROM Student
WHERE Id In 
(SELECT Id FROM
(
SELECT std.Id
	  ,std.StudentName
	  ,std.EnglishName
	  ,std.SuspendDate
	  ,std.TerminateDate
	  ,newest.MaxEndDate
  FROM Student std 
  JOIN
  (
	SELECT cs.StudentId, MAX([EndDate]) as MaxEndDate
	FROM [ClassStudent] cs
	JOIN [Schedule] sch ON cs.[ClassId] = sch.[ClassCourseId]
	GROUP BY cs.StudentId
  ) newest on newest.StudentId = std.Id
  WHERE std.SuspendDate IS NULL AND std.TerminateDate IS NULL AND newest.MaxEndDate < @CheckDay
) t)