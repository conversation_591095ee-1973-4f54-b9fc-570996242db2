USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormActivityLogs]    Script Date: 3/26/2025 5:37:54 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormActivityLogs](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[FormStatus] [int] NULL,
	[CreatedDate] [datetime] NULL,
	[Note] [nvarchar](100) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormActivityLogs] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormActivityLogs] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormActivityLogs]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormActivityLogs_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[RequestFormActivityLogs] CHECK CONSTRAINT [FK_RequestFormActivityLogs_AspNetUsers]
GO

ALTER TABLE [dbo].[RequestFormActivityLogs]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormActivityLogs_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormActivityLogs] CHECK CONSTRAINT [FK_RequestFormActivityLogs_RequestForm]
GO
