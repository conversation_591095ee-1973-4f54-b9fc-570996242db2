﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace GeeO.Common
{
    public class ObjectUtils
    {
        public static T CopyObject<T>(object objSource)
        {
            T objTarget = Activator.CreateInstance<T>();

            Type typeSource = objSource.GetType();
            Type typeTarget = objTarget.GetType();

            foreach (PropertyInfo sourceProperty in typeSource.GetProperties())
            {
                PropertyInfo targetProperty = typeTarget.GetProperty(sourceProperty.Name);
                targetProperty?.SetValue(objTarget, sourceProperty.GetValue(objSource));
            }

            return objTarget;
        }

        public static object CopyProperties(object source, object target)
        {
            var typeSource = source.GetType();
            var typeTarget = target.GetType();
            foreach (var sourceProperty in typeSource.GetProperties())
            {
                var targetProperty = typeTarget.GetProperty(sourceProperty.Name);
                targetProperty?.SetValue(target, sourceProperty.GetValue(source));
            }
            //foreach (var sourceField in type.GetFields())
            //{
            //    var targetField = typeTarget.GetField(sourceField.Name);
            //    targetField?.SetValue(target, sourceField.GetValue(source));
            //}

            return target;
        }
    }
}
