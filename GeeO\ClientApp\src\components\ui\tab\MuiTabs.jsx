import React from 'react';
import { Tabs, Tab } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';

export const MuiTabs = withStyles(theme => ({
  root: {
    '& .MuiTab-root': {
      textTransform: 'none',
      minHeight: '1.563rem',
      height: '1.5rem',
      width: 'auto',
      letterSpacing: '1.8px',
      color: '#13273B',
      opacity: 0.5,
      fontFamily: 'SVNNexaRegular',
      fontSize: '1.125rem',
      fontWeight: 'bold',
      paddingLeft: '1.25rem',
      paddingRight: '1.25rem'
    },
    '& .Mui-selected': {
      opacity: 1
    }
  },
  indicator: {
    background: theme.palette.background.paper
  }
}))(props => <Tabs {...props} />);

export const MuiTab = withStyles(theme => ({
  root: {
    borderRight: '2px solid #0054A6 !important'
  }
}))(props => <Tab {...props} />);

export const IdentifyTabs = withStyles(theme => ({
  root: {
    '& .MuiTab-root': {
      minHeight: '2.188rem',
      height: '0.188rem',
      letterSpacing: '0.113rem',
      color: '#13273B',
      opacity: 0.5,
      fontFamily: 'SVNNexaHeavy',
      fontSize: '1rem',
      paddingLeft: '3.063rem',
      paddingRight: '3.063rem',
      width: '20rem',
      maxWidth: '20rem',
      background: '#ececec'
    },
    '& .MuiTabs-root': {
      background: '#F3F3F3',
      minHeight: '0px'
    },
    '& .Mui-selected': {
      opacity: 1,
      color: '#0054A6',
      background: 'rgb(236, 236, 236)',
      borderRight: '2px solid #0054A6 !important'
    }
  },
  indicator: {
    background: theme.palette.background.paper
  }
}))(props => <Tabs {...props} />);

export const DoorTabs = withStyles(theme => ({
  root: {
    minHeight: '0px !important',
    '& .MuiTab-root': {
      textTransform: 'none',
      height: '1.5rem',
      minHeight: '1.5rem',
      width: 'auto',
      letterSpacing: '0.8px',
      color: '#13273B',
      opacity: 0.5,
      fontFamily: 'SVNNexaHeavy',
      fontSize: '1rem',
      minWidth: '90px'
    },
    '& .MuiTabs-root': {},
    '& .Mui-selected': {
      opacity: 1
    }
  },
  indicator: {
    background: theme.palette.background.paper
  }
}))(props => <Tabs {...props} />);
