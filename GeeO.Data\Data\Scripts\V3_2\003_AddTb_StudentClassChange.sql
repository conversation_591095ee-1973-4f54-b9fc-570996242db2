USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentClassChange]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentClassChange](
	[Id] [nvarchar](450) NOT NULL,
	[StudentId] [nvarchar](450) NOT NULL,
	ClassChangeDate datetime NOT NULL,
	PreviousClassId [nvarchar](450) NULL,
 CONSTRAINT [PK_StudentClassChange] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentClassChange]  WITH CHECK ADD  CONSTRAINT [FK_StudentClassChange_StudentId] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO
ALTER TABLE [dbo].[StudentClassChange] CHECK CONSTRAINT [FK_StudentClassChange_StudentId]
GO

ALTER TABLE [dbo].[StudentClassChange]  WITH CHECK ADD  CONSTRAINT [FK_StudentClassChange_PreviousClassId] FOREIGN KEY([PreviousClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
GO
ALTER TABLE [dbo].[StudentClassChange] CHECK CONSTRAINT [FK_StudentClassChange_PreviousClassId]
GO

