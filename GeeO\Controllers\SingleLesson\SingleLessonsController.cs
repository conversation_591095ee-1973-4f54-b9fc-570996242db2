﻿using GeeO.Services.Interfaces;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using System;
using GeeO.Data.Dto.SingleLesson;
using GeeO.Services;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace GeeO.Controllers.SingleLesson
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class SingleLessonsController : ControllerBase
    {
        private readonly ISingleLessonService _singleLessonService;
        private readonly IWorkLogService _workLogService;

        public SingleLessonsController(ISingleLessonService singleLessonService, IWorkLogService workLogService)
        {
            _singleLessonService = singleLessonService;
            _workLogService = workLogService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {   
                var userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<SingleLessonDto> result = await _singleLessonService.GetAll(userId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            try
            {
                SingleLessonDto result = await _singleLessonService.GetById(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Put(string id, SingleLessonDto singleLessonDto)
        {
            try
            {
                SingleLessonDto result = await _singleLessonService.Update(id, singleLessonDto);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Post(SingleLessonDto singleLessonDto)
        {
            try
            {
                string singleLessonId = await _singleLessonService.Create(singleLessonDto);
                var result = await _singleLessonService.GetById(singleLessonId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                bool singleLessonId = await _singleLessonService.Delete(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = singleLessonId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("{id}/archive")]
        public async Task<IActionResult> UpdateArchive(string id, [FromBody] bool isArchived)
        {
            try
            {
                bool result = await _singleLessonService.UpdateArchive(id, isArchived);
                return Ok(new { status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}/check-approved")]
        public async Task<IActionResult> CheckApprovedStatus(string id)
        {
            try
            {
                bool result = await _workLogService.CheckApprovedStatus(id);
                return Ok(new { status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

    }
}
