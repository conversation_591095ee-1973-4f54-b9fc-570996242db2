﻿using GeeO.Data;
using GeeO.Data.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LessonReportController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public LessonReportController(GeeODbContext context)
        {
            _context = context;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<ClassLessonStatistics>> GetClassLessonStatistics(string classId)
        {
            var lessonTimes = await (from cl in _context.ClassLesson.Where(cl => cl.ClassId == classId && cl.EndTime.Value < DateTime.Now)
                                     from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id /*&& tll.HistoryLog == false*/).DefaultIfEmpty()
                                     orderby cl.StartTime
                                     select new ClassLessonStatistics()
                                     {
                                         Id = cl.Id,
                                         Lesson = cl.Lesson.Lesson,
                                         Status = tll != null ? "recorded" : string.Empty,
                                         LessonDate = cl.StartTime,
                                         StandardLessonTime = cl.Lesson.Level.LessonTime.Value,
                                         LessonDurationInSeconds = tll.LogDatas.Select(ld => ld.Duration).Sum()
                                     })
                                    .ToListAsync().ConfigureAwait(false);

            ClassLessonStatistics classLessonStatistics = new()
            {
                NumberOfLessons = lessonTimes.Count(),
                NumberOfLate = lessonTimes.Count(x => x.LessonDuration > x.StandardLessonTime),
                NumberOfEarly = lessonTimes.Count(x => x.LessonDuration < x.StandardLessonTime),
                LessonTimeData = lessonTimes
            };

            return classLessonStatistics;
        }

    }
}
