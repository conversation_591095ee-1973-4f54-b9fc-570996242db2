﻿using GeeO.Data.Dto.WorkingTime;
using GeeO.Model.WorkingTime;
using GeeO.Services;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Net.Mime;
using System.Threading.Tasks;

namespace GeeO.Controllers.WorkingTime
{
    [Route("api/[controller]")]
    [ApiController]
    public class WorkingTimeReportController : ControllerBase
    {
        private readonly IWorkingTimeService _workingTimeService;
        private readonly IWorkLogService _workLogService;

        public WorkingTimeReportController(IWorkingTimeService workingTimeService, IWorkLogService workLogService)
        {
            _workingTimeService = workingTimeService;
            _workLogService = workLogService;
        }

        [HttpPost("summary")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAll(GetTimebookRequest request)
        {
            try
            {
                List<SummaryReportTeacherTimebook> result = await _workingTimeService.GetReportSummaryByOwner(request.DateRange.From, request.DateRange.To, request.OwnerId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("access-time")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAccessTime(GetTimebookRequest request)
        {
            try
            {
                var result = await _workingTimeService.GetReportAccessTime(request.OwnerId, request.DateRange.From, request.DateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("worklog")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetWorkLogsByOwner(GetTimebookRequest request)
        {
            try
            {
                var result = await _workLogService.GetReportAllByOwner(request.DateRange.From, request.DateRange.To, request.OwnerId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}
