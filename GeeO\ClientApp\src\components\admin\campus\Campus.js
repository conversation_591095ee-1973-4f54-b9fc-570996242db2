import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import CampusList from './CampusList';
import CampusEdit from './CampusEdit';
import { CampusActions } from './CampusConstants';
import { AdminPage } from '../../ui/page/AdminPage';

class Campus extends Component {
  constructor(...args) {
    super(...args);
    this.state = {
      campusId: this.props.match.params.campusId,
      content: null,
      pageTitle: ''
    };
    switch (this.props.action) {
      case CampusActions.List:
        this.state.pageTitle = 'Branch';
        this.state.content = <CampusList />;
        break;
      case CampusActions.Create:
        this.state.pageTitle = 'Create Branch';
        this.state.content = <CampusEdit action={this.props.action} />;
        break;
      case CampusActions.Edit:
        this.state.pageTitle = 'Edit Branch';
        this.state.content = (
          <CampusEdit
            action={this.props.action}
            campusId={this.state.campusId}
          />
        );
        break;
      default:
    }
  }

  render() {
    return (
      <AdminPage title={this.state.pageTitle} content={this.state.content} />
    );
  }
}

const stylesCampus = () => ({});

Campus.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(stylesCampus)(Campus);
