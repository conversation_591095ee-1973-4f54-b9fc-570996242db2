﻿using GeeO.Data.Dto.WorkingTime;
using GeeO.Data.Models;
using GeeO.Model;
using GeeO.Model.WorkingTime;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mime;
using System.Security.Claims;
using System.Threading.Tasks;

namespace GeeO.Controllers.WorkingTime
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public class WorkingTimeController : ControllerBase
    {
        private readonly IWorkingTimeService _workingTimeService;

        public WorkingTimeController(IWorkingTimeService workingTimeService)
        {
            _workingTimeService = workingTimeService;
        }

        [HttpPost("list")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetAll(DateRange dateRange)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<WorkingTimeDto> result = await _workingTimeService.GetAll(userId, dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("teachers-working")]
        public async Task<IActionResult> GetWorkingTimeTeachers(DateRange dateRange)
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<TeacherWorkingTime> result = await _workingTimeService.GetWorkingTimeTeachers(userId, dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("owner")]
        [Consumes(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> GetByOwner([FromBody] GetTimebookRequest timebookRequest)
        {
            try
            {
                var userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                List<WorkingTimeDto> result = await _workingTimeService.GetByOwner(timebookRequest.DateRange.From, timebookRequest.DateRange.To, string.IsNullOrEmpty(timebookRequest.OwnerId) ? userId : timebookRequest.OwnerId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            try
            {
                WorkingTimeDto result = await _workingTimeService.GetById(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("staff/{id}")]
        public async Task<IActionResult> GetByStaff(string id)
        {
            try
            {
                List<WorkingTimeDto> result = await _workingTimeService.GetByStaff(id);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("in")]
        public async Task<ActionResult<WorkingTimeDto>> CheckIn()
        {
            try
            {
                if (!HttpContext.Request.Headers.TryGetValue("X-Client-IP", out var clientIPHeader))
                    return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = "X-Client-IP header not provided" });

                string clientIP = clientIPHeader.FirstOrDefault();
                var campus = await _workingTimeService.GetCampusByClientIP(clientIP);

                if (campus == null)
                    return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = "Invalid client IP" });

                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                WorkingTimeDto entity = await _workingTimeService.CheckIn(userId, campus.Id);
                return Ok(new { Status = StatusCodes.Status201Created, Body = entity });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("out")]
        public async Task<IActionResult> CheckOut()
        {
            try
            {
                if (!HttpContext.Request.Headers.TryGetValue("X-Client-IP", out var clientIPHeader))
                    return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = "X-Client-IP header not provided." });

                string clientIP = clientIPHeader.FirstOrDefault();
                var campus = await _workingTimeService.GetCampusByClientIP(clientIP);

                if (campus == null)
                    return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = "Invalid client IP." });

                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                bool result = await _workingTimeService.CheckOut(userId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("checked-in-today")]
        public async Task<IActionResult> IsCheckedInToday()
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                bool value = await _workingTimeService.IsCheckedInToday(userId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = value });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("ave-time")]
        public async Task<IActionResult> GeAverageTime()
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                AverageTimeDto averageTime = await _workingTimeService.GeAverageTime((string)userId);
                return Ok(new { Status = StatusCodes.Status200OK, Body = averageTime });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("access-summary")]
        public async Task<IActionResult> GetSummaryByMonth([FromBody] DateRange dateRange)
        {
            try
            {
                AccessTimeSummary summary = await _workingTimeService.GetAccessSummaryByMonth(dateRange.From, dateRange.To);
                return Ok(new { Status = StatusCodes.Status200OK, Body = summary });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("validate/{ip}")]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ValidateIP(string ip)
        {
            try
            {
                bool result = await _workingTimeService.ValidateIP(ip);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("update-status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> UpdateStatus(StatusRequest request)
        {
            try
            {
                bool result = await _workingTimeService.UpdateStatus(request.Id, request.Status);
                return Ok(new { Status = StatusCodes.Status200OK, Body = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}
