using System;
using System.Threading.Tasks;
using AutoMapper;
using GeeO.Data.Dto.RequestForm;
using GeeO.Data.Models;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GeeO.Controllers.RequestForm
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RequestFormController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IRequestFormService _requestFormService;
        public RequestFormController(IRequestFormService requestFormService, IMapper mapper)
        {
            _requestFormService = requestFormService;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(string collectionId, int skip, int take, string keyword = "", string sortColumn = "CreatedDate desc")
        {
            try
            {
                var result = await _requestFormService.GetAll(collectionId, skip, take, keyword, sortColumn);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var result = await _requestFormService.GetById(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("process/{id}/{workflowId}")]
        public async Task<IActionResult> Process(string id, string workFlowId)
        {
            try
            {
                var result = await _requestFormService.Process(id, WorkFlowStatusEnum.Processing, workFlowId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] RequestFormViewModel viewModel)
        {
            try
            {
                var id = await _requestFormService.Create(_mapper.Map<RequestFormDto>(viewModel));
                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("approved/{id}/{workflowId}")]
        public async Task<IActionResult> Approved(string id, string workflowId)
        {
            try
            {
                var result = await _requestFormService.Process(id, WorkFlowStatusEnum.Solve, workflowId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-activities/{id}")]
        public async Task<IActionResult> GetActivityLogs(string id)
        {
            try
            {
                var result = await _requestFormService.GetActivityLogs(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-communicates/{id}")]
        public async Task<IActionResult> GetCommunicates(string id)
        {
            try
            {
                var result = await _requestFormService.GetCommunicates(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("add-comment")]
        public async Task<IActionResult> AddComment([FromForm] string id, [FromForm] string communicateId, [FromForm] string content, [FromForm] string attachments)
        {
            try
            {
                var data = await _requestFormService.AddComment(id, communicateId, content, attachments);
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("deny/{id}/{workflowId}")]
        public async Task<IActionResult> Deny(string id, string workflowId)
        {
            try
            {
                var result = await _requestFormService.Process(id, WorkFlowStatusEnum.Denied, workflowId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("expired/{id}/{workflowId}")]
        public async Task<IActionResult> Expired(string id, string workflowId)
        {
            try
            {
                var result = await _requestFormService.Process(id, WorkFlowStatusEnum.Denied, workflowId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-user-role/{id}")]
        public async Task<IActionResult> GetUserRole(string id)
        {
            try
            {
                var result = await _requestFormService.GetUserRole(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("assign-to/{id}")]
        public async Task<IActionResult> AssignTo(string id, [FromForm] string processId, [FromForm] string userId, [FromForm] string userName)
        {
            try
            {
                var result = await _requestFormService.AssignTo(id, processId, userId, userName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-user-group/{id}")]
        public async Task<IActionResult> GetUserGroup(string id)
        {
            try
            {
                var result = await _requestFormService.GetUserGroup(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("work-log/{id}")]
        public async Task<IActionResult> GetWorkLogById(string id)
        {
            try
            {
                var result = await _requestFormService.GetWorkLogById(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("save-work-log")]
        public async Task<IActionResult> SaveWorkLog([FromBody] WorkLogViewModel viewModel)
        {
            try
            {
                var workLogDto = _mapper.Map<WorkLogDto>(viewModel);
                var result = await _requestFormService.SaveWorkLog(workLogDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}