{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/GeeO.csproj"], "problemMatcher": "$tsc"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/GeeO.csproj"], "problemMatcher": "$tsc"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/GeeO.csproj"], "problemMatcher": "$tsc"}, {"type": "npm", "script": "start", "path": "ClientApp/", "problemMatcher": []}]}