﻿using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System;
using Microsoft.Data.SqlClient;
using GeeO.Services;
using GeeO.Data.Extensions;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassLessonController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;
        private readonly IClassLessonService _classLessonService;

        public ClassLessonController(GeeODbContext context, IAcadManageService acadManageService, IClassLessonService classLessonService)
        {
            _context = context;
            _acadManageService = acadManageService;
            _classLessonService = classLessonService;
        }

        // POST: api/ClassCourses
        [HttpPost("sync-for-new")]
        public async Task<IActionResult> SyncForNewLessonPlan(string lessonPlanId, string lesson)
        {
            try
            {
                await _classLessonService.SyncForNewLessonPlan(lessonPlanId, lesson);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("sync-by-level")]
        public async Task<IActionResult> SyncForLessonPlansByLevel(string levelId)
        {
            try
            {
                await _classLessonService.SyncForLessonPlansByLevel(levelId);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        // GET: api/ClassLesson/GetLessonDates/5
        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<DateTime>>> GetLessonDates(string classId)
        {
            var result = from cl in _context.ClassLesson.Where(cl => cl.ClassId == classId).OrderBy(x => x.StartTime)
                         select cl.StartTime.Value;
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassLesson
        [HttpGet("{classId}")]
        public async Task<ActionResult<IEnumerable<ClassLessonGrid>>> GetClassLesson(string classId)
        {
            var data = _context.ClassLesson.Include(x => x.Lesson).Where(x => x.ClassId == classId && (!x.Lesson.IsDeleted || x.TeacherLessonLogs.Count() > 0)).OrderBy(x => x.Lesson.CreatedDate);
            var result = from x in data
                         select new ClassLessonGrid
                         {
                             Id = x.Id,
                             LessonId = x.Lesson.Id,
                             Level = x.Lesson.Level.Name,
                             Lesson = x.Lesson.Lesson,
                             Subject = x.Lesson.Subject,
                             Content = x.Lesson.Content,
                             Tb = x.Lesson.Tb
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<ClassLesson>> GetClassLessonById(string id)
        {
            var result = await _context.ClassLesson
                .Include(x => x.ClassCourse)
                .Include(x => x.Lesson).ThenInclude(x => x.Level).FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
            return result;
        }

        // POST: api/ClassLesson/ResetClassLessons
        [HttpPost("[action]")]
        public async Task<ActionResult> ResetClassLessons()
        {
            var classIdList = from cls in _context.ClassCourse
                              from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId /*&& sch.EndDate.Date > DateTime.Today.AddMonths(-2)*/)
                              select cls.Id;
            List<string> classIds = await classIdList.ToListAsync().ConfigureAwait(false);
            foreach (string classId in classIds)
            {
                await BuildSheduleClassLessons(classId).ConfigureAwait(false);
            }
            _context.Database.ExecuteSqlRaw
                (
                    @"UPDATE Schedule SET EndDate = 
                        CASE 
                            WHEN (SELECT MAX(StartTime) FROM ClassLesson WHERE ClassId = Schedule.ClassCourseId) IS NULL THEN EndDate 
                            ELSE (SELECT MAX(StartTime) FROM ClassLesson WHERE ClassId = Schedule.ClassCourseId) 
                        END"
                );
            return Ok();
        }

        // POST: api/ClassLesson/ResetOrderClassLessons
        [HttpPost("[action]/{levelId}")]
        public async Task<ActionResult> ResetOrderClassLessons(string levelId)
        {
            var parameters = new object[]
            {
                new SqlParameter("levelId", levelId)
            };
            await _context.Database.ExecuteSqlRawAsync(@"ResetOrderLessonPlan @LevelId=@levelId", parameters);
            // await _classLessonService.SyncForLessonPlansByLevel(levelId).ConfigureAwait(false);
            List<string> classIds = await _context.ClassCourse.Include(x => x.Schedule).Where(cl => cl.LevelId == levelId && cl.Schedule.EndDate > DateTime.Today).Select(cls => cls.Id).ToListAsync().ConfigureAwait(false);
            foreach (string classId in classIds)
            {
                await BuildSheduleClassLessons(classId).ConfigureAwait(false);
            }
            return Ok();
        }

        // This is called when click to Assign teacher and lesson
        // POST: api/ClassLesson/BuildSheduleClassLessons/5
        [HttpPost("[action]/{classId}")]
        public async Task<ActionResult> BuildSheduleClassLessons(string classId)
        {
            var classCourse = await _context.ClassCourse
                                    .Include(x => x.Schedule).Include(x => x.ClassLessons)
                                    .FirstOrDefaultAsync(x => x.Id == classId).ConfigureAwait(false);

            if (classCourse != null && classCourse.Schedule != null)
            {
                var schedule = classCourse.Schedule;
                Dictionary<DayOfWeek, bool> scheduleDict = new Dictionary<DayOfWeek, bool>()
                {
                    { DayOfWeek.Monday, schedule.Monday },
                    { DayOfWeek.Tuesday, schedule.Tuesday },
                    { DayOfWeek.Wednesday, schedule.Wednesday },
                    { DayOfWeek.Thursday, schedule.Thursday },
                    { DayOfWeek.Friday, schedule.Friday },
                    { DayOfWeek.Saturday, schedule.Saturday },
                    { DayOfWeek.Sunday, schedule.Sunday }
                };
                List<DayOfWeek> lstDayOfWeeks = scheduleDict.Where(x => x.Value == true).Select(day => day.Key).ToList();
                List<DateTime> holidays = await _acadManageService.GetHolidays(classId).ConfigureAwait(false);

                var lessons = await _context.LessonPlan
                                    .Include(x => x.Level)
                                    .Where(x => x.LevelId == classCourse.LevelId && !x.IsDeleted)
                                    .OrderBy(x => x.CreatedDate)
                                    .ToListAsync().ConfigureAwait(false);

                DateTime dateRun = schedule.StartDate;

                for (int i = 0; i < lessons.Count; i++)
                {
                    bool matchedDate = false;
                    while (!matchedDate)
                    {
                        matchedDate = !holidays.Contains(dateRun.Date) && lstDayOfWeeks.Contains(dateRun.DayOfWeek);
                        if (matchedDate)
                        {
                            var startTime = new DateTime(dateRun.Year, dateRun.Month, dateRun.Day, schedule.StartTime.Hour, schedule.StartTime.Minute, 0);
                            var endTime = new DateTime(dateRun.Year, dateRun.Month, dateRun.Day, schedule.EndTime.Hour, schedule.EndTime.Minute, 0);
                            ClassLesson classLesson = classCourse.ClassLessons.Where(x => x.LessonId == lessons[i].Id).FirstOrDefault();
                            if (classLesson != null)
                            {
                                // This is updating StartTime, EndTime of ClassLesson.
                                classLesson.StartTime = startTime;
                                classLesson.EndTime = endTime;
                                _context.Entry(classLesson).SetActivityState(ActivityActionType.Arranged);
                            }
                        }
                        dateRun = dateRun.AddDays(1);
                    }
                }
            }

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        // POST: api/ClassLesson/AssignAllLessonToClass/5
        [HttpPost("[action]/{classId}")]
        public async Task<ActionResult> AssignAllLessonToClass(string classId)
        {
            var holidays = await _acadManageService.GetHolidays(classId).ConfigureAwait(false);
            var classCourse = await _context.ClassCourse.Include(x => x.Schedule).Include(x => x.ClassLessons).FirstOrDefaultAsync(x => x.Id == classId).ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }

            var schedule = classCourse.Schedule;
            List<string> classLessonList = classCourse.ClassLessons.Select(cls => cls.LessonId).ToList();
            var lessons = await _context.LessonPlan
                .Include(x => x.Level)
                .Where(x => !classLessonList.Contains(x.Id) && x.LevelId == classCourse.LevelId)
                .OrderBy(x => x.Level.Name).ThenBy(x => x.CreatedDate)
                .ToListAsync().ConfigureAwait(false);
            if (schedule == null)
            {
                foreach (var item in lessons)
                {
                    ClassLesson obj = new ClassLesson()
                    {
                        ClassId = classId,
                        LessonId = item.Id
                    };
                    _context.ClassLesson.Add(obj);
                }
            }
            else
            {
                if (lessons.Count > 0)
                {
                    DateTime? dtMax = _context.ClassLesson.Where(x => x.ClassId == classId).Max(x => x.StartTime);
                    List<DayOfWeek> lstDayOfWeeks = new List<DayOfWeek>();
                    if (schedule.Monday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Monday);
                    }
                    if (schedule.Tuesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Tuesday);
                    }
                    if (schedule.Wednesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Wednesday);
                    }
                    if (schedule.Thursday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Thursday);
                    }
                    if (schedule.Friday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Friday);
                    }
                    if (schedule.Saturday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Saturday);
                    }
                    if (schedule.Sunday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Sunday);
                    }
                    int studetTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                    DateTime tempDate;
                    if (dtMax.HasValue)
                    {
                        tempDate = new DateTime(dtMax.Value.Year, dtMax.Value.Month, dtMax.Value.Day, 0, 0, 0);
                        tempDate = tempDate.AddDays(1);
                    }
                    else
                    {
                        tempDate = schedule.StartDate;
                    }
                    int j = 0;
                    for (int i = 0; i < studetTime; i++)
                    {
                        var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (!holidays.Contains(tempDate.Date) && checkDayOfWeeks && lessons.Count > j)
                        {
                            ClassLesson obj = new ClassLesson()
                            {
                                ClassId = classId,
                                LessonId = lessons[j].Id,
                                StartTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.StartTime.Hour, schedule.StartTime.Minute, 0),
                                EndTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.EndTime.Hour, schedule.EndTime.Minute, 0)
                            };
                            j++;
                            _context.ClassLesson.Add(obj);
                        }
                        tempDate = tempDate.AddDays(1);
                    }
                }
            }

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        // POST: api/ClassLesson
        [HttpPost("{classId}")]
        public async Task<ActionResult> PostClassLesson(string classId, List<string> lstSelected)
        {
            if (lstSelected is null)
            {
                throw new ArgumentNullException(nameof(lstSelected));
            }

            var classCourse = await _context.ClassCourse.Include(x => x.ClassLessons).FirstOrDefaultAsync(x => x.Id == classId).ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }
            for (int i = 0; i < lstSelected.Count; i++)
            {
                ClassLesson obj = new ClassLesson()
                {
                    ClassId = classId,
                    LessonId = lstSelected[i]
                };
                _context.ClassLesson.Add(obj);
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        [HttpPost]
        [Route("list-filter/{classId}")]
        public async Task<ActionResult<IEnumerable<LessonPlan>>> GetClassLessonFilter(string classId, List<string> lstId)
        {
            var classCourse = await _context.ClassCourse.FindAsync(classId);
            var data = _context.LessonPlan.Include(x => x.Level).Where(x => !lstId.Contains(x.Id) && x.LevelId == classCourse.LevelId);
            return await data.ToListAsync().ConfigureAwait(false);
        }
        [HttpDelete("{id}")]
        public async Task<ActionResult<ClassLesson>> DeleteClassLesson(string id)
        {
            var classLesson = await _context.ClassLesson.FindAsync(id);
            if (classLesson == null)
            {
                return NotFound();
            }

            _context.ClassLesson.Remove(classLesson);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return classLesson;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<CatchUpScheduleGrid>>> GetClassLessonByTeacher(string id)
        {
            var data = await (from cl in _context.ClassLesson
                              join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId
                              join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                              join sl in _context.StudyLevel on lp.LevelId equals sl.Id
                              where ct.TeacherId == id && cl.StartTime != null && cl.EndTime != null && cl.StartTime >= DateTime.Now && (!lp.IsDeleted || cl.TeacherLessonLogs.Count() > 0)
                              orderby sl.Name, lp.CreatedDate
                              select new CatchUpScheduleGrid()
                              {
                                  Id = cl.Id,
                                  Level = sl.Name,
                                  Class = cc.Name,
                                  Lesson = lp.Lesson,
                                  StartTime = cl.StartTime
                              }).ToListAsync().ConfigureAwait(false);
            return data;
        }
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<CatchUpScheduleGrid>> GetClassLessonByClassLessonId(string id)
        {
            var data = await (from cl in _context.ClassLesson
                              join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId
                              join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                              join sl in _context.StudyLevel on lp.LevelId equals sl.Id
                              where cl.Id == id && cl.StartTime != null && cl.EndTime != null
                              select new CatchUpScheduleGrid()
                              {
                                  Id = cl.Id,
                                  Level = sl.Name,
                                  Class = cc.Name,
                                  Lesson = lp.Lesson,
                                  StartTime = cl.StartTime
                              }).FirstOrDefaultAsync().ConfigureAwait(false);
            return data;
        }
    }
}
