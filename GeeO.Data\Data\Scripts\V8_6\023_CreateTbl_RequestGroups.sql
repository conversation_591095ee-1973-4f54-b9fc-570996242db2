USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestGroups]    Script Date: 3/26/2025 5:56:59 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestGroups](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](450) NULL,
	[Description] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestGroups] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestGroups] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO


