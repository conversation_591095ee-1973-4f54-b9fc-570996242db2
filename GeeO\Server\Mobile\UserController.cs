﻿using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Mobile.Models;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class UserController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly IAzureAdService _azureAdService;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;

        public UserController(GeeODbContext context,
            IStudentPaymentService studentPaymentService,
            IAzureAdService azureAdService,
            IUploadService uploadService,
            IFileService fileService)
        {
            _context = context;
            _studentPaymentService = studentPaymentService;
            _azureAdService = azureAdService;
            _uploadService = uploadService;
            _fileService = fileService;
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<AcadUser>> GetUser(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return BadRequest();
            }

            AcadUser userInfo = await (from aus in _context.AspNetUsers.Where(u => u.Id == userId)
                                       from usr in _context.UserInfo.Where(u => aus.Id == u.UserId)
                                       select new AcadUser()
                                       {
                                           Id = aus.Id,
                                           FullName = $"{aus.LastName} {aus.FirstName}",
                                           EnglishName = usr.EnglishName,
                                           ElAccount = aus.Email
                                       })
                                       .FirstOrDefaultAsync().ConfigureAwait(false);

            if (userInfo == null)
            {
                return NotFound();
            }

            return userInfo;
        }

        [HttpGet("[action]/{principal}")]
        public async Task<ActionResult<AcadUser>> GetUserInfo(string principal)
        {
            if (string.IsNullOrEmpty(principal))
            {
                return BadRequest();
            }

            bool isMSAccount = principal.EndsWith("@el.gee-o.edu.vn");

            AcadUser userInfo = await (from aus in _context.AspNetUsers.Where(u => isMSAccount || u.NormalizedEmail == principal.ToUpper())
                                       from usr in _context.UserInfo.Where(u => aus.Id == u.UserId && (!isMSAccount || u.ElAccount.ToUpper() == principal.ToUpper()))
                                       select new AcadUser()
                                       {
                                           Id = aus.Id,
                                           FullName = $"{aus.LastName} {aus.FirstName}",
                                           EnglishName = usr.EnglishName,
                                           ElAccount = isMSAccount ? usr.ElAccount : aus.Email
                                       })
                                       .FirstOrDefaultAsync().ConfigureAwait(false);

            if (userInfo == null)
            {
                return NotFound();
            }

            userInfo.ImageBase64 = isMSAccount ? await _azureAdService.GetUserPhotoAsync(principal) : null;
            return userInfo;
        }
    }
}
