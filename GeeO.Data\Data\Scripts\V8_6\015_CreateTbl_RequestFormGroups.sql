USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormGroups]    Script Date: 3/26/2025 5:40:50 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormGroups](
	[Id] [nvarchar](450) NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[GroupId] [nvarchar](450) NULL,
	[GroupName] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormGroups] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormGroups] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormGroups]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormGroups_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormGroups] CHECK CONSTRAINT [FK_RequestFormGroups_RequestForm]
GO

ALTER TABLE [dbo].[RequestFormGroups]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormGroups_RequestGroups] FOREIGN KEY([GroupId])
REFERENCES [dbo].[RequestGroups] ([Id])
GO

ALTER TABLE [dbo].[RequestFormGroups] CHECK CONSTRAINT [FK_RequestFormGroups_RequestGroups]
GO


