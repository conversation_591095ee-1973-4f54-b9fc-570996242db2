select sc.Name as PaymentDescription, sc.StartDate as PaymentDate
		,ISNULL(CAST(sc.Amount as varchar(20)),'') as Amount
		,ISNULL(CAST(sc.NumberOfSession as varchar(20)),'') as NumberOfSessions
		,cls.Name as ClassName, cs.StudentId, std.StudentName, std.EnglishName
from StudentCourse sc
join ClassStudent cs on sc.ClassStudentId = cs.Id
join ClassCourse cls on cs.ClassId = cls.Id
join Student std on cs.StudentId = std.Id
order by cls.Name, std.Id, sc.StartDate