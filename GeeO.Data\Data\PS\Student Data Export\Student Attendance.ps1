﻿###
### GEE-O ENGLISH script for getting student attendance
###

# read settings 
$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

# load common utilities
.$PSScriptRoot/../Common/Common.ps1


# declare sql in a variable and pass it to -Query switch
$sqlServer = $settings.database.server
$database = $settings.database.database
$outFolderRoot = $settings.outFolder


function Get-StudentAttendance
{
    $time = Get-Date -Format "yyyyMMdd-HHmmss"
    Write-RunLog "====================== Starting ======================"

	if (!(Test-Path $outFolderRoot))
	{
		New-Item -ItemType Directory -Path $outFolderRoot > $null
		Write-Host "$outFolderRoot folder created successfully"
	}

	$outFolder = "$outFolderRoot/$time"
	New-Item -ItemType Directory -Path $outFolder > $null
	Write-Host "$outFolder folder created successfully"
	
	$ExcelObject = New-Object -ComObject Excel.Application  
	$ExcelObject.Visible = $false 
	$ExcelObject.DisplayAlerts = $false

    $sqlClass="
    select cls.Id as 'ClassId', cls.[Name] as 'ClassName', cls.LevelId
	from ClassCourse cls
	where not (cls.[Name] like '%test%' or cls.[Name] like 'SC%' or cls.[Name] like '%demo%' or cls.[Name] in ('', '0221')) --and cls.[Name] like 'TH%'
	order by cls.[Name]
    "
	#$classList = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlClass
	$classList = Execute-DbQuery -Query $sqlClass

    foreach ($class in $classList)
    {
        $outFile = Join-Path $outFolder "$($class.ClassName).xlsx" # name of file to export
        Write-Host $outFile
		Write-Output "Class: $($class.ClassName)"
		
		# Create Excel file  
		$ActiveWorkbook = $ExcelObject.Workbooks.Add()  
		$ActiveWorksheet = $ActiveWorkbook.Worksheets.Item(1)
		$ActiveWorksheet.Columns(4).NumberFormat = "dd/MM/yyyy HH:mm"
		$ActiveWorksheet.Columns(7).NumberFormat = "dd/MM/yyyy"

		#$sheetName = $lesson.LessonDate.ToString("dd-MM-yyyy")
		#$ActiveWorksheet.Name = $sheetName

		$ActiveWorksheet.Cells.Item(1,1) = "StudentId"
		$ActiveWorksheet.Cells.Item(1,2) = "Lesson"
		$ActiveWorksheet.Cells.Item(1,3) = "DateTime"
		$ActiveWorksheet.Cells.Item(1,4) = "StudentName"
		$ActiveWorksheet.Cells.Item(1,5) = "EnglishName"
		$ActiveWorksheet.Cells.Item(1,6) = "Birthday"
		$ActiveWorksheet.Cells.Item(1,7) = "Presence"
		$ActiveWorksheet.Cells.Item(1,8) = "StarScore"
		$ActiveWorksheet.Cells.Item(1,9) = "ClassLessonId"
		$ActiveWorksheet.Cells.Item(1,10) = "LevelId"
		$ActiveWorksheet.Cells.Item(1,11) = "ClassId"
		$ActiveWorksheet.Cells.Item(1,12) = "LogDateTime"
			
		# $sqlLesson="
			# select cl.Id, lp.Lesson, cl.StartTime as 'LessonDate',
				   # std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   # sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			# from ClassLesson cl 
			# join LessonPlan lp on cl.LessonId = lp.Id
			# --join ClassStudent cs on cl.ClassId = cs.ClassId
			# join Student std on cs.StudentId = std.Id
			# left join TeacherLessonLog tll on cl.Id = tll.ClassLessonId and tll.HistoryLog = 0
			# left join StudentLessonLogData sll on tll.Id = sll.LogId and std.Id = sll.StudentInfoId
			# where cl.ClassId = '$($class.ClassId)' and cl.StartTime < CONVERT(date, GETDATE())
			# order by cl.StartTime, std.Id
		# "
		$sqlLesson="
			select cl.Id, lp.Lesson, cl.StartTime as 'LessonDate',
				   std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			from StudentLessonLogData sll
			join TeacherLessonLog tll on sll.LogId = tll.Id and tll.HistoryLog = 0
			join ClassLesson cl on tll.ClassLessonId = cl.Id
			join LessonPlan lp on cl.LessonId = lp.Id
			join Student std on sll.StudentInfoId = std.Id
			where cl.ClassId = '$($class.ClassId)'
			order by cl.StartTime, std.Id
		"

        $classLessons = Execute-DbQuery -Query $sqlLesson
            
		$rowIndex = 2

        foreach ($lesson in $classLessons)
        {
			$ActiveWorksheet.Cells.Item($rowIndex,1) = "$($lesson.StudentId)"
			$ActiveWorksheet.Cells.Item($rowIndex,2) = "$($lesson.Lesson)"
			$ActiveWorksheet.Cells.Item($rowIndex,3) = "$($lesson.LessonDate)"
			$ActiveWorksheet.Cells.Item($rowIndex,4) = "$($lesson.StudentName)"
			$ActiveWorksheet.Cells.Item($rowIndex,5) = "$($lesson.EnglishName)"
			$ActiveWorksheet.Cells.Item($rowIndex,6) = $lesson.Birthday
			$ActiveWorksheet.Cells.Item($rowIndex,7) = "$($lesson.Present)"
			$ActiveWorksheet.Cells.Item($rowIndex,8) = "$($lesson.StarScore)"
			$ActiveWorksheet.Cells.Item($rowIndex,9) = "$($lesson.Id)"
			$ActiveWorksheet.Cells.Item($rowIndex,10) = "$($class.LevelId)"
			$ActiveWorksheet.Cells.Item($rowIndex,11) = "$($class.ClassId)"
			$ActiveWorksheet.Cells.Item($rowIndex,12).Formula = "=DATE(YEAR(C$rowIndex),MONTH(C$rowIndex),DAY(C$rowIndex))"
			
			$rowIndex++
        }
		#$ActiveWorksheet.UsedRange.EntireColumn.AutoFit()
		$ActiveWorksheet.UsedRange.EntireColumn.AutoFilter()
		$ActiveWorksheet.Range("C2:C$rowIndex").NumberFormat = "dd/MM/yyyy HH:mm"
		$ActiveWorksheet.Range("L2:L$rowIndex").NumberFormat = "dd/MM/yyyy"
		$ActiveWorksheet.Range("G2:G$rowIndex").NumberFormat = "0"
		$ActiveWorksheet.Columns.Item("C:H").EntireColumn.AutoFit()
		$ActiveWorksheet.Columns.Item("L:L").EntireColumn.AutoFit()
		$ActiveWorkbook.SaveAs($outFile)
    }

	$ExcelObject.Quit()
    Write-RunLog "====================== Ending ======================"
}

Write-Host "PowerShell version $($PSVersionTable.PSVersion)"

Get-StudentAttendance
