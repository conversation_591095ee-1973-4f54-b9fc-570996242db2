﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class ClassEventsController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;

        public ClassEventsController(GeeODbContext context, IAcadManageService acadManageService)
        {
            _context = context;
            _acadManageService = acadManageService;
        }

        [HttpGet("[action]/{userId}")]
        public async Task<IEnumerable<Notifications>> GetEventsPerUser(string userId)
        {
            var userRole = await _acadManageService.GetUserRole(userId);

            var otherNotify = GetNotifications();

            if (GeeOConstants.TeacherRoles.Contains(userRole.NormalizedName))
            {
                var classEvents = GetEventsPerTeacher(userId);
                return await classEvents.Union(otherNotify).OrderByDescending(x => x.StartTime).ToListAsync().ConfigureAwait(false);
            }

            var allClassEvents = await GetEventsPerRole(userId);
            return await allClassEvents.Union(otherNotify).OrderByDescending(x => x.StartTime).ToListAsync().ConfigureAwait(false);
        }

        public async Task<IQueryable<Notifications>> GetEventsPerRole(string userId)
        {
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var classNotify = from evt in _context.Notifications.Where(evt => evt.StartTime > DateTime.Now.AddDays(-1) && (evt.UserId == null || evt.UserId == userId))
                              from cls in _context.ClassCourse.Where(cls => evt.ClassId == cls.Id && (assignedCampus == null || assignedCampus.Contains(cls.CampusId)))
                              select new Notifications()
                              {
                                  Id = evt.Id,
                                  Title = evt.Title,
                                  Content = evt.Content,
                                  StartTime = evt.StartTime,
                                  EndTime = evt.EndTime,
                                  Subject = evt.Subject,
                                  Activity = evt.Activity,
                              };

            var classEvents = from evt in _context.Events.Where(evt => evt.StartTime > DateTime.Now.AddDays(-1))
                              from cls in _context.ClassCourse.Where(cls => evt.ClassId == cls.Id && (assignedCampus == null || assignedCampus.Contains(cls.CampusId)))
                              select new Notifications()
                              {
                                  Id = evt.Id,
                                  Title = evt.Title,
                                  Content = evt.Content,
                                  StartTime = evt.StartTime,
                                  EndTime = evt.EndTime,
                                  Subject = evt.Subject,
                                  Activity = evt.Activity,
                              };

            return classEvents.Union(classNotify);
        }

        public IQueryable<Notifications> GetEventsPerTeacher(string userId)
        {
            var classNotify = from evt in _context.Notifications.Where(evt => evt.StartTime > DateTime.Now.AddDays(-1) && (evt.UserId == null || evt.UserId == userId))
                              from ct in _context.ClassTeacher.Where(ct => evt.ClassId == ct.ClassId && ct.TeacherId == userId)
                              select new Notifications()
                              {
                                  Id = evt.Id,
                                  Title = evt.Title,
                                  Content = evt.Content,
                                  StartTime = evt.StartTime,
                                  EndTime = evt.EndTime,
                                  Subject = evt.Subject,
                                  Activity = evt.Activity,
                              };

            var classEvents = from evt in _context.Events.Where(evt => evt.StartTime > DateTime.Now.AddDays(-1))
                              from ct in _context.ClassTeacher.Where(ct => evt.ClassId == ct.ClassId && ct.TeacherId == userId)
                              select new Notifications()
                              {
                                  Id = evt.Id,
                                  Title = evt.Title,
                                  Content = evt.Content,
                                  StartTime = evt.StartTime,
                                  EndTime = evt.EndTime,
                                  Subject = evt.Subject,
                                  Activity = evt.Activity,
                              };

            return classEvents.Union(classNotify);
        }

        public IQueryable<Notifications> GetNotifications()
        {
            var otherNotify = from evt in _context.Notifications.Where(evt => evt.StartTime > DateTime.Now.AddDays(-1) && evt.ClassId == null)
                              select new Notifications()
                              {
                                  Id = evt.Id,
                                  Title = evt.Title,
                                  Content = evt.Content,
                                  StartTime = evt.StartTime,
                                  EndTime = evt.EndTime,
                                  Subject = evt.Subject,
                                  Activity = evt.Activity,
                              };

            return otherNotify;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Events>>> GetEvents(string classId)
        {
            var result = _context.Events.Where(x => x.ClassId == classId).OrderByDescending(x => x.StartTime);
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpPost("[action]")]
        public async Task<ActionResult> SaveClassEvent(Events updateEvent)
        {
            if (updateEvent == null)
            {
                return BadRequest();
            }

            var existingEvent = await _context.Events.FindAsync(updateEvent.Id).ConfigureAwait(false);

            if (existingEvent == null)
            {
                updateEvent.EndTime = updateEvent.StartTime.AddMinutes(30);
                updateEvent.CreatedDate = DateTime.Now;
                _context.Events.Add(updateEvent);
            }
            else
            {
                existingEvent.Title = updateEvent.Title;
                existingEvent.Content = updateEvent.Content;
                existingEvent.StartTime = updateEvent.StartTime;
                existingEvent.EndTime = updateEvent.EndTime;
                existingEvent.ModifiedBy = updateEvent.CreatedBy;

                _context.Entry(existingEvent).State = EntityState.Modified;
            }

            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok();
        }
    }
}
