USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormProcess]    Script Date: 3/26/2025 5:42:38 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormProcess](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[WorkLogById] [nvarchar](450) NULL,
	[ProcessName] [nvarchar](100) NULL,
	[Description] [nvarchar](500) NULL,
	[Content] [nvarchar](max) NULL,
	[ProcessOrder] [int] NULL,
	[ProcessStatus] [int] NULL,
	[ProcessAction] [int] NULL,
	[IsDeactivate] [bit] NULL,
	[ExpirationCycle] [int] NULL,
	[ExpiratedDate] [datetime] NULL,
	[TypeApprovalId] [int] NULL,
	[NextProcessIfDeniedId] [nvarchar](450) NULL,
	[WorkLogTemplateId] [nvarchar](450) NULL,
	[Answers] [nvarchar](max) NULL,
	[ProcessById] [nvarchar](450) NULL,
	[ProcessByName] [nvarchar](100) NULL,
	[ProcessAt] [datetime] NULL,
	[WorkLogByName] [nvarchar](100) NULL,
	[WorkLogAt] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormProcess] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcess] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormProcess]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcess_FormTemplate] FOREIGN KEY([WorkLogTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcess] CHECK CONSTRAINT [FK_RequestFormProcess_FormTemplate]
GO

ALTER TABLE [dbo].[RequestFormProcess]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcess_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcess] CHECK CONSTRAINT [FK_RequestFormProcess_RequestForm]
GO

ALTER TABLE [dbo].[RequestFormProcess]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormProcess_RequestFormProcess] FOREIGN KEY([NextProcessIfDeniedId])
REFERENCES [dbo].[RequestFormProcess] ([Id])
GO

ALTER TABLE [dbo].[RequestFormProcess] CHECK CONSTRAINT [FK_RequestFormProcess_RequestFormProcess]
GO


