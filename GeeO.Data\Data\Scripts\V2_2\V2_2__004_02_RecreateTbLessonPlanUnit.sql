ALTER TABLE [dbo].[LessonPlanUnit] DROP CONSTRAINT [FK_LessonPlanUnit_MaterialId]
GO

ALTER TABLE [dbo].[LessonPlanUnit] DROP CONSTRAINT [FK_LessonPlanUnit_LessonPlan_LessonPlanId]
GO

/****** Object:  Table [dbo].[LessonPlanUnit]    Script Date: 14-Dec-19 9:41:35 PM ******/
DROP TABLE [dbo].[LessonPlanUnit]
GO

/****** Object:  Table [dbo].[LessonPlanUnit]    Script Date: 14-Dec-19 9:41:35 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[LessonPlanUnit](
	[Id] [nvarchar](450) NOT NULL,
	[LessonPlanId] [nvarchar](450) NOT NULL,
	[MaterialId] [nvarchar](450) NULL,
	[SortOrder] [int] NULL,
	[Time] [int] NULL,
	[Procedures] [nvarchar](2048) NULL,
	[Description] [nvarchar](2048) NULL,
	[Materials] [nvarchar](2048) NULL,
	[TeacherActivities] [nvarchar](4000) NULL,
	[LearningOutcome] [nvarchar](4000) NULL,
	[Note] [nvarchar](2048) NULL,
	[Category] [nvarchar](1024) NULL,
 CONSTRAINT [PK_LessonPlanUnit] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[LessonPlanUnit]  WITH CHECK ADD  CONSTRAINT [FK_LessonPlanUnit_LessonPlan_LessonPlanId] FOREIGN KEY([LessonPlanId])
REFERENCES [dbo].[LessonPlan] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[LessonPlanUnit] CHECK CONSTRAINT [FK_LessonPlanUnit_LessonPlan_LessonPlanId]
GO

ALTER TABLE [dbo].[LessonPlanUnit]  WITH CHECK ADD  CONSTRAINT [FK_LessonPlanUnit_MaterialId] FOREIGN KEY([MaterialId])
REFERENCES [dbo].[Material] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[LessonPlanUnit] CHECK CONSTRAINT [FK_LessonPlanUnit_MaterialId]
GO


