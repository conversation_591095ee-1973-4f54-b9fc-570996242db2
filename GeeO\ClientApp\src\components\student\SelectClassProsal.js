import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import CommonSearchToolbar from '../ui/table/CommonSearchToolbar';
import authService from '../api-authorization/AuthorizeService';
import { PagingTable } from '../ui/table/PagingTable';
import { Loading } from '../ui/Loading';

const styles = theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
    marginBottom: theme.spacing(2),
    overflowX: 'auto'
  },
  cell: {
    padding: theme.spacing(1, 0, 1, 2)
  }
});

class SelectClassProposalComp extends Component {
  static displayName = SelectClassProposalComp.name;
  constructor(...args) {
    super(...args);
    this.state = {
      data: [],
      lstSelected: [],
      studentId: this.props.studentId,
      filterText: '',
      loading: true
    };
  }

  componentDidMount() {
    this.populateParentData();
  }

  async populateParentData() {
    const [token, loggedInUser] = await Promise.all([
      authService.getAccessToken(),
      authService.getUser()
    ]);
    const response = await fetch(
      `api/Student/GetClassPropose/${loggedInUser.sub}`,
      {
        headers: !token ? {} : { Authorization: `Bearer ${token}` }
      }
    );
    const data = await response.json();
    this.setState({ data, loading: false }, () => this.setChildData(''));
  }

  callbackValueCheckboxTabl = e => {
    this.props.callbackValueCheckboxTabl(e);
  };

  handleChange = filterText => {
    this.setChildData(filterText);
  };

  callbackGetValueRadioBtn = e => {
    this.props.callbackGetValueRadioBtn(e);
  };

  callbackGetValueObjRadioBtn = e => {
    this.props.callbackGetValueObjRadioBtn(e);
  };

  setChildData = filterText => {
    const { data } = this.state;
    const searchTerm = filterText.toLowerCase();
    let filteredRows = data.filter(item => {
      const itemText = (
        item.level +
        item.class +
        item.scheduleFormat +
        item.room
      ).toLowerCase();
      return itemText.indexOf(searchTerm) !== -1;
    });
    this.child.setData(filteredRows);
  };

  render() {
    const cols = [
      { name: 'level', header: 'Level', align: 'left' },
      { name: 'class', header: 'Class' },
      { name: 'scheduleFormat', header: 'Schedule', align: 'left' },
      { name: 'startDateLocal', header: 'Start Date', align: 'left' },
      { name: 'endDateLocal', header: 'End Date', align: 'left' },
      { name: 'room', header: 'Room', align: 'left' },
      { name: 'learningProccess', header: 'Progress (Days)', align: 'left' },
      { name: 'numberOfPupils', header: 'Number Of Pupils', align: 'left' }
    ];
    let contents = this.state.loading ? (
      <Loading />
    ) : (
      <Paper>
        <CommonSearchToolbar handleChange={this.handleChange} />
        <PagingTable
          onRef={actualChild => (this.child = actualChild)}
          cols={cols}
          isShowRadioButton="true"
          callbackGetValueRadioBtn={this.callbackGetValueRadioBtn}
          callbackGetValueObjRadioBtn={this.callbackGetValueObjRadioBtn}
        />
      </Paper>
    );
    return <Fragment>{contents}</Fragment>;
  }
}

SelectClassProposalComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const SelectClassProposal = withStyles(styles)(SelectClassProposalComp);
