using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Geeo.Data.Dto.FormTemplate;
using Geeo.Models;
using GeeO.Data.Models;
using GeeO.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Geeo.Controllers.FormTemplate
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]

    public class FormTemplateController : ControllerBase
    {
        private readonly IFormTemplateService _formTemplateService;
        private readonly IMapper _mapper;
        public FormTemplateController(IFormTemplateService formTemplateService, IMapper mapper)
        {
            _formTemplateService = formTemplateService;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] FormTemplateRequest formTemplateRequest)
        {
            try
            {
                var formTemplateDto = new FormTemplateDto
                {
                    CollectionId = formTemplateRequest.CollectionId,
                    Skip = formTemplateRequest.Skip,
                    Take = formTemplateRequest.Take,
                    IsFilter = formTemplateRequest.IsFilter
                };
                var result = await _formTemplateService.GetAll(formTemplateDto, FormTemplateType.RequestForm);
                var formTemplateResponse = result.Select(r => new FormTemplateResponse
                {
                    Id = r.Id,
                    FormCollectionName = r.FormCollections.Name,
                    Name = r.Name,
                    FormStatus = r.FormStatus,
                    Deactivate = r.Deactivate,
                    CampusName = r.FormTemplateCampuses.Select(c => c.Campus.Name).ToList()
                }).ToList();
                return Ok(new { Status = StatusCodes.Status200OK, Data = formTemplateResponse });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("work-log")]
        public async Task<IActionResult> GetWorkLog([FromQuery] FormTemplateRequest formTemplateRequest)
        {
            try
            {
                var formTemplateDto = new FormTemplateDto
                {
                    CollectionId = formTemplateRequest.CollectionId,
                    Skip = formTemplateRequest.Skip,
                    Take = formTemplateRequest.Take,
                    IsFilter = formTemplateRequest.IsFilter
                };
                var result = await _formTemplateService.GetAll(formTemplateDto, FormTemplateType.WorkLog);
                var formTemplateResponse = result.Select(r => new FormTemplateResponse
                {
                    Id = r.Id,
                    FormCollectionName = r.FormCollections.Name,
                    Name = r.Name,
                    FormStatus = r.FormStatus,
                    Deactivate = r.Deactivate,
                    CampusName = r.FormTemplateCampuses.Select(c => c.Campus.Name).ToList()
                }).ToList();
                return Ok(new { Status = StatusCodes.Status200OK, Data = formTemplateResponse });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-collections")]
        public async Task<IActionResult> GetFormTemplateCollections()
        {
            try
            {
                var result = await _formTemplateService.GetFormTemplateCollections();
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-by-id")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var result = await _formTemplateService.GetById(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-groups")]
        public async Task<IActionResult> GetGroups()
        {
            try
            {
                var result = await _formTemplateService.GetGroups();
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpDelete("delete-field/{id}")]
        public async Task<IActionResult> DeleteField(string id, string fieldId)
        {
            try
            {
                var result = await _formTemplateService.DeleteField(id, fieldId);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("create-temporary")]
        public async Task<IActionResult> CreateTemporary(string type)
        {
            try
            {
                var id = await _formTemplateService.CreateTemporary(type);
                var result = ((OkObjectResult)await GetById(id)).Value;
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] FormTemplateViewModel formTemplateRequest)
        {
            try
            {
                var formTemplateDto = _mapper.Map<FormTemplateAllDto>(formTemplateRequest);
                var id = await _formTemplateService.Create(formTemplateDto);
                var result = ((OkObjectResult)await GetById(id)).Value;
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] FormTemplateViewModel formTemplateRequest)
        {
            try
            {
                var formTemplateDto = _mapper.Map<FormTemplateAllDto>(formTemplateRequest);
                var result = await _formTemplateService.Update(formTemplateDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("toggle-status")]
        public async Task<IActionResult> ToggleStatus(string id)
        {
            try
            {
                var result = await _formTemplateService.ToggleStatus(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-process/{formTemplateId}")]
        public async Task<IActionResult> GetProcessByFormId(string formTemplateId)
        {
            try
            {
                var result = await _formTemplateService.GetProcessByFormId(formTemplateId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("add-process")]
        public async Task<IActionResult> AddProcess([FromBody] FormTemplateProcessViewModel viewModel)
        {
            try
            {
                var data = await _formTemplateService.AddProcess(_mapper.Map<FormTemplateProcessDto>(viewModel));
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("update-process")]
        public async Task<IActionResult> UpdateProcess([FromBody] FormTemplateProcessViewModel viewModel)
        {
            try
            {
                var data = await _formTemplateService.UpdateProcess(_mapper.Map<FormTemplateProcessDto>(viewModel));
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-process-by-id/{id}")]
        public async Task<IActionResult> GetProcessById(string id)
        {
            try
            {
                var result = await _formTemplateService.GetProcessById(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpDelete("delete-process/{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await _formTemplateService.Delete(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("update-worklog")]
        public async Task<IActionResult> UpdateWorkLog(string processId, string workLogId)
        {
            try
            {
                var data = await _formTemplateService.UpdateWorkLog(processId, workLogId);
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-trigger/{processId}")]
        public async Task<IActionResult> GetTrigger(string processId)
        {
            try
            {
                var result = await _formTemplateService.GetTrigger(processId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPost("add-trigger")]
        public async Task<IActionResult> AddTrigger([FromBody] FormTemplateProcessTriggerViewModel viewModel)
        {
            try
            {
                var data = await _formTemplateService.AddTrigger(_mapper.Map<FormTemplateProcessTriggerDto>(viewModel));
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpPut("update-trigger")]
        public async Task<IActionResult> UpdateTrigger([FromBody] FormTemplateProcessTriggerViewModel viewModel)
        {
            try
            {
                var data = await _formTemplateService.UpdateTrigger(_mapper.Map<FormTemplateProcessTriggerDto>(viewModel));
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpDelete("delete-trigger/{id}")]
        public async Task<IActionResult> DeleteTrigger(string id)
        {
            try
            {
                var result = await _formTemplateService.DeleteTrigger(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-form-template-trigger/{id}")]
        public async Task<IActionResult> GetFormTemplateTrigger(string id)
        {
            try
            {
                var result = await _formTemplateService.GetFormTemplateTrigger(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("get-input-keys/{id}")]
        public async Task<IActionResult> GetInputKeys(string id)
        {
            try
            {
                var result = await _formTemplateService.GetInputKeys(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}