﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Common;
using GeeO.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassCoursesController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;
        private readonly ILogger _logger;
        private readonly IClassLessonService _classLessonService;

        public ClassCoursesController(GeeODbContext context, IAcadManageService acadManageService, ILogger<ClassCoursesController> logger, IClassLessonService classLessonService)
        {
            _context = context;
            _acadManageService = acadManageService;
            _logger = logger;
            _classLessonService = classLessonService;
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassCourseInfo(string userId)
        {
            try
            {
                List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);
                var result = from cls in _context.ClassCourse
                             .Include(cls => cls.Schedule)
                             .Where(cls =>
                                   (assignedCampus == null || assignedCampus.Contains(cls.CampusId)) &&
                                   (_context.Schedule.Any(sch => sch.ClassCourseId == cls.Id && sch.EndDate > DateTime.Now)
                                   || cls.Schedule == null))
                            .Include(x => x.ClassTeachers)
                             from lv in _context.StudyLevel.Where(lv => cls.LevelId == lv.Id)
                             orderby cls.Name
                             select new ClassCourseGrid()
                             {
                                 Id = cls.Id,
                                 Level = lv.Name,
                                 Class = cls.Name,
                                 Description = cls.Description,
                                 Teacher = string.Join(", ", cls.ClassTeachers.Select(x => x.Teacher.FirstName + " " + x.Teacher.LastName).ToList()),
                             };
                return await result.ToListAsync().ConfigureAwait(false);
            }
            catch(Exception ex)
            {
                var exMessage = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                return BadRequest(exMessage);
            }
        }

        // GET: api/ClassCourses
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassCourse()
        {
            return await GetClassList("0", 0);
        }

        // GET: api/ClassCourses/1
        [HttpGet("[action]/{userId}/{finishedClasses}")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassList(string userId, int finishedClasses)
        {
            _logger.LogInformation("Request get class list...");

            IQueryable<ClassCourseGrid> scheduledClassesQuery = await GetScheduledClasses(userId, finishedClasses);
            List<ClassCourseGrid> scheduledClasses = await scheduledClassesQuery.ToListAsync().ConfigureAwait(false);

            if (finishedClasses == 1)
            {
                return scheduledClasses.ToList();
            }

            IQueryable<ClassCourseGrid> notScheduledClassesQuery = await GetNotScheduledClasses(userId);
            List<ClassCourseGrid> notScheduledClasses = await notScheduledClassesQuery.ToListAsync().ConfigureAwait(false);

            return scheduledClasses.Concat(notScheduledClasses).ToList();
        }

        public async Task<IQueryable<ClassCourseGrid>> GetScheduledClasses(string userId, int finishedClasses)
        {
            List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = from cls in _context.ClassCourse
                         from lv in _context.StudyLevel.Where(lv => cls.LevelId == lv.Id)
                             //from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId).DefaultIfEmpty()
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && ((finishedClasses == 1 && sch.EndDate.Date < DateTime.Today) || finishedClasses == 0 && sch.EndDate.Date >= DateTime.Today))
                         where (assignedCampus == null || assignedCampus.Contains(cls.CampusId))
                         orderby sch.StartDate.Date descending, sch.EndDate.Date descending
                         select new ClassCourseGrid()
                         {
                             Id = cls.Id,
                             Level = lv.Name,
                             Class = cls.Name,
                             Description = cls.Description,
                             Schedule = sch.ScheduleFormat,
                             StartTimeLocal = sch.StartTimeLocal,
                             EndTimeLocal = sch.EndTimeLocal,
                             StartDate = sch.StartDateLocal,
                             EndDate = sch.EndDateLocal,
                             Teacher = string.Join(",<br/>",
                                                   from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId)
                                                   from user in _context.AspNetUsers.Where(tch => tch.Id == ct.TeacherId)
                                                   orderby ct.IsPrimary descending
                                                   select user.FullName
                                                   ),
                             TeachingProccess = (from cl in _context.ClassLesson.Where(cl => cl.ClassId == cls.Id && cl.StartTime <= DateTime.Today) select cl).Count().ToString(),
                             NumberOfStudents = (from cs in cls.ClassStudents.Where(cs => cs.StudentType == ClassType.Regular) select cs).Count(),
                             ScheduleObj = sch
                         };

            return result;
        }

        public async Task<IQueryable<ClassCourseGrid>> GetNotScheduledClasses(string userId)
        {
            List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = from cls in _context.ClassCourse.Where(cls => cls.Schedule == null)
                         from lv in _context.StudyLevel.Where(lv => cls.LevelId == lv.Id)
                         where (assignedCampus == null || assignedCampus.Contains(cls.CampusId))
                         orderby cls.Name
                         select new ClassCourseGrid()
                         {
                             Id = cls.Id,
                             Level = lv.Name,
                             Class = cls.Name,
                             Description = cls.Description,
                             Schedule = string.Empty,
                             StartTimeLocal = string.Empty,
                             EndTimeLocal = string.Empty,
                             StartDate = string.Empty,
                             EndDate = string.Empty,
                             Teacher = string.Join(",<br/>",
                                                   from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId)
                                                   from user in _context.AspNetUsers.Where(tch => tch.Id == ct.TeacherId)
                                                   orderby ct.IsPrimary descending
                                                   select user.FullName
                                                   ),
                             TeachingProccess = string.Empty
                         };

            return result;
        }

        // GET: api/ClassCourses/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<ClassCourseGrid>> GetClassInfo(string id)
        {
            var classCourse = await (from cls in _context.ClassCourse.Where(cls => cls.Id == id)
                                     from lv in _context.StudyLevel.Where(lv => cls.LevelId == lv.Id)
                                     from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId).DefaultIfEmpty()
                                     from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId).DefaultIfEmpty()
                                     select new ClassCourseGrid()
                                     {
                                         Id = cls.Id,
                                         Level = lv.Name,
                                         Class = cls.Name,
                                         Description = cls.Description,
                                         Schedule = sch.ScheduleFormat,
                                         StartTimeLocal = sch.StartTimeLocal,
                                         EndTimeLocal = sch.EndTimeLocal,
                                         StartDate = sch.StartDateLocal,
                                         EndDate = sch.EndDateLocal,
                                         NumberOfStudents = (from cs in cls.ClassStudents.Where(cs => cs.StudentType == ClassType.Regular &&
                                                                                                      cs.Student.SuspendDate == null &&
                                                                                                      cs.Student.TerminateDate == null)
                                                             select cs
                                                             ).Count(),
                                         Notification = cls.AcadAnnounces.OrderByDescending(x => x.CreatedDate).FirstOrDefault().Content
                                     })
                                     .FirstOrDefaultAsync().ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }

            return classCourse;
        }

        // GET: api/ClassCourses/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClassCourse>> GetClassCourse(string id)
        {
            var classCourse = await _context.ClassCourse
                                        .Where(l => l.Id == id)
                                        .Include(l => l.Level)
                                        .Include(l => l.Schedule)
                                        .Include(l => l.ClassTeachers)
                                            .ThenInclude(t => t.Teacher)
                                                .ThenInclude(u => u.UserInfo)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }

            return classCourse;
        }

        // PUT: api/ClassCourses/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutClassCourse(string id, ClassCourse classCourse)
        {
            if (id != classCourse.Id)
            {
                return BadRequest();
            }
            _context.Entry(classCourse).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ClassCourseExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/ClassCourses
        [HttpPost]
        public async Task<ActionResult<ClassCourse>> PostClassCourse(ClassCourse classCourse)
        {
            _context.ClassCourse.Add(classCourse);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            // Migrate lesson by level for new class
            var lessons = await _context.LessonPlan
                                    .Include(x => x.Level)
                                    .Where(x => x.LevelId == classCourse.LevelId && !x.IsDeleted)
                                    .OrderBy(x => x.CreatedDate)
                                    .ToListAsync().ConfigureAwait(false);

            lessons.ForEach(lesson =>
            {
                ClassLesson obj = new ClassLesson()
                {
                    ClassId = classCourse.Id,
                    LessonId = lesson.Id
                };
                _context.ClassLesson.Add(obj);
            });
            _ = _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetClassCourse", new { id = classCourse.Id }, classCourse);
        }

        // DELETE: api/ClassCourses/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<ClassCourse>> DeleteClassCourse(string id)
        {
            var classCourse = await _context.ClassCourse.FindAsync(id);
            if (classCourse == null)
            {
                return NotFound();
            }

            _context.ClassCourse.Remove(classCourse);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return classCourse;
        }

        private bool ClassCourseExists(string id)
        {
            return _context.ClassCourse.Any(e => e.Id == id);
        }

        // GET: api/ClassCourses
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<ClassCourse>>> GetClassCourseByTeacherId(string id)
        {
            var query = _context.ClassTeacher
                .Include(l => l.ClassCourse)
                .Where(x => x.TeacherId == id).Select(x => new ClassCourse()
                {
                    Id = x.ClassId,
                    Name = x.ClassCourse.Name
                });
            return await query.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ClassCourse>>> GetClassCourseDropDown(string userId)
        {
            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = await _context.ClassCourse
                                    .Where(cls => assignedCampus == null || assignedCampus.Contains(cls.CampusId))
                                    .OrderBy(cls => cls.Name).ToListAsync().ConfigureAwait(false);

            var user = await _context.AspNetUsers.Where(x => x.Id == userId).Include(x => x.Role).FirstOrDefaultAsync();
            if (user?.Role.Name == EnumsHelper.GetDescription(AcadRoles.Teacher))
            {
                var classTeachers = await _context.ClassTeacher.Where(x => x.TeacherId.Equals(userId)).ToListAsync();
                return result.Where(x => classTeachers.Select(x => x.ClassId).Contains(x.Id)).ToList();
            }

            return result;
        }

        [HttpPost("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ClassCourse>>> GetClassesWithFilter(string userId, AcademicFilterParams academicFilterParams)
        {
            var assignedCampus = string.IsNullOrEmpty(academicFilterParams.Campus)
                                        ? await _acadManageService.GetAssignedCampus(userId)
                                        : new List<string> { academicFilterParams.Campus };

            var result = from cls in _context.ClassCourse
                         join sch in _context.Schedule on cls.Id equals sch.ClassCourseId into lj_sch
                         from _sch in lj_sch.DefaultIfEmpty()
                         where (assignedCampus == null || assignedCampus.Contains(cls.CampusId)) &&
                               (string.IsNullOrEmpty(academicFilterParams.Level) || cls.LevelId == academicFilterParams.Level)
                         orderby cls.Name
                         select cls;

            if (result.Count() > 0)
            {
                var user = await _context.AspNetUsers.Where(x => x.Id == userId).Include(x => x.Role).FirstOrDefaultAsync();
                if (user?.Role.Name == EnumsHelper.GetDescription(AcadRoles.Teacher))
                {
                    var classTeachers = await _context.ClassTeacher.Where(x => x.TeacherId.Equals(userId)).ToListAsync();
                    return await result.Distinct().Where(x => classTeachers.Select(x => x.ClassId).Contains(x.Id)).ToListAsync().ConfigureAwait(false);
                }
            }

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassCourseSchedule()
        {
            var data = await (from cc in _context.ClassCourse
                              join s in _context.Schedule on cc.Id equals s.ClassCourseId into lj
                              from _s in lj.DefaultIfEmpty()
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId
                              join u in _context.AspNetUsers on ct.TeacherId equals u.Id into ljU
                              from _u in ljU.DefaultIfEmpty()
                              join l in _context.StudyLevel on cc.LevelId equals l.Id
                              //where teacherId != ct.TeacherId
                              select new ClassCourseGrid()
                              {
                                  Id = cc.Id,
                                  Level = l.Name,
                                  Class = cc.Name,
                                  Description = cc.Description,
                                  Schedule = _s != null ? _s.ScheduleFormat : "",
                                  StartTimeLocal = _s != null ? _s.StartTimeLocal : "",
                                  EndTimeLocal = _s != null ? _s.EndTimeLocal : "",
                                  Teacher = _u.FirstName + " " + _u.LastName,
                                  ScheduleObj = _s
                              }).ToListAsync().ConfigureAwait(false);
            var result = (from d in data
                          group d by new { d.Id, d.Level, d.Class, d.Description, d.Schedule, d.StartTimeLocal, d.EndTimeLocal, d.ScheduleObj }
                into g
                          select new ClassCourseGrid()
                          {
                              Id = g.Key.Id,
                              Level = g.Key.Level,
                              Class = g.Key.Class,
                              Description = g.Key.Description,
                              Schedule = g.Key.Schedule,
                              StartTimeLocal = g.Key.StartTimeLocal,
                              EndTimeLocal = g.Key.EndTimeLocal,
                              ScheduleObj = g.Key.ScheduleObj,
                              Teacher = string.Join(", ", g.Select(x => x.Teacher)),
                              TeachingProccess = "0"
                          }).ToList();
            foreach (var item in result)
            {
                if (item.ScheduleObj != null && item.ScheduleObj.StartDate < item.ScheduleObj.EndDate)
                {
                    var schedule = item.ScheduleObj;
                    int studentTime = 0;
                    DateTime dtNow = DateTime.Now;
                    List<DayOfWeek> lstDayOfWeeks = new List<DayOfWeek>();
                    if (schedule.Monday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Monday);
                    }
                    if (schedule.Tuesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Tuesday);
                    }
                    if (schedule.Wednesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Wednesday);
                    }
                    if (schedule.Thursday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Thursday);
                    }
                    if (schedule.Friday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Friday);
                    }
                    if (schedule.Saturday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Saturday);
                    }
                    if (schedule.Sunday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Sunday);
                    }

                    DateTime tempDate;
                    if (schedule.StartDate > dtNow)
                    {
                        studentTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                        tempDate = schedule.StartDate;
                    }
                    else
                    {
                        studentTime = schedule.EndDate.Subtract(dtNow).Days;
                        tempDate = dtNow;
                    }

                    int countDay = 0;
                    for (int i = 0; i < studentTime; i++)
                    {
                        var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (checkDayOfWeeks)
                        {
                            countDay++;
                        }
                        tempDate = tempDate.AddDays(1);
                    }

                    item.TeachingProccess = countDay.ToString();
                }
            }

            return result;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<ClassCourseGrid>> GetNewClassCourseSchedule(string classId)
        {
            var data = await (from cc in _context.ClassCourse
                              join s in _context.Schedule on cc.Id equals s.ClassCourseId into lj
                              from _s in lj.DefaultIfEmpty()
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId into ljCT
                              from _ct in ljCT.DefaultIfEmpty()
                              join u in _context.AspNetUsers on _ct.TeacherId equals u.Id into ljU
                              from _u in ljU.DefaultIfEmpty()
                              join l in _context.StudyLevel on cc.LevelId equals l.Id
                              where classId == cc.Id
                              select new ClassCourseGrid()
                              {
                                  Id = cc.Id,
                                  Level = l.Name,
                                  Class = cc.Name,
                                  Description = cc.Description,
                                  Schedule = _s != null ? _s.ScheduleFormat : "",
                                  StartTimeLocal = _s != null ? _s.StartTimeLocal : "",
                                  EndTimeLocal = _s != null ? _s.EndTimeLocal : "",
                                  Teacher = _u.FirstName + " " + _u.LastName,
                                  ScheduleObj = _s
                              }).ToListAsync().ConfigureAwait(false);
            var result = (from d in data
                          group d by new { d.Id, d.Level, d.Class, d.Description, d.Schedule, d.StartTimeLocal, d.EndTimeLocal, d.ScheduleObj }
                into g
                          select new ClassCourseGrid()
                          {
                              Id = g.Key.Id,
                              Level = g.Key.Level,
                              Class = g.Key.Class,
                              Description = g.Key.Description,
                              Schedule = g.Key.Schedule,
                              StartTimeLocal = g.Key.StartTimeLocal,
                              EndTimeLocal = g.Key.EndTimeLocal,
                              ScheduleObj = g.Key.ScheduleObj,
                              Teacher = string.Join(", ", g.Select(x => x.Teacher)),
                              TeachingProccess = "0"
                          }).ToList();
            foreach (var item in result)
            {
                if (item.ScheduleObj != null && item.ScheduleObj.StartDate < item.ScheduleObj.EndDate)
                {
                    var schedule = item.ScheduleObj;
                    int studentTime = 0;
                    DateTime dtNow = DateTime.Now;
                    List<DayOfWeek> lstDayOfWeeks = new List<DayOfWeek>();
                    if (schedule.Monday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Monday);
                    }
                    if (schedule.Tuesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Tuesday);
                    }
                    if (schedule.Wednesday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Wednesday);
                    }
                    if (schedule.Thursday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Thursday);
                    }
                    if (schedule.Friday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Friday);
                    }
                    if (schedule.Saturday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Saturday);
                    }
                    if (schedule.Sunday)
                    {
                        lstDayOfWeeks.Add(DayOfWeek.Sunday);
                    }

                    DateTime tempDate;
                    if (schedule.StartDate > dtNow)
                    {
                        studentTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                        tempDate = schedule.StartDate;
                    }
                    else
                    {
                        studentTime = schedule.EndDate.Subtract(dtNow).Days;
                        tempDate = dtNow;
                    }

                    int countDay = 0;
                    for (int i = 0; i < studentTime; i++)
                    {
                        var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (checkDayOfWeeks)
                        {
                            countDay++;
                        }
                        tempDate = tempDate.AddDays(1);
                    }

                    item.TeachingProccess = countDay.ToString();
                }
            }

            return result.FirstOrDefault();
        }

        [HttpGet("[action]")]
        public async Task<ActionResult> AddMissingClassLesson()
        {
            try
            {
                await _classLessonService.AddMissingClassLesson();
            }
            catch (Exception ex)
            {
                _logger.LogError($"AddMissingClassLesson - {ex.Message}");
            }
            return Ok(true);
        }

        [HttpGet("[action]/{levelId}")]
        public async Task<ActionResult> AddMissingClassLesson(string levelId)
        {
            try
            {
                await _classLessonService.AddMissingClassLesson(levelId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"AddMissingClassLesson - {ex.Message}");
            }
            return Ok(true);
        }
    }
}
