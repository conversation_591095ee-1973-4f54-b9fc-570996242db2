﻿using System;
using System.Collections.Generic;

namespace GeeO.Data.Dto
{
    public class StudentExamResult
    {
        public string StudentId { get; set; }
        public string StudentName { get; set; }
        public string EnglishName { get; set; }
        public int ExamType { get; set; }
        public string TeacherComment { get; set; }
        public string ExamResultJson { get; set; }
    }

    public class ClassExamResult
    {
        public int ExamType { get; set; }
        public List<StudentExamResult> ExamResult { get; set; }
    }
}
