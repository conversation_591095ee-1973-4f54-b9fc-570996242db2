update [GeeODb].[dbo].[Student]
set [Birthday] = DATEADD(hh, 7, [Birthday])
where DATEPART(HOUR, [Birthday]) = 17 AND DATEPART(MINUTE, [Birthday]) = 0 AND DATEPART(SECOND, [Birthday]) = 0

update [GeeODb].[dbo].[StudentCourse]
set [NumberOfSession] = case when [NumberOfSession] is null then [Lessons]
							 else [NumberOfSession] * 2 end
where [PeriodType] = 1


update [GeeODb].[dbo].[TeacherLessonLog]
set [LogDateTime] = DATEADD(hh, -7, [LogDateTime])
  where CONVERT(date, [LogDateTime]) > CONVERT(date, '2021-03-13')
