﻿select distinct std.Id as 'StudentId'
			  , std.StudentName
			  , std.EnglishName
			  , std.Birthday
			  --, FORMAT(std.Birthday, 'dd/MM/yyyy') as Birthday
			  , case when std.SuspendDate is not null then N'Bảo lưu'
					when cs.ClassType = 1 then N'Regular'
					when cs.ClassType = 3 then N'Demo'
					else '' end as StudentType
			  ,cls.[Name] as 'Class'
from Student std
left join ClassStudent cs on std.Id = cs.StudentId 
left join ClassCourse cls on cs.ClassId = cls.Id
where std.TerminateDate is null and std.StudentName <> ''
order by std.StudentName