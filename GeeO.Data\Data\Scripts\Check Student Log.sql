/****** <PERSON><PERSON>t for SelectTopNRows command from SSMS  ******/
SELECT std.[Id]
      ,std.StudentName
      ,std.[EnglishName]
	  ,cls.[Id] AS ClassId
	  ,cls.[Name] AS ClassName
	  ,lp.Id as 'LessonId'
	  ,lp.<PERSON>on
	  ,lp.Content
	  ,tll.LogDateTime
	  ,tll.HistoryLog as 'Deleted'
  FROM [GeeODb].[dbo].[StudentLessonLogData] sll
  join TeacherLessonLog tll on sll.LogId = tll.Id
  join ClassLesson cl on tll.ClassLessonId = cl.Id
  join ClassCourse cls on cl.ClassId = cls.Id
  join Student std on sll.StudentInfoId = std.Id
  join LessonPlan lp on cl.LessonId = lp.Id
  where StudentInfoId = '8880bcc4-3f85-41b7-abaf-20a6f122a6ae' and tll.HistoryLog = 1
  order by LogDateTime