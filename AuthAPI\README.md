# AuthAPI

## Overview

AuthAPI is a .NET 6 application that implements authentication using IdentityServer 4. It provides a secure way to manage user authentication and authorization.

## Project Structure

- **Controllers**
  -
- **Models**
  -
- **Services**
  -
- **Config**
  -
- **Program.cs**: Entry point of the application, setting up the web host and configuring services and middleware.
- **Startup.cs**: Configures the application's services and request pipeline.
- **appsettings.json**: Contains configuration settings for the application, such as connection strings and IdentityServer settings.
- **appsettings.Development.json**: Contains development-specific configuration settings, overriding values in appsettings.json when in the development environment.

- **appsettings.Staging.json**: Contains development-specific configuration settings, overriding values in appsettings.json when in the development environment.

## Setup Instructions

1. Clone the repository:

   ```
   git clone <repository-url>
   cd AuthAPI
   ```

2. Restore the dependencies:

   ```
   dotnet restore
   ```

3. Run the application:
   ```
   dotnet run
   ```

## Usage

## License

This project is licensed under the MIT License.
