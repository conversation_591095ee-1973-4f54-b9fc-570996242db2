﻿USE [GeeODb]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[CatchUpSchedules](
	[Id] [nvarchar](450) NOT NULL,
	[Type] [int] NOT NULL,
	[Name] [nvarchar](450) NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
	[ClassCourseId] [nvarchar](450) NULL,
	[StartTime] [datetime] NULL,
	[EndTime] [datetime] NULL,
	[ClassCourseCurrentId] [nvarchar](450) NULL,
	[StartDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[CreateDate] [datetime] NULL,
 CONSTRAINT [PK_CatchUpSchedules] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_AspNetUsers] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_AspNetUsers]
GO