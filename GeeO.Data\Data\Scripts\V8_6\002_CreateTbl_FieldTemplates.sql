USE [GeeODb]
GO

/****** Object:  Table [dbo].[FieldTemplates]    Script Date: 3/26/2025 5:22:10 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FieldTemplates](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[FieldName] [nvarchar](100) NULL,
	[TypeInput] [int] NULL,
	[Description] [nvarchar](max) NULL,
	[InputKey] [varchar](200) NULL,
	[InputMask] [varchar](200) NULL,
	[InitialData] [nvarchar](max) NULL,
	[DefaultDataSource] [nvarchar](max) NULL,
	[IsRequired] [bit] NULL,
	[IsHiddenField] [bit] NULL,
	[IsDeactivate] [bit] NULL,
	[DisplayOrder] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[FieldTemplates] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FieldTemplates] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FieldTemplates]  WITH CHECK ADD  CONSTRAINT [FK_FieldTemplates_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FieldTemplates] CHECK CONSTRAINT [FK_FieldTemplates_FormTemplate]
GO


