﻿using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class LessonController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IUploadService _uploadService;
        private readonly ILessonPlanService _lessonPlanService;
        private readonly IFileService _fileService;

        public LessonController(GeeODbContext context,
                                IUploadService uploadService,
                                ILessonPlanService lessonPlanService,
                                IFileService fileService)
        {
            _context = context;
            _uploadService = uploadService;
            _lessonPlanService = lessonPlanService;
            _fileService = fileService;
        }

        [HttpPost("[action]/{userId}/{studentId}/{classLessonId}")]
        public async Task<IActionResult> UploadFile(string userId, string studentId, string classLessonId, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(studentId) || string.IsNullOrEmpty(classLessonId) || files?.Count == 0)
            {
                return NotFound();
            }

            await _uploadService.UploadLessonMedia(studentId, classLessonId, files).ConfigureAwait(false);
            await _lessonPlanService.SaveLessonMedia(userId, studentId, classLessonId, files[0]?.FileName);

            return Ok(new { count = files.Count });
        }

        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> SetLessonMediaSeen(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            await _lessonPlanService.SetLessonMediaSeen(id);

            return Ok();
        }

        [HttpGet("[action]/{studentId}/{classLessonId}/{filename}")]
        public IActionResult GetForView(string studentId, string classLessonId, string filename)
        {
            if (string.IsNullOrEmpty(studentId) || string.IsNullOrEmpty(classLessonId) || string.IsNullOrEmpty(filename))
            {
                return NotFound();
            }

            return _fileService.ViewDownloadLessonMedia(studentId, classLessonId, filename, false) ?? (IActionResult)NotFound();
        }

        [HttpGet("[action]/{studentId}/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<LessonMedia>>> GetLessonMediaList(string studentId, string classLessonId)
        {
            List<LessonMedia> lessonMedia = await (from lm in _context.LessonMedia.Where(lm => lm.StudentId == studentId &&
                                                                                               lm.ClassLessonId == classLessonId)
                                                   orderby lm.UploadDateTime descending
                                                   select lm)
                                                    .ToListAsync().ConfigureAwait(false);

            //foreach (LessonMedia media in lessonMedia)
            //{
            //}

            return lessonMedia;
        }

    }
}
