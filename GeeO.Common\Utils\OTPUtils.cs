﻿using System.Text;
using System.Globalization;
using System.Collections.Generic;
using System;

namespace GeeO.Common
{
    public static class OTPUtils
    {
        static readonly string[] saAllowedCharacters = { "1", "2", "3", "4", "5", "6", "7", "8", "9", "0" };

        public static string GenerateRandomOTP(int iOTPLength)
        {
            string sOTP = string.Empty;
            Random rand = new();

            for (int i = 0; i < iOTPLength; i++)
            {
                _ = rand.Next(0, saAllowedCharacters.Length);
                string sTempChars = saAllowedCharacters[rand.Next(0, saAllowedCharacters.Length)];
                sOTP += sTempChars;
            }
            return sOTP;
        }
    }
}
