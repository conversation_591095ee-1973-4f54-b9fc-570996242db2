import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import authService from '../../api-authorization/AuthorizeService';
//import CustomizedTable from '../ui/table/CustomizedTable';
import MyLessonPresentTable from './MyLessonPresentTable';
import RunStatusBox from './RunStatusBox';
import { Loading } from '../../ui/Loading';
import MyClassLessonInfo from '../MyClassLessonInfo';
//import { MyClassPaths } from './MyClassConstants';

const stylesRunMyLesson = () => ({});

class RunMyLessonComp extends Component {
  static displayName = RunMyLessonComp.name;

  constructor(...args) {
    super(...args);
    this.state = {
      classLessonId: this.props.classLessonId,
      myLessonData: [],
      runStatusBox: [],
      runStatusData: [],
      currentRunIdx: 0,
      classLesson: {},
      loading: true
    };
  }

  componentDidMount() {
    this.props.onRef(this);
    this.populateMyClassData();
  }

  async populateMyClassData() {
    const token = await authService.getAccessToken();
    //get lesson units
    const response = await fetch(
      `api/MyClasses/GetMyLesson/${this.props.classLessonId}`,
      {
        headers: !token ? {} : { Authorization: `Bearer ${token}` }
      }
    );
    const data = await response.json();
    this.setState({ myLessonData: data });
    this.populateRunStatusData();
    this.props.getItemCount(data.length);

    //get class lesson info
    const responseClassInfo = await fetch(
      `api/MyClasses/GetMyClassLesson/${this.props.classLessonId}`,
      {
        headers: !token ? {} : { Authorization: `Bearer ${token}` }
      }
    );
    const dataClassInfo = await responseClassInfo.json();
    this.setState({ classLesson: dataClassInfo, loading: false });
  }

  populateRunStatusData() {
    let runStatusDataArr = [];
    let runStatusBoxArr = [];
    this.state.myLessonData.map((lesson, idx) => {
      runStatusDataArr[idx] = {
        started: false,
        timeElapsed: 0,
        child: React.createRef()
      };
      runStatusBoxArr[idx] = (
        <RunStatusBox
          onRef={actualChild => (runStatusDataArr[idx].child = actualChild)}
          timeInMinutes={lesson.time}
        />
      );
      return idx;
    });
    this.setState({
      runStatusData: runStatusDataArr,
      runStatusBox: runStatusBoxArr
    });
  }

  componentWillUnmount() {
    if (this.state.timerElapsedId !== undefined) {
      clearInterval(this.state.timerElapsedId);
    }
  }

  timerElapsed = () => {
    const { currentRunIdx, runStatusData: runStatusDataArr } = this.state;
    runStatusDataArr[currentRunIdx].timeElapsed++;
    this.setState({ runStatusData: runStatusDataArr });
    runStatusDataArr[currentRunIdx].child.updateElapsedTime(
      runStatusDataArr[currentRunIdx].timeElapsed
    );
  };

  startLesson() {
    const { currentRunIdx, runStatusData: runStatusDataArr } = this.state;
    if (runStatusDataArr[currentRunIdx].started === false) {
      runStatusDataArr[currentRunIdx].child.updateRunStatus('run');
      runStatusDataArr[currentRunIdx].started = true;
      this.setState({ runStatusData: runStatusDataArr });
      var intervalId = setInterval(this.timerElapsed, 1000);
      this.setState({ timerElapsedId: intervalId });
      this.openMaterial();
    } else {
      if (currentRunIdx < runStatusDataArr.length - 1) {
        //runStatusData[currentRunIdx].started = false;
        runStatusDataArr[currentRunIdx].child.updateRunStatus('done');
        let nextRunIdx = currentRunIdx + 1;
        runStatusDataArr[nextRunIdx].child.updateRunStatus('run');
        runStatusDataArr[nextRunIdx].started = true;
        this.setState(
          {
            currentRunIdx: nextRunIdx,
            runStatusData: runStatusDataArr
          },
          this.openMaterial
        );
        //this.openMaterial();
      } else {
        runStatusDataArr[currentRunIdx].child.updateRunStatus('done');
        if (this.state.timerElapsedId !== undefined) {
          clearInterval(this.state.timerElapsedId);
        }
      }
    }
  }

  openMaterial() {
    const { myLessonData, currentRunIdx } = this.state;
    let openUrl = null;
    if (myLessonData[currentRunIdx].materialFileName) {
      openUrl = `api/Materials/GetForView/${
        myLessonData[currentRunIdx].materialId
      }/${encodeURI(myLessonData[currentRunIdx].materialFileName)}`;
    } else if (myLessonData[currentRunIdx].materialUrl) {
      openUrl = myLessonData[currentRunIdx].materialUrl;
    }
    if (openUrl !== null) {
      window.open(openUrl, '_blank');
    }
  }

  renderDatagrid() {
    const cols = [
      { name: 'time', header: 'Time (mins)', align: 'center' },
      { name: 'procedures', header: 'Procedures', align: 'left' },
      { name: 'description', header: 'Description', align: 'left' },
      { name: 'materials', header: 'Materials', align: 'left' },
      {
        name: 'teacherActivities',
        header: "Teachers' activities",
        align: 'left'
      },
      {
        name: 'learningOutcome',
        header: "Students' learning outcome",
        align: 'left'
      },
      { name: 'note', header: 'Note', align: 'left' },
      {
        name: 'materialUrl',
        header: 'Material Link',
        align: 'left',
        noWrap: 'true'
      },
      {
        name: 'materialFileName',
        header: 'Material File Name',
        className: 'materialFName',
        align: 'left'
      }
    ];

    return (
      <MyLessonPresentTable
        rows={this.state.myLessonData}
        cols={cols}
        runStatusBox={this.state.runStatusBox}
      />
    );
  }

  render() {
    return (
      <Fragment>
        {this.state.loading ? (
          <Loading />
        ) : (
          <Fragment>
            <MyClassLessonInfo classLesson={this.state.classLesson} />
            {this.renderDatagrid()}
          </Fragment>
        )}
      </Fragment>
    );
  }
}

RunMyLessonComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const RunMyLesson = withStyles(stylesRunMyLesson)(RunMyLessonComp);
