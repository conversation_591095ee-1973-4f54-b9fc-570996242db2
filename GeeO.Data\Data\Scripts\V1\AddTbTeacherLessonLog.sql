USE [GeeODb]
GO

/****** Object:  Table [dbo].[TeacherLessonLog]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TeacherLessonLog](
	[Id] [nvarchar](450) NOT NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
	[ClassLessonId] [nvarchar](450) NOT NULL,
	[LogDateTime] [datetime] NULL,
 CONSTRAINT [PK_TeacherLessonLog] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TeacherLessonLog]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLog_ClassLessonId] FOREIGN KEY([ClassLessonId])
REFERENCES [dbo].[ClassLesson] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonLog] CHECK CONSTRAINT [FK_TeacherLessonLog_ClassLessonId]
GO

ALTER TABLE [dbo].[TeacherLessonLog]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonLog_TeacherId] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonLog] CHECK CONSTRAINT [FK_TeacherLessonLog_TeacherId]
GO

