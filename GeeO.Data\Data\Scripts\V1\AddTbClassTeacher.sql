/****** Object:  Table [dbo].[ClassTeacher]    Script Date: 20-Oct-19 2:43:00 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClassTeacher](
	[Id] [nvarchar](450) NOT NULL,
	[ClassId] [nvarchar](450) NOT NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
 CONSTRAINT [PK_ClassTeacher] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClassTeacher]  WITH CHECK ADD  CONSTRAINT [FK_ClassTeacher_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClassTeacher] CHECK CONSTRAINT [FK_ClassTeacher_ClassId]
GO

ALTER TABLE [dbo].[ClassTeacher]  WITH CHECK ADD  CONSTRAINT [FK_ClassTeacher_TeacherId] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClassTeacher] CHECK CONSTRAINT [FK_ClassTeacher_TeacherId]
GO


