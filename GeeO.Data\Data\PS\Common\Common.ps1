﻿
function Execute-DbQuery
{
    param([string]$Query)

	$dbSettings = $settings.database
    if ($dbSettings.user -eq "")
    {
        $result = Invoke-Sqlcmd -ServerInstance $dbSettings.server -Database $dbSettings.database -Query $Query
    }
    else
    {
        $result = Invoke-Sqlcmd -ServerInstance $dbSettings.server -Database $dbSettings.database -Username $dbSettings.user -Password $dbSettings.password -Query $Query
    }
	return $result
}

function Write-RunLog 
{
    param([string]$message, [bool]$isGetError = $false)
	$logLocation = $settings.logFolder
	if (!(Test-Path $logLocation))
	{
		New-Item -ItemType Directory -Path $logLocation > $null
		Write-Host "$logLocation folder created successfully"
	}

    Write-Log -logLocation $logLocation -message $message -isGetError $isGetError
}

function Send-Mail 
{
    Param ([string]$to, [string]$subject, [string]$content)
	$emailSettings = $settings.email
    Send-MailMessage -From $emailSettings.fromEmail -To $to -Subject $subject -Body $content -BodyAsHtml -SmtpServer $emailSettings.smtpServer
    Write-RunLog -message "Send email to $to - email content: $content"
}

function Get-LogFilename
{
	Param([string]$logLocation)

    $fileName = Get-Date -Format 'MMddyyyy'
    $fileName = $fileName + '.log'

    # create new log file if not existing
    $logFilePath = [IO.Path]::Combine($logLocation, $fileName)
	
	if ([IO.File]::Exists($logFilePath) -ne $true)
    {
        New-Item -Path $logLocation -Name $fileName -ItemType File > $null
    }
	
	return $logFilePath
}

function Write-Emptyline
{
	Param ([string]$logLocation)

	$logFilePath = Get-LogFilename $logLocation
	
	Write-Output -InputObject "`n" | Out-File -FilePath $logFilePath -Append
}

function Write-Log
{
    Param ([string]$logLocation, [Parameter(Mandatory=$true)][string]$message, [bool]$isGetError = $false)

    # create new log file if not existing
    $logFilePath = Get-LogFilename $logLocation

    $logType = if ($isGetError) { 'ERROR' } else { 'INFO' }

    $logDateTime = Get-Date

    Write-Output -InputObject "[$logDateTime] $logType - $message" | Out-File -FilePath $logFilePath -Append
    
    if ($error.count -gt 0) 
    {
        Write-Output -InputObject '================  Start Error  =====================' | Out-File -FilePath $logFilePath -Append
        foreach ($err in $error) 
        {
            Write-Output -InputObject "[$logDateTime] ERROR - $err" | Out-File -FilePath $logFilePath -Append
        }
        Write-Output -InputObject '================   End Error   =====================' | Out-File -FilePath $logFilePath -Append
        $error.Clear()
    }
}
