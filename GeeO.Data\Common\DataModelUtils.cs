﻿using System;
using System.Collections.Generic;
using System.Text;
using GeeO.Common;
using GeeO.Models;

namespace GeeO.Data.Common
{
    public class DataModelUtils
    {
        public static object CopyProperties(object source, object target)
        {
            ObjectUtils.CopyProperties(source, target);

            // copy properties of special objects
            if (source.GetType() == typeof(LessonPlanUnit) &&
                target.GetType() == typeof(LessonPlanUnitHistory))
            {
                LessonPlanUnitHistory historyUnit = (LessonPlanUnitHistory) target;
                historyUnit.HistoryUnitId = historyUnit.Id;
                historyUnit.Id = null;

                return historyUnit;
            }
            if (target.GetType() == typeof(TeacherLessonUnitHistory))
            {
                ((TeacherLessonUnitHistory) target).Id = null;
            }

            return target;
        }
    }
}
