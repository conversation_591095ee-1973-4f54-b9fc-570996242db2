﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Data.Utils;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TeacherReportController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;

        public TeacherReportController(GeeODbContext context, IStudentPaymentService studentPaymentService)
        {
            _context = context;
            _studentPaymentService = studentPaymentService;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<TeacherInfo>> GetTeacherInfo(string id)
        {
            var teacher = from tch in _context.AspNetUsers.Where(x => x.Id == id).Include(x => x.UserInfo)
                          select new TeacherInfo
                          {
                              Id = tch.Id,
                              Name = tch.FullName,
                              TitleName = tch.UserInfo.TitleName,
                              EnglishName = tch.UserInfo.EnglishName,
                              Phone = tch.PhoneNumber,
                              Email = tch.Email,
                              ElAccount = tch.UserInfo.ElAccount
                          };

            if (teacher == null)
            {
                return NotFound();
            }

            return await teacher.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<IEnumerable<TeacherClasses>>> GetTeacherClasses(string teacherId)
        {
            try
            {
                return await (from ct in _context.ClassTeacher
                                            where ct.TeacherId == teacherId
                                            join cls in _context.ClassCourse on ct.ClassId equals cls.Id
                                            join sch in _context.Schedule on cls.Id equals sch.ClassCourseId
                                            orderby sch.StartDate descending
                                            select new TeacherClasses()
                                            {
                                                ClassId = cls.Id,
                                                ClassName = cls.Name,
                                                Level = cls.Level.Name,
                                                CoTeacher = cls.ClassTeachers
                                                              .Where(x => x.TeacherId != teacherId)
                                                              .Select(x => x.Teacher.FullName)
                                                              .FirstOrDefault(),
                                                Time = $"{sch.StartTimeLocal} - {sch.EndTimeLocal} | {sch.ScheduleFormat}",
                                                NumberOfStudents = cls.ClassStudents
                                                                   .Where(x => x.StudentType == ClassType.Regular
                                                                              && (x.Student.SuspendDate == null || x.Student.SuspendDate > sch.EndTime)
                                                                              && (x.Student.TerminateDate == null || x.Student.TerminateDate > sch.EndTime))
                                                                   .Count(),
                                                RecentLesson = cls.ClassLessons
                                                                  .Where(x => x.StartTime < DateTime.Now)
                                                                  .OrderByDescending(x => x.StartTime)
                                                                  .Select(cl => $"Lesson {cl.Lesson.Lesson} - {cl.Lesson.Subject} - {cl.Lesson.Content}")
                                                                  .FirstOrDefault()
                                            }).ToListAsync();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<TeacherClassesStatistics>> GetClassLessonStatistics(string teacherId)
        {
            var lessonTimes = await (from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                                     from cl in _context.ClassLesson.Where(cl => cl.ClassId == ct.ClassId && cl.EndTime.Value < DateTime.Now)
                                     from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id /*&& tll.HistoryLog == false*/).DefaultIfEmpty()
                                     orderby cl.StartTime
                                     select new ClassLessonStatistics()
                                     {
                                         ClassId = cl.ClassId,
                                         ClassName = cl.ClassCourse.Name,
                                         Id = cl.Id,
                                         StandardLessonTime = cl.Lesson.Level.LessonTime.Value,
                                         LessonDurationInSeconds = tll.LogDatas.Select(ld => ld.Duration).Sum()
                                     }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, List<ClassLessonStatistics>> dict = lessonTimes.GroupBy(x => x.ClassName).ToDictionary(x => x.Key, x => x.ToList());
            List<string> classNames = new();
            List<int> lateRatios = new();
            List<int> earlyRatios = new();

            foreach (var className in dict.Keys)
            {
                List<ClassLessonStatistics> listRes = dict[className];
                int numberOfLessons = listRes.Count();
                int numberOfLate = listRes.Count(x => x.LessonDuration > x.StandardLessonTime);
                int numberOfEarly = listRes.Count(x => x.LessonDuration < x.StandardLessonTime);

                int lateRatio = (int)Math.Round((double)numberOfLate * 100 / numberOfLessons);
                int earlyRatio = (int)Math.Round((double)numberOfEarly * 100 / numberOfLessons);

                lateRatios.Add(lateRatio);
                earlyRatios.Add(earlyRatio);
                classNames.Add(className);
            }

            TeacherClassesStatistics classLessonStatistics = new()
            {
                ClassNames = classNames,
                LateRatio = lateRatios,
                EarlyRatio = earlyRatios,
            };

            return classLessonStatistics;
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<StudentResultStatistics>> GetStudentResults(string teacherId)
        {
            var data = await (from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                              from cls in _context.ClassCourse.Where(cls => cls.Id == ct.ClassId)
                              from sch in _context.Schedule.Where(sch => sch.ClassCourseId == cls.Id && sch.EndDate < DateTime.Now)
                              from cs in _context.ClassStudent.Where(cs => cs.ClassId == ct.ClassId /*&& cs.StudentType == ClassType.Regular*/)
                              from er in _context.ExamResult.Where(er => er.ClassId == cs.ClassId && er.StudentId == cs.StudentId).DefaultIfEmpty()
                              orderby sch.StartDate
                              select new StudentResult()
                              {
                                  ClassId = cls.Id,
                                  ClassName = cls.Name,
                                  StudentId = cs.StudentId,
                                  StudentResultJson = er.ExamResultJson
                              }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, List<StudentResult>> dict = data.GroupBy(x => x.ClassName).ToDictionary(x => x.Key, x => x.ToList());
            List<string> classNames = new();
            List<int> average = new();
            List<int> fair = new();
            List<int> good = new();
            List<int> exellent = new();

            foreach (var className in dict.Keys)
            {
                List<StudentResult> listRes = dict[className];
                int numOfStudents = listRes.Count;
                int numOfAverage = 0;
                int numOfFair = 0;
                int numOfGood = 0;
                int numOfExellent = 0;

                foreach (var res in listRes)
                {
                    if (string.IsNullOrEmpty(res.StudentResultJson))
                        continue;

                    ExamResultData resData = JsonConvert.DeserializeObject<ExamResultData>(res.StudentResultJson);
                    string resType = ExamResultUtils.CalcExamResult(resData.Academic);
                    switch (resType)
                    {
                        case "AVERAGE":
                            numOfAverage++;
                            break;
                        case "FAIR":
                            numOfFair++;
                            break;
                        case "GOOD":
                            numOfGood++;
                            break;
                        case "EXCELLENT":
                            numOfExellent++;
                            break;
                    }
                }
                //average.Add((int)((double)numOfAverage * 100 / numOfStudents));
                //fair.Add((int)((double)numOfFair * 100 / numOfStudents));
                //good.Add((int)((double)numOfGood * 100 / numOfStudents));
                //exellent.Add((int)((double)numOfExellent * 100 / numOfStudents));
                average.Add(numOfAverage);
                fair.Add(numOfFair);
                good.Add(numOfGood);
                exellent.Add(numOfExellent);

                classNames.Add(className);
            }

            StudentResultStatistics studentResultStatistics = new()
            {
                ClassNames = classNames,
                AverageRatio = average,
                FairRatio = fair,
                GoodRatio = good,
                ExellentRatio = exellent
            };

            return studentResultStatistics;
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<TeacherStatistics>> GetTeacherStatistics(string teacherId)
        {
            int sessionRemainingThreshold = 8;
            IQueryable<Student> students = GetStudents(teacherId);
            IQueryable<TeacherStatisticsData> dataRenew = GetDataRenew(teacherId);
            IQueryable<ClassLesson> dataDemo = GetDataDemo(teacherId);
            Dictionary<string, List<string>> attendingInWeek = await GetAttendingInWeek(teacherId).ConfigureAwait(false);
            Dictionary<string, int> renewByMonth = await GetStudentsRenew(teacherId).ConfigureAwait(false);
            var (mainTeacherCount, mainTeachersByMonth) = await GetTeacherStatistics(true, teacherId).ConfigureAwait(false);
            var (coTeacherCount, coTeachersByMonth) = await GetTeacherStatistics(false, teacherId).ConfigureAwait(false);

            List<string> studentsUnderThreshold = new List<string>();
            List<string> studentsOverThreshold = new List<string>();

            var studentList = await students.ToListAsync().ConfigureAwait(false);
            foreach (var student in studentList)
            {
                int remainingDays = await CalculateRemainingSessionDaysAsync(student);
                if (remainingDays < sessionRemainingThreshold)
                {
                    studentsUnderThreshold.Add(student.Id);
                }
                else
                {
                    studentsOverThreshold.Add(student.Id);
                }
            }

            var splitAttendingInWeek = new Dictionary<string, (int UnderThreshold, int OverThreshold)>();
            foreach (var day in attendingInWeek)
            {
                var studentIdsForDay = day.Value;
                int underThresholdCount = studentIdsForDay.Count(id => studentsUnderThreshold.Contains(id));
                int overThresholdCount = studentIdsForDay.Count(id => studentsOverThreshold.Contains(id));
                splitAttendingInWeek[day.Key] = (underThresholdCount, overThresholdCount);
            }

            TeacherStatistics teacherData = new()
            {
                TotalStudents = await students.CountAsync().ConfigureAwait(false),
                CurrentStudents = await dataRenew.CountAsync().ConfigureAwait(false),
                RenewStudents = await dataRenew.CountAsync(x => x.RenewDate.Value > x.ClassStartDate.Date).ConfigureAwait(false),
                RenewCoTeachers = coTeacherCount,
                RenewMainTeachers = mainTeacherCount,
                DemoSessions = await dataDemo.CountAsync().ConfigureAwait(false),
                TotalClasses = await _context.ClassTeacher.CountAsync(ct => ct.TeacherId == teacherId).ConfigureAwait(false),
                TotalLessons = await (from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                                      from cl in _context.ClassLesson.Where(cl => cl.ClassId == ct.ClassId && cl.EndTime < DateTime.Now)
                                      select cl).CountAsync().ConfigureAwait(false),
                CatchUpSessions = await _context.CatchUpSchedules.CountAsync(ct => ct.TeacherId == teacherId).ConfigureAwait(false),
                AttendingInWeek = splitAttendingInWeek,
                RenewByMonth = renewByMonth,
                Today = DateTime.Now,
                CountStudentsSessionRemainUnderThreshold = studentsUnderThreshold.Count(),
                RenewMainTeachersByMonth = mainTeachersByMonth,
                RenewCoTeachersByMonth = coTeachersByMonth,
            };
            return teacherData;
        }

        private async Task<int> CalculateRemainingSessionDaysAsync(Student student)
        {
            var totalSessions = await _studentPaymentService.GetNumberOfSessions(student.Id);
            var totalStudiedSessions = await _studentPaymentService.GetNumberOfStudiedSessions(student.Id);
            var totalRemainSessions = totalSessions < totalStudiedSessions ? 0 : totalSessions - totalStudiedSessions;
            return totalRemainSessions;
        }

        private async Task<(int teacherCount, Dictionary<string, int> teachersByMonth)> GetTeacherStatistics(bool isMain, string teacherId)
        {
            var teachersRenew = await (from sc in _context.StudentCourse
                                       where (isMain ? sc.MainTeacherId == teacherId : sc.CoTeacherId == teacherId)
                                             && sc.Type == TypeEnum.Renew && sc.StartDate != null
                                       select new
                                       {
                                           RenewDate = sc.StartDate,
                                           RenewMonth = sc.StartDate.Value.ToString("MM/yyyy")
                                       }).ToListAsync().ConfigureAwait(false);

            int teacherCount = teachersRenew.Count;
            var groupedTeachers = teachersRenew
                .GroupBy(tr => tr.RenewMonth)
                .ToDictionary(g => g.Key, g => g.Count());

            return (teacherCount, groupedTeachers);
        }

        private async Task<Dictionary<string, int>> GetStudentsRenew(string teacherId)
        {
            var studentsRenew = await (from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                                       from cs in _context.ClassStudent.Where(cs => cs.ClassId == ct.ClassId)
                                       from sc in _context.StudentCourse.Where(sc => sc.StudentId == cs.StudentId && sc.StartDate != null)
                                       orderby sc.StartDate
                                       select new TeacherStatisticsData()
                                       {
                                           StudentId = cs.StudentId,
                                           RenewDate = sc.StartDate,
                                           RenewMonth = sc.StartDate.Value.ToString("MM/yyyy")
                                       }).ToListAsync().ConfigureAwait(false);

            Dictionary<string, int> renewByMonth = studentsRenew.GroupBy(x => x.RenewMonth).ToDictionary(x => x.Key, x => x.Count());

            return renewByMonth;
        }

        private async Task<Dictionary<string, List<string>>> GetAttendingInWeek(string teacherId)
        {
            var monday = DateUtils.GetMondayThisWeek().Date;
            var sunday = DateUtils.GetSundayThisWeek().Date;

            var classData = await (from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                                   from cl in _context.ClassLesson.Where(cl => cl.ClassId == ct.ClassId
                                                                                && cl.StartTime.Value.Date >= monday
                                                                                && cl.StartTime.Value.Date <= sunday)
                                   select new
                                   {
                                       Date = cl.StartTime.Value.Date,
                                       StudentIds = cl.ClassCourse.ClassStudents
                                                       .Where(x => x.StudentType == ClassType.Regular
                                                                   && x.Student.SuspendDate == null
                                                                   && x.Student.TerminateDate == null)
                                                       .Select(x => x.Student.Id)
                                   }).ToListAsync().ConfigureAwait(false);

            Dictionary<DayOfWeek, string> daysInWeek = new()
            {
                [DayOfWeek.Monday] = "Thứ 2",
                [DayOfWeek.Tuesday] = "Thứ 3",
                [DayOfWeek.Wednesday] = "Thứ 4",
                [DayOfWeek.Thursday] = "Thứ 5",
                [DayOfWeek.Friday] = "Thứ 6",
                [DayOfWeek.Saturday] = "Thứ 7",
                [DayOfWeek.Sunday] = "CN",
            };
            Dictionary<string, List<string>> attendingInWeek = new()
            {
                ["Thứ 2"] = new List<string>(),
                ["Thứ 3"] = new List<string>(),
                ["Thứ 4"] = new List<string>(),
                ["Thứ 5"] = new List<string>(),
                ["Thứ 6"] = new List<string>(),
                ["Thứ 7"] = new List<string>(),
                ["CN"] = new List<string>(),
            };

            foreach (var item in classData)
            {
                string dayOfWeek = daysInWeek[item.Date.DayOfWeek];
                if (attendingInWeek.ContainsKey(dayOfWeek))
                {
                    attendingInWeek[dayOfWeek].AddRange(item.StudentIds);
                }
            }

            return attendingInWeek;
        }

        private IQueryable<ClassLesson> GetDataDemo(string teacherId)
        {
            return from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                   from cs in _context.ClassStudent.Where(cs => cs.ClassId == ct.ClassId && cs.StudentType == ClassType.Demo)
                   from cl in _context.ClassLesson.Where(cl => cl.ClassId == cs.ClassId && cl.EndTime < DateTime.Now)
                   select cl;
        }

        private IQueryable<Student> GetStudents(string teacherId)
        {
            return from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                   from cs in _context.ClassStudent.Where(cs => cs.ClassId == ct.ClassId && cs.StudentType == ClassType.Regular)
                   from std in _context.Student.Where(std => std.Id == cs.StudentId && std.SuspendDate == null && std.TerminateDate == null)
                   select std;
        }

        private IQueryable<TeacherStatisticsData> GetDataRenew(string teacherId)
        {
            return from ct in _context.ClassTeacher.Where(ct => ct.TeacherId == teacherId)
                   from sch in _context.Schedule.Where(sch => sch.ClassCourseId == ct.ClassId && sch.EndDate > DateTime.Today)
                   from cs in _context.ClassStudent.Where(cs => cs.ClassId == ct.ClassId && cs.StudentType == ClassType.Regular)
                   from std in _context.Student.Where(std => std.Id == cs.StudentId && std.SuspendDate == null && std.TerminateDate == null)
                   select new TeacherStatisticsData
                   {
                       ClassId = ct.ClassId,
                       RenewDate = std.StudentCourses.Max(x => x.StartDate),
                       ClassStartDate = sch.StartDate
                   };
        }
    }
}
