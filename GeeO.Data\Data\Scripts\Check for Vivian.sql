﻿select cs.*, std.StudentName, cls.Name as ClassName
from ClassStudent cs
join Student std on std.Id = cs.StudentId
join ClassCourse cls on cls.Id = cs.ClassId
where cs.StudentId = 'dcbfb68f-197d-488f-8852-e292573e00c5' or cs.StudentType = 4


select sll.*, std.StudentName
from StudentLessonLogData sll
join Student std on std.Id = sll.StudentInfoId
where std.Id = 'dcbfb68f-197d-488f-8852-e292573e00c5'


select sll.*, cl.ClassId, cls.Name as ClassName, cs.ClassId as ClassStudentId, cs.StudentId, std.StudentName
from StudentLessonLogData sll
join TeacherLessonLog tll on tll.Id = sll.LogId
join ClassLesson cl on cl.Id = tll.ClassLessonId
join ClassCourse cls on cls.Id = cl.ClassId
join Student std on std.Id = sll.StudentInfoId
left join ClassStudent cs on cs.ClassId = cl.ClassId and cs.StudentId = sll.StudentInfoId
where cs.ClassId is null and sll.StudentInfoId = 'dcbfb68f-197d-488f-8852-e292573e00c5'

select distinct sll.StudentInfoId, cl.ClassId, cs.ClassId as ClassStudentId
from StudentLessonLogData sll
join TeacherLessonLog tll on tll.Id = sll.LogId
join ClassLesson cl on cl.Id = tll.ClassLessonId
left join ClassStudent cs on cs.ClassId = cl.ClassId and cs.StudentId = sll.StudentInfoId
where cs.ClassId is null
order by StudentInfoId, ClassId


			select cl.Id, lp.Lesson, cl.StartTime as 'LessonDate',
				   std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			from StudentLessonLogData sll
			join TeacherLessonLog tll on sll.LogId = tll.Id and tll.HistoryLog = 0
			join ClassLesson cl on tll.ClassLessonId = cl.Id
			join LessonPlan lp on cl.LessonId = lp.Id
			join Student std on sll.StudentInfoId = std.Id
			where cl.ClassId = '6b48ed16-d645-470a-98be-ec52c0e4b7d6'
			order by cl.StartTime, std.Id

select *
from Student
where StudentName = N'Nguyễn Ngọc Bảo Vy'