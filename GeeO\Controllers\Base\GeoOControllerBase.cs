﻿using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;

namespace GeeO.Controllers
{
    public class GeoOControllerBase : ControllerBase
    {
        protected readonly UserManager<ApplicationUser> _userManager;

        //class constructor
        public GeoOControllerBase(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        protected void AddContentType(string filename)
        {
            System.Net.Mime.ContentDisposition cd = new System.Net.Mime.ContentDisposition
            {
                FileName = Uri.EscapeDataString(filename),
                Inline = true  // false = prompt the user for downloading;  true = browser to try to show the file inline
            };
            Response.Headers.Add("Content-Disposition", cd.ToString());
            Response.Headers.Add("X-Content-Type-Options", "nosniff");
            Response.Headers.Add("Accept-Ranges", "bytes");

            //var cookieOptions = new CookieOptions
            //{
            //    // Set the secure flag, which Chrome's changes will require for SameSite none.
            //    // Note this will also require you to be running on HTTPS.
            //    Secure = true,

            //    // Set the cookie to HTTP only which is good practice unless you really do need
            //    // to access it client side in scripts.
            //    HttpOnly = true,

            //    // Add the SameSite attribute, this will emit the attribute with a value of none.
            //    // To not emit the attribute at all set
            //    // SameSite = (SameSiteMode)(-1)
            //    SameSite = SameSiteMode.None
            //};

            //// Add the cookie to the response cookie collection
            //Response.Cookies.Append("MyCookie", "cookieValue", cookieOptions);
        }

    }
}