﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace GeeO.Data.Migrations
{
    public partial class CreateGeeOSchema : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "StudyLevel",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    Name = table.Column<string>(maxLength: 256, nullable: true),
                    Description = table.Column<string>(maxLength: 256, nullable: true),
                    NumberOfLesson = table.Column<int>(nullable: true),
                    LessonTime = table.Column<int>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StudyLevel", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LessonPlan",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    LevelId = table.Column<string>(nullable: false),
                    Lesson = table.Column<string>(maxLength: 256, nullable: true),
                    Subject = table.Column<string>(maxLength: 256, nullable: true),
                    Content = table.Column<string>(maxLength: 512, nullable: true),
                    Tb = table.Column<string>(maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LessonPlan", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LessonPlan_StudyLevel_LevelId",
                        column: x => x.LevelId,
                        principalTable: "StudyLevel",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Material",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    Name = table.Column<string>(maxLength: 256, nullable: true),
                    Description = table.Column<string>(maxLength: 256, nullable: true),
                    MaterialFormat = table.Column<int>(nullable: true),
                    Url = table.Column<string>(maxLength: 1024, nullable: true),
                    FileName = table.Column<string>(maxLength: 256, nullable: true),
                    TeacherId = table.Column<string>(nullable: true),
                    Approved = table.Column<bool>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Material", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LessonPlanUnit",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    LessonPlanId = table.Column<string>(nullable: false),
                    MaterialId = table.Column<string>(nullable: true),
                    SortOrder = table.Column<int>(nullable: true),
                    Time = table.Column<int>(nullable: true),
                    Procedures = table.Column<string>(maxLength: 512, nullable: true),
                    Description = table.Column<string>(maxLength: 2048, nullable: true),
                    Materials = table.Column<string>(maxLength: 512, nullable: true),
                    TeacherActivities = table.Column<string>(maxLength: 2048, nullable: true),
                    LearningOutcome = table.Column<string>(maxLength: 2048, nullable: true),
                    Note = table.Column<string>(maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LessonPlanUnit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LessonPlanUnit_LessonPlan_LessonPlanId",
                        column: x => x.LessonPlanId,
                        principalTable: "LessonPlan",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LessonPlanUnit_MaterialId",
                        column: x => x.MaterialId,
                        principalTable: "Material",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ClassCourse",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    LevelId = table.Column<string>(nullable: false),
                    Name = table.Column<string>(maxLength: 256, nullable: true),
                    Description = table.Column<string>(maxLength: 512, nullable: true),
                    Course = table.Column<string>(maxLength: 512, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClassCourse", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClassCourse_LevelId",
                        column: x => x.LevelId,
                        principalTable: "StudyLevel",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ClassTeacher",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    ClassId = table.Column<string>(nullable: false),
                    TeacherId = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClassTeacher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClassTeacher_ClassId",
                        column: x => x.ClassId,
                        principalTable: "ClassCourse",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ClassTeacher_TeacherId",
                        column: x => x.TeacherId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ClassLesson",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    ClassId = table.Column<string>(nullable: false),
                    LessonId = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClassLesson", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClassLesson_ClassId",
                        column: x => x.ClassId,
                        principalTable: "ClassCourse",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ClassLesson_LessonId",
                        column: x => x.LessonId,
                        principalTable: "LessonPlan",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateTable(
                name: "TeacherLessonUnit",
                columns: table => new
                {
                    Id = table.Column<string>(nullable: false),
                    TeacherId = table.Column<string>(nullable: false),
                    ClassLessonId = table.Column<string>(nullable: false),
                    UnitId = table.Column<string>(nullable: false),
                    MaterialId = table.Column<string>(nullable: true),
                    Note = table.Column<string>(maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TeacherLessonUnit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TeacherLessonUnit_TeacherId",
                        column: x => x.TeacherId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TeacherLessonUnit_ClassLessonId",
                        column: x => x.ClassLessonId,
                        principalTable: "ClassLesson",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TeacherLessonUnit_UnitId",
                        column: x => x.UnitId,
                        principalTable: "LessonPlanUnit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_TeacherLessonUnit_MaterialId",
                        column: x => x.MaterialId,
                        principalTable: "Material",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TeacherLessonUnit");
            migrationBuilder.DropTable(
                name: "ClassLesson");
            migrationBuilder.DropTable(
                name: "ClassTeacher");
            migrationBuilder.DropTable(
                name: "ClassCourse");
            migrationBuilder.DropTable(
                name: "LessonPlanUnit");
            migrationBuilder.DropTable(
                name: "LessonPlan");
            migrationBuilder.DropTable(
                name: "Material");
            migrationBuilder.DropTable(
                name: "StudyLevel");
        }
    }
}
