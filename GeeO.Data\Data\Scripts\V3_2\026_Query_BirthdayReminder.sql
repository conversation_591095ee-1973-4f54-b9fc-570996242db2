﻿DECLARE @TimeCheckBirthday datetime = DATEADD(dd, 7, GETDATE());

INSERT INTO [dbo].[Notifications]
				([Id]
				,[ClassId]
				,[Title]
				,[Content]
				,[StartTime]
				,[EndTime]
				,[CreatedDate]
				,[CreatedBy])
SELECT 
				LOWER(CONVERT(nvarchar(450), NEWID())) AS Id
				,cls.Id
				,N'Sắp đến sinh nhật học sinh ' + IIF(NULLIF(std.EnglishName, '') IS NULL, '', std.EnglishName + ', ') + std.StudentName + N' (' + FORMAT(std.Birthday, 'dd/MM/yyyy') + N') tại lớp ' + cls.[Name]
				,N'Thông báo sinh nhật học sinh'
				,GETDATE()
				,DATEADD(mi, 5, GETDATE())
				,GETDATE()
				,'sysadmin'
FROM ClassStudent cs
JOIN Student std ON cs.StudentId = std.Id AND DAY(std.Birthday) = DAY(@TimeCheckBirthday) AND MONTH(std.Birthday) = MONTH(@TimeCheckBirthday)
JOIN ClassCourse cls ON cs.ClassId = cls.Id
JOIN Schedule sch ON cs.ClassId = sch.ClassCourseId AND CONVERT(date, sch.EndDate) >= CONVERT(date, GETDATE())
