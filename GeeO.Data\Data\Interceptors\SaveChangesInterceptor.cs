using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.Linq;
using GeeO.Data.Models.Interfaces;
using System;
using GeeO.Data.Models;
using GeeO.Data.Extensions;
using GeeO.Common;
using GeeO.Models;
using System.Text.Json;

public class SaveChangesInterceptor : Microsoft.EntityFrameworkCore.Diagnostics.SaveChangesInterceptor
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SaveChangesInterceptor(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        var context = eventData.Context;
        var user = _httpContextAccessor.HttpContext.User;
        var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

        UpdateBaseEntityEntries(context, userId);

        if (context.ChangeTracker.Entries().Any(e => e.Entity is ITrackableEntity))
        {
            TrackChanges(context, userId);
        }

        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        var user = _httpContextAccessor.HttpContext.User;
        var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

        UpdateBaseEntityEntries(context, userId);

        if (context.ChangeTracker.Entries().Any(e => e.Entity is ITrackableEntity))
        {
            TrackChanges(context, userId);
        }

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void UpdateBaseEntityEntries(DbContext context, string userId)
    {
        var entries = context.ChangeTracker.Entries()
        .Where(e => e.Entity is BaseEntity &&
                (e.State == EntityState.Added || e.State == EntityState.Modified));

        DateTime currentTime = DateTime.UtcNow;

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;

            if (entry.State == EntityState.Added)
            {
                entity.CreatedBy = userId;
                entity.CreatedAt = currentTime;
            }

            entity.UpdatedBy = userId;
            entity.UpdatedAt = currentTime;
        }
    }

    private void TrackChanges(DbContext context, string userId)
    {
        var entries = context.ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted).ToList();

        foreach (var entry in entries)
        {
            var action = entry.GetActivityState();
            CreateActivityLog(context, entry, action, userId);
        }
    }

    private void CreateActivityLog(DbContext context, EntityEntry entry, ActivityActionType action, string userId)
    {
        string tableName = entry.Entity.GetType().Name;
        string recordId = entry.Properties.FirstOrDefault(p => p.Metadata.IsPrimaryKey())?.CurrentValue.ToString();

        var changes = entry.State == EntityState.Added || entry.State == EntityState.Modified ? entry.Properties
            .Select(p => new
            {
                Field = p.Metadata.Name,
                OldValue = entry.State == EntityState.Modified ? p.OriginalValue : string.Empty,
                NewValue = p.CurrentValue
            })
            .ToList() : null;

        var activityLog = new ActivityLog(
            userId,
            tableName,
            recordId,
            action.GetEnumMemberValue(),
            $"{GeeOConstants.ActionTitles[action]} {tableName}",
            changes != null ? JsonSerializer.Serialize(changes) : null
        );
        _ = context.Add(activityLog);
    }
}