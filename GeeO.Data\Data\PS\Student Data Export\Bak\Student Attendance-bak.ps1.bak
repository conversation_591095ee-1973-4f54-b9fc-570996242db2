﻿###
### CAREFULLY RUNNING THIS SCRIPT!!! It will kill any that runs for more than 20 minutes!!!
###

# read settings 
$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

# load common utilities
.$PSScriptRoot/Common.ps1


# declare sql in a variable and pass it to -Query switch
$sqlServer = $settings.database.server
$database = $settings.database.database
$outFolderRoot = $settings.outFolder


function Get-StudentAttendance
{
    $time = Get-Date -Format "yyyyMMdd-HHmmss"
    Write-RunLog "====================== Starting ======================"

	$outFolder = $outFolderRoot
	
	if (!(Test-Path $outFolder))
	{
		New-Item -ItemType Directory -Path $outFolder > $null
		Write-Host "$outFolder folder created successfully"
	}

	$ExcelObject = New-Object -ComObject Excel.Application  
	$ExcelObject.Visible = $false 
	$ExcelObject.DisplayAlerts =$false

    $sqlClass="
    select cls.Id as 'ClassId', cls.[Name] as 'ClassName'
	from ClassCourse cls
	where cls.[Name] not like '%test%' and cls.[Name] not like 'SC%'
    "
	$classList = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlClass

    foreach ($class in $classList)
    {
        $outFile = Join-Path $outFolder "$($class.ClassName).xlsx" # name of file to export
        Write-Host $outFile
		Write-Output "Class: $($class.ClassName)"
		
		# Create Excel file  
		$ActiveWorkbook = $ExcelObject.Workbooks.Add()  
		$ActiveWorksheet = $ActiveWorkbook.Worksheets.Item(1)
		$ActiveWorksheet.Columns(4).NumberFormat = "dd/MM/yyyy HH:mm"
		$ActiveWorksheet.Columns(7).NumberFormat = "dd/MM/yyyy"

		#$sheetName = $lesson.LessonDate.ToString("dd-MM-yyyy")
		#$ActiveWorksheet.Name = $sheetName

		$ActiveWorksheet.Cells.Item(1,1) = "ClassLessonId"
		$ActiveWorksheet.Cells.Item(1,2) = "StudentId"
		$ActiveWorksheet.Cells.Item(1,3) = "Lesson"
		$ActiveWorksheet.Cells.Item(1,4) = "DateTime"
		$ActiveWorksheet.Cells.Item(1,5) = "StudentName"
		$ActiveWorksheet.Cells.Item(1,6) = "EnglishName"
		$ActiveWorksheet.Cells.Item(1,7) = "Birthday"
		$ActiveWorksheet.Cells.Item(1,8) = "Presence"
		$ActiveWorksheet.Cells.Item(1,9) = "StarScore"
		$ActiveWorksheet.Cells.Item(1,10) = "Note"
			
		$rowIndex = 2

		$sqlLesson="
			select cl.Id, lp.Lesson, cl.StartTime as 'LessonDate',
				   std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			from ClassLesson cl 
			join LessonPlan lp on cl.LessonId = lp.Id
			join ClassStudent cs on cl.ClassId = cs.ClassId
			join Student std on cs.StudentId = std.Id
			left join TeacherLessonLog tll on cl.Id = tll.ClassLessonId 
			left join StudentLessonLogData sll on tll.Id = sll.LogId and std.Id = sll.StudentInfoId
			where cl.ClassId = '$($class.ClassId)'
			order by cl.StartTime, std.Id
		"

        $classLessons = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlLesson
            
        foreach ($lesson in $classLessons)
        {

			$sqlLessonLog = "
			select std.Id as 'StudentId', std.StudentName, std.EnglishName, std.Birthday,
				   sll.Present, sll.StarScore, sll.Note, tll.Id as 'LogId', tll.LogDateTime
			from ClassLesson cl 
			join ClassStudent cs on cl.ClassId = cs.ClassId
			join Student std on cs.StudentId = std.Id
			left join TeacherLessonLog tll on cl.Id = tll.ClassLessonId 
			left join StudentLessonLogData sll on tll.Id = sll.LogId
			where cl.Id = '$($lesson.Id)'
			order by std.Id
			"
			
			$lessonLogs = Invoke-Sqlcmd -ServerInstance $sqlserver -Database $database -Query $sqlLessonLog
			
			foreach ($lessonLog in $lessonLogs)
			{
				$ActiveWorksheet.Cells.Item($rowIndex,1) = "$($lesson.Id)"
				$ActiveWorksheet.Cells.Item($rowIndex,2) = "$($lessonLog.StudentId)"
				$ActiveWorksheet.Cells.Item($rowIndex,3) = "$($lesson.Lesson)"
				$ActiveWorksheet.Cells.Item($rowIndex,4) = "$($lesson.LessonDate)"
				$ActiveWorksheet.Cells.Item($rowIndex,5) = "$($lessonLog.StudentName)"
				$ActiveWorksheet.Cells.Item($rowIndex,6) = "$($lessonLog.EnglishName)"
				$ActiveWorksheet.Cells.Item($rowIndex,7) = $lessonLog.Birthday
				$ActiveWorksheet.Cells.Item($rowIndex,8) = "$($lessonLog.Present)"
				$ActiveWorksheet.Cells.Item($rowIndex,9) = "$($lessonLog.StarScore)"
				$ActiveWorksheet.Cells.Item($rowIndex,10) = "$($lessonLog.Note)"
				
				$rowIndex++
			}
        }
		#$ActiveWorksheet.UsedRange.EntireColumn.AutoFit()
		$ActiveWorksheet.UsedRange.EntireColumn.AutoFilter()
		$ActiveWorksheet.Columns.Item("D:G").EntireColumn.AutoFit()
		$ActiveWorkbook.SaveAs($outFile)
    }

	$ExcelObject.Quit()
    Write-RunLog "====================== Ending ======================"
}

Write-Host "PowerShell version $($PSVersionTable.PSVersion)"

Get-StudentAttendance
