﻿CREATE FUNCTION [dbo].[SearchTextRemovedDiacritics] (
@search_exp nvarchar(max),
@exp_to_be_searched nvarchar(max)
)   RETURNS bit

AS BEGIN
DECLARE @diacritic_removed nvarchar(max) = LOWER([dbo].[RemoveDiacritics](@exp_to_be_searched));
DECLARE @result bit = CASE
						WHEN EXISTS (SELECT * FROM (SELECT value AS 'text_value' FROM STRING_SPLIT(@diacritic_removed, ' ')) t WHERE text_value = LOWER(@search_exp)) THEN 1
						ELSE 0
					  END;

return @result
END;
