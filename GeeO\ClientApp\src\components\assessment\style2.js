const stylesPresenceStudent = theme => ({
  paper: {
    color: '#707070',
    width: '100%',
    maxWidth: 1400,
    minHeight: '100vh',
    margin: 'auto'
  },
  root: {
    flexGrow: 1,
    alignItems: 'stretch'
  },
  contentCol: {
    flexGrow: 1
  },
  navCol: {},
  footer: {},
  studentName: {
    lineHeight: '34px'
  },
  label: {
    fontSize: 14,
    color: '#707070',
    padding: theme.spacing(1)
  },
  labelBold: {
    fontWeight: 'bold'
  },
  box: {
    margin: theme.spacing(4, 0),
    textAlign: 'right'
  },
  button: {
    margin: theme.spacing(0, 1.5),
    borderRadius: theme.spacing(2.25),
    minWidth: 100
  },
  checkBox: {
    width: '30%',
    textAlign: 'right'
  },
  checkPresence: {
    marginTop: 62
  },
  checkLabel: {
    cursor: 'pointer'
  }
});
