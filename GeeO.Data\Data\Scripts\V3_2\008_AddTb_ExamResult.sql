USE [GeeODb]
GO

/****** Object:  Table [dbo].[ExamResult]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ExamResult](
	[Id] [nvarchar](450) NOT NULL,
	ClassId [nvarchar](450) NULL,
	StudentId [nvarchar](450) NULL,
	ExamType int NOT NULL DEFAULT 0,
	[ExamResultJson] [nvarchar](MAX) NULL,
	TeacherComment [nvarchar](max) NULL,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_ExamResult] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ExamResult]  WITH CHECK ADD  CONSTRAINT [FK_ExamResult_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
GO
ALTER TABLE [dbo].[ExamResult] CHECK CONSTRAINT [FK_ExamResult_ClassId]
GO

ALTER TABLE [dbo].[ExamResult]  WITH CHECK ADD  CONSTRAINT [FK_ExamResult_StudentId] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO
ALTER TABLE [dbo].[ExamResult] CHECK CONSTRAINT [FK_ExamResult_StudentId]
GO

