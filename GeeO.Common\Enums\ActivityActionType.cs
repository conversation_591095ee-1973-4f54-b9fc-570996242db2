using System.Runtime.Serialization;

public enum ActivityActionType
{
    [EnumMember(Value = "imported")]
    Imported,
    [EnumMember(Value = "created")]
    Created,
    [EnumMember(Value = "updated")]
    Updated,
    [EnumMember(Value = "deleted")]
    Deleted,
    [EnumMember(Value = "copied")]
    Copied,
    [EnumMember(Value = "archived")]
    Archived,
    [EnumMember(Value = "arranged")]
    Arranged,
    [EnumMember(Value = "started")]
    Started,
    [EnumMember(Value = "exported")]
    Exported,
    [EnumMember(Value = "performed")]
    Performed,
    [EnumMember(Value = "suspended")]
    Suspended,
    [EnumMember(Value = "terminated")]
    Terminated,
    [EnumMember(Value = "launched")]
    Launched,
    [EnumMember(Value = "approved")]
    Approved
}
