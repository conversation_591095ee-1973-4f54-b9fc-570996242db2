/****** Object:  Table [dbo].[ClassLesson]    Script Date: 20-Oct-19 2:42:02 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClassLesson](
	[Id] [nvarchar](450) NOT NULL,
	[ClassId] [nvarchar](450) NOT NULL,
	[LessonId] [nvarchar](450) NOT NULL,
	[StartTime] [datetime],
	[EndTime] [datetime],
 CONSTRAINT [PK_ClassLesson] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClassLesson]  WITH CHECK ADD  CONSTRAINT [FK_ClassLesson_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClassLesson] CHECK CONSTRAINT [FK_ClassLesson_ClassId]
GO

ALTER TABLE [dbo].[ClassLesson]  WITH CHECK ADD  CONSTRAINT [FK_ClassLesson_LessonId] FOREIGN KEY([LessonId])
REFERENCES [dbo].[LessonPlan] ([Id])
GO

ALTER TABLE [dbo].[ClassLesson] CHECK CONSTRAINT [FK_ClassLesson_LessonId]
GO
