﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using GeeO.GridVo;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class LessonCommentController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public LessonCommentController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/LessonComment/GetLessonComments/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<LessonComment>>> GetLessonComments(string classLessonId)
        {
            var result = _context.LessonComment
                    .Where(c => c.ClassLessonId == classLessonId)
                    .Include(c => c.ClassLesson)
                    .Include(c => c.User)
                        .ThenInclude(u => u.Role)
                    .OrderBy(lc => lc.DateTime)
                    .AsNoTracking();
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // POST: api/LessonComment
        [HttpPost("[action]")]
        public async Task<ActionResult<LessonComment>> Create(LessonComment lessonComment)
        {
            lessonComment.DateTime = DateTime.Now;
            _context.LessonComment.Add(lessonComment);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("LessonComment/Create", new { id = lessonComment.Id }, lessonComment);
        }

        // DELETE: api/LessonComment/Delete/5
        [HttpDelete("[action]/{id}")]
        public async Task<ActionResult<LessonComment>> Delete(string id)
        {
            var lessonComment = await _context.LessonComment.FindAsync(id);
            if (lessonComment == null)
            {
                return NotFound();
            }

            _context.LessonComment.Remove(lessonComment);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return lessonComment;
        }
    }
}
