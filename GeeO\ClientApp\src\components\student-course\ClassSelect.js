import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import authService from '../api-authorization/AuthorizeService';
import { Loading } from '../ui/Loading';
import { PagingTable } from '../ui/table/PagingTable';
import CommonSearchToolbar from '../ui/table/CommonSearchToolbar';

const stylesList = () => ({});

class CLassSelectComp extends Component {
  static displayName = CLassSelectComp.name;
  constructor(...args) {
    super(...args);
    this.child = React.createRef();
    this.state = {
      data: [],
      loading: true,
      catchUp: this.props.catchUp
    };
  }

  componentDidMount() {
    this.populateData();
  }

  populateData = async () => {
    const token = await authService.getAccessToken();
    const response = await fetch(`api/ClassCourses`, {
      headers: !token ? {} : { Authorization: `Bearer ${token}` }
    });
    const data = await response.json();
    this.setState({
      data: data,
      loading: false
    });
    this.setChildDataSelect();
  };

  setChildDataSelect = () => {
    const { data } = this.state;
    this.child.setData(data);
  };

  callbackGetValueRadioBtn = e => {
    this.props.callbackGetValueRadioBtn(e);
  };

  handleChange = filterText => {
    this.setChildData(filterText);
  };

  setChildData = filterText => {
    const { data } = this.state;
    const searchTerm = filterText.toLowerCase();
    let filteredRows = data.filter(item => {
      const itemText = (
        item.level +
        item.class +
        item.schedule +
        item.startTimeLocal +
        item.endTimeLocal
      ).toLowerCase();
      return itemText.indexOf(searchTerm) !== -1;
    });
    this.child.setData(filteredRows);
  };

  render() {
    const cols = [
      { name: 'level', header: 'Level', align: 'right' },
      { name: 'class', header: 'Class', align: 'right' },
      { name: 'schedule', header: 'Schedule', align: 'right' },
      { name: 'startTimeLocal', header: 'Start Time', align: 'right' },
      { name: 'endTimeLocal', header: 'End Time', align: 'right' }
    ];
    return (
      <Fragment>
        {this.state.loading ? (
          <Loading />
        ) : (
          <Fragment>
            <CommonSearchToolbar handleChange={this.handleChange} />
            <PagingTable
              onRef={actualChild => (this.child = actualChild)}
              cols={cols}
              isShowRadioButton="true"
              callbackGetValueRadioBtn={this.callbackGetValueRadioBtn}
            />
          </Fragment>
        )}
      </Fragment>
    );
  }
}

CLassSelectComp.propTypes = {
  classes: PropTypes.object.isRequired
};
export default withStyles(stylesList)(CLassSelectComp);
