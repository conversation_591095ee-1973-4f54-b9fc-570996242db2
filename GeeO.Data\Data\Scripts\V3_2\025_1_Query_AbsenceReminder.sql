﻿INSERT INTO [dbo].[Notifications]
		([Id]
		,[ClassId]
		,[Title]
		,[Content]
		,[StartTime]
		,[EndTime]
		,[CreatedDate]
		,[CreatedBy])
SELECT
		LOWER(CONVERT(nvarchar(450), NEWID()))
		,ClassId
		,N'Ngày ' + LessonDate + N' học sinh ' + StudentName + N' lớp ' + ClassName + N' vắng mặt.'
		,N'Thông báo học sinh vắng mặt.'
		,GETDATE()
		,DATEADD(mi, 5, GETDATE())
		,GETDATE()
		,'sysadmin'
FROM
(
	SELECT IIF(NULLIF(std.EnglishName, '') IS NULL, '', std.EnglishName + ', ') + std.StudentName AS StudentName,
			cls.Id AS ClassId,
			cls.[Name] AS ClassName,
			FORMAT(cl.StartTime, 'dd/MM/yyyy') AS LessonDate,
			sll.Present AS Presence
	FROM Student std WITH(NOLOCK)
	JOIN StudentLessonLogData sll WITH(NOLOCK) ON std.Id = sll.StudentInfoId
	JOIN TeacherLessonLog tll WITH(NOLOCK) ON sll.LogId = tll.Id
	JOIN ClassLesson cl WITH(NOLOCK) ON tll.ClassLessonId = cl.Id
	JOIN ClassCourse cls WITH(NOLOCK) ON cl.ClassId = cls.Id
	WHERE sll.Present = 0 AND CONVERT(date, cl.StartTime) = DATEADD(day, -1, CONVERT(date, GETDATE()))
) t
