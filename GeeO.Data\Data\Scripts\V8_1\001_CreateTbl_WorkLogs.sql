
USE [GeeODb]
GO

/****** Object:  Table [dbo].[WorkLogs]    Script Date: 1/3/2024 11:23:53 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WorkLogs](
	[Id] [nvarchar](450) NOT NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
	[SubsForTeacherId] [nvarchar](450) NOT NULL,
	[TeacherLessonLogId] [nvarchar](450) NOT NULL,
	[EffortType] [varchar](50) NULL,
	[Status] [int] NULL,
	[IsExpat] [bit] NULL,
	[Evaluate] [bit] NULL,
	[Comment] [nvarchar](max) NULL,
	[Order] [int] NULL,
	[Score] [float] NOT NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NOT NULL,
	[UpdatedBy] [nvarchar](450) NOT NULL,
	[LessonEnd] [datetime] NULL,
	[LessonStart] [datetime] NULL,
 CONSTRAINT [PK__WorkLogs__3214EC07A602A97B] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__Status__0E04126B]  DEFAULT ((0)) FOR [Status]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__IsExpa__0EF836A4]  DEFAULT ((0)) FOR [IsExpat]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__Evalua__0FEC5ADD]  DEFAULT ((0)) FOR [Evaluate]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__Order__10E07F16]  DEFAULT (NULL) FOR [Order]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF_WorkLogs_Score]  DEFAULT ((0)) FOR [Score]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__Create__11D4A34F]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WorkLogs] ADD  CONSTRAINT [DF__WorkLogs__Update__12C8C788]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[WorkLogs]  WITH CHECK ADD  CONSTRAINT [FK_WorkLogs_TeacherLessonLogs] FOREIGN KEY([TeacherLessonLogId])
REFERENCES [dbo].[TeacherLessonLog] ([Id])
GO

ALTER TABLE [dbo].[WorkLogs] CHECK CONSTRAINT [FK_WorkLogs_TeacherLessonLogs]
GO


