﻿using Microsoft.Extensions.Logging;

namespace GeeO.Common.FileLogging
{
    public class FileLoggerProvider : ILoggerProvider
    {
        private readonly string path;
        private readonly string logLevel;

        public FileLoggerProvider(string _path, string _logLevel)
        {
            path = _path;
            logLevel = _logLevel;
        }

        public ILogger CreateLogger(string categoryName)
        {
            return new FileLogger(path, logLevel);
        }

        public void Dispose()
        {
        }
    }
}
