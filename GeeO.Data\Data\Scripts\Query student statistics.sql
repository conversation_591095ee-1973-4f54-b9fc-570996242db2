with res as
(
select std.StudentName
	  ,std.EnglishName
	  ,(select SUM(NumberOfSession) from StudentCourse sc where sc.StudentId = std.Id) as 'TotalNumberOfSessions'
	  ,(select COUNT(*) from StudentLessonLogData sll
	    join TeacherLessonLog tll on tll.Id = sll.LogId and tll.HistoryLog = 0
		where sll.StudentInfoId = std.Id) as 'StudiedNumberOfSessions'
	  ,(select SUM(Amount) from StudentCourse sc where sc.StudentId = std.Id) as 'TotalPayments'
from ClassCourse cls
join ClassStudent cs on cs.ClassId = cls.Id and cs.StudentType = 1
join Student std on std.Id = cs.StudentId and std.SuspendDate is null and std.TerminateDate is null
where cls.Id = '9b237dc6-4f39-4bfc-b938-44166f8b8594'
) 

select StudentName
	  ,EnglishName
	  ,TotalNumberOfSessions
	  ,StudiedNumberOfSessions
	  ,[TotalNumberOfSessions] - [StudiedNumberOfSessions] as 'RemainNumberOfSessions'
	  ,TotalPayments
from res
order by StudentName
