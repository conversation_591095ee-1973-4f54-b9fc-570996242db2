import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import classNames from 'classnames';
import AppBar from '@material-ui/core/AppBar';
import Toolbar from '@material-ui/core/Toolbar';
import IconButton from '@material-ui/core/IconButton';
import Typography from '@material-ui/core/Typography';
import CloseIcon from '@material-ui/icons/Close';
import Fullscreen from 'react-full-screen';
import LetsLearnContent from './LetsLearnContent';

const useStylesLetsLearnScreen = theme => ({
  root: {
    backgroundColor: theme.palette.common.white,
    width: '100%',
    height: '100%'
  },
  appBar: {
    position: 'relative'
  },
  toolBar: {
    minHeight: 0,
    height: 0
  },
  closeButton: {
    marginTop: '52px',
    marginRight: '-22px',
    color: theme.palette.text.secondary
  },
  content: {},
  open: {
    display: 'block'
  },
  close: {
    display: 'none'
  },
  grow: {
    flexGrow: 1
  },
  title: {
    marginLeft: theme.spacing(1),
    flex: 1,
    color: theme.palette.common.white
  }
});

class LetsLearnScreen extends Component {
  constructor(...args) {
    super(...args);
    this.letsLearnContent = React.createRef();
    this.state = {
      open: false,
      previewMode: true,
      stateClass: 'close'
    };
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  openLetsLearnScreen(classLessonId, lessonLog, previewMode) {
    this.letsLearnContent.setLesson(classLessonId, lessonLog, previewMode);
    this.setState({ previewMode });
    this.handleClickOpen();
  }

  openLessonPlanPreviewScreen(lessonPlanId) {
    this.letsLearnContent.setLessonPlan(lessonPlanId);
    this.setState({ previewMode: true });
    this.handleClickOpen();
  }

  handleClickOpen = () => {
    this.setState({ stateClass: 'open', open: true });
  };

  handleClose = () => {
    this.setState({ stateClass: 'close', open: false });
    this.letsLearnContent.stopLesson();
  };

  handleFinish = () => {
    this.setState({ stateClass: 'close', open: false });
    this.props.handleFinish();
  };

  handleStateChange = openStatus => {
    if (openStatus === false) {
      this.handleClose();
    }
  };

  render() {
    const { classes } = this.props;
    return (
      <Fullscreen
        enabled={this.state.open}
        onChange={isFull => this.handleStateChange(isFull)}
      >
        <div
          className={classNames(classes.root, classes[this.state.stateClass])}
        >
          <AppBar className={classes.appBar}>
            <Toolbar className={classes.toolBar}>
              <Typography variant="h6" className={classes.title}>
                {/* Let's Learn{this.state.previewMode && ' - Preview Mode'} */}
              </Typography>
              <div className={classes.grow} />
              <IconButton
                className={classes.closeButton}
                edge="end"
                color="inherit"
                onClick={this.handleClose}
                aria-label="Close"
              >
                <CloseIcon />
              </IconButton>
            </Toolbar>
          </AppBar>
          <LetsLearnContent
            className={classes.content}
            onRef={actualChild => (this.letsLearnContent = actualChild)}
            handleFinish={this.handleFinish}
          />
        </div>
      </Fullscreen>
    );
  }
}

LetsLearnScreen.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withStyles(useStylesLetsLearnScreen)(LetsLearnScreen);
