﻿IF EXISTS (SELECT 1 FROM sys.objects WHERE [name] = N'trg_StudentAbsence' AND [type] = 'TR')
BEGIN
    DROP TRIGGER [dbo].[trg_StudentAbsence];
END
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER dbo.trg_StudentAbsence
   ON  dbo.StudentLessonLogData 
   AFTER INSERT, UPDATE
AS 
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @studentName nvarchar(500),
			@studentPresence bit = 1,
			@classId    nvarchar(450),
			@className  nvarchar(200),
			@logDate    nvarchar(50),
			@eventTitle nvarchar(1000);

	DECLARE @TimeToCheck datetime = SYSDATETIMEOFFSET() AT TIME ZONE 'SE Asia Standard Time';

	SELECT @studentName = IIF(NULLIF(std.EnglishName, '') IS NULL, '', std.EnglishName + ', ') + std.StudentName, 
			@classId = cls.Id,
			@className = cls.[Name], 
			@logDate = FORMAT(tll.LogDateTime, 'dd/MM/yyyy'),
			@studentPresence = sll.Present
	FROM Student std WITH(NOLOCK)
	JOIN INSERTED sll WITH(NOLOCK) ON std.Id = sll.StudentInfoId
	JOIN TeacherLessonLog tll WITH(NOLOCK) ON sll.LogId = tll.Id
	JOIN ClassLesson cl WITH(NOLOCK) ON tll.ClassLessonId = cl.Id
	JOIN ClassCourse cls WITH(NOLOCK) ON cl.ClassId = cls.Id

	SET @eventTitle = N'Ngày ' + @logDate + N' học sinh ' + @studentName + N' lớp ' + @className + N' vắng mặt.';

	IF (@studentPresence = 1)
	BEGIN
		IF EXISTS (SELECT * FROM [dbo].[Notifications] WHERE [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE()))
			DELETE FROM [dbo].[Notifications] WHERE [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE());
	END;

	IF (@studentPresence = 0)
	BEGIN
		IF NOT EXISTS (SELECT * FROM [dbo].[Notifications] WHERE [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE()))
			INSERT INTO [dbo].[Notifications]
						([Id]
						,[ClassId]
						,[Title]
						,[Content]
						,[StartTime]
						,[EndTime]
						,[CreatedDate]
						,[CreatedBy])
					VALUES
						(LOWER(CONVERT(nvarchar(450), NEWID()))
						,@classId
						,@eventTitle
						,N'Thông báo học sinh vắng mặt.'
						,@TimeToCheck
						,DATEADD(mi, 5, @TimeToCheck)
						,@TimeToCheck
						,'sysadmin');
	END;

END
GO
