ALTER TABLE [dbo].[ClassTeacher] ADD
	IsPrimary bit NOT NULL DEFAULT (0)
GO

DECLARE @ClassTeachers CURSOR;
DECLARE @ClassId nvarchar(450);
BEGIN
    SET @ClassTeachers = CURSOR FOR
    SELECT DISTINCT ClassId from dbo.ClassTeacher

    OPEN @ClassTeachers 
    FETCH NEXT FROM @ClassTeachers INTO @ClassId

    WHILE @@FETCH_STATUS = 0
    BEGIN
		UPDATE ClassTeacher SET IsPrimary = 1
		WHERE Id = (SELECT TOP 1 Id FROM ClassTeacher WHERE ClassId = @ClassId)

		FETCH NEXT FROM @ClassTeachers INTO @ClassId 
    END;

    CLOSE @ClassTeachers;
    DEALLOCATE @ClassTeachers;
END;