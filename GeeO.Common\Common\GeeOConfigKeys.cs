using System;
using System.Collections.Generic;

namespace GeeO.Common
{
        public partial class GeeOConfigKeys
        {
                public static string SmsApi = "SmsApi";
                public static string SmsApi_Url = "Url";
                public static string SmsApi_RQST = "RQST";
                public static string SSIS = "SSIS";
                public static string SSIS_TargetServerName = "TargetServerName";
                public static string SSIS_FolderName = "FolderName";
                public static string SSIS_ProjectName = "ProjectName";
                public static string SSIS_Packages = "Packages";
                public static string ImportLocation = "Import:Location";
                public static string ImportDone = "Import:Done";
                public static string ImportType = "Import:{0}";
                public static string ImportType_Overall = "Overall";
                public static string ImportType_LessonPlan = "LessonPlan";
                public static string EmailTemplateLessonContent = "EmailTemplates:LessonContent";
                public static string EmailTemplateESSLFeedbackContent = "EmailTemplates:eSSLFeedbackContent";
                public static string EmailSettings = "EmailSettings";
                public static string AzureAdElAccount = "AzureAdElAccount";
                public static string LessonContentStore = "LessonContent:Store";
                public static string LessonContentMedia = "LessonContent:Media";
                public static string LessonPlanStore = "LessonPlan:Store";
                public static string MaterialStore = "Material:Store";
                public static string Material_HtmlContent_DefaultIndexFile = "Material:HtmlContent:DefaultIndexFile";
                public static string Holidays = "Holidays";
                public static string GeeOStoryStore = "GeeOStory:Store";
                public static string ChatStore = "Chat:Store";
                public static string Media = "Media:Store";
                public static string UserInfo = "UserInfo:Store";
        }
}
