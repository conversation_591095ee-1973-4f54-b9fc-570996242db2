﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using GeeO.GridVo;
using System.Reflection;
using GeeO.Data.Models;
using System.Globalization;
using Microsoft.Extensions.Configuration;
using GeeO.Common;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class StudentAssessmentController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IConfiguration _config;

        public StudentAssessmentController(IConfiguration config, GeeODbContext context)
        {
            _context = context;
            _config = config;
        }

        // POST: api/StudentAssessment/AddNewStudentAssessment
        [HttpPost("[action]")]
        public async Task<ActionResult<StudentAssessment>> AddNewStudentAssessment(StudentAssessment studentAssessmentNew)
        {
            if (studentAssessmentNew == null)
            {
                return BadRequest();
            }

            _context.StudentAssessment.Add(studentAssessmentNew);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStudentAssessment", new { id = studentAssessmentNew.Id }, studentAssessmentNew);
        }

        // POST: api/StudentAssessment/AddStudentAssessment
        [HttpPost("[action]")]
        public async Task<ActionResult<StudentAssessment>> AddStudentAssessment(StudentAssessment studentAssessmentNew)
        {
            var studentAssessment = await _context.StudentAssessment.Where(l => l.StudentLessonLogId == studentAssessmentNew.StudentLessonLogId &&
                                                                                l.AssessmentCriteriaId == studentAssessmentNew.AssessmentCriteriaId)
                                        .FirstOrDefaultAsync().ConfigureAwait(false);

            if (studentAssessment == null)
            {
                return await AddNewStudentAssessment(studentAssessmentNew).ConfigureAwait(false);
            }

            return CreatedAtAction("GetStudentAssessment", new { id = studentAssessment.Id }, studentAssessment);
        }

        // PUT: api/StudentAssessment/SaveStudentAssessment/5
        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> SaveStudentAssessment(string id, StudentAssessment studentAssessment)
        {
            if (studentAssessment == null || id != studentAssessment.Id)
            {
                return BadRequest();
            }

            _context.Entry(studentAssessment).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LogDataExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // GET: api/StudentAssessment/GetStudentAssessmentOfClass/5
        [HttpGet("[action]/{classLessonId}")]
        public async Task<ActionResult<IEnumerable<Object>>> GetStudentAssessmentOfClass(string classLessonId)
        {
            var now = DateTime.Now;
            var result = from sa in _context.StudentAssessment
                         from acr in _context.AssessmentCriteria.Where(acr => acr.Id == sa.AssessmentCriteriaId)
                         from sll in _context.StudentLessonLogData.Where(sll => sll.Id == sa.StudentLessonLogId)
                         from tll in _context.TeacherLessonLog.Where(tll => tll.Id == sll.LogId)
                         where tll.ClassLessonId == classLessonId
                         select new
                         {
                             tll.ClassLessonId,
                             StudentId = sll.StudentInfoId,
                             CriteriaId = acr.Id,
                             acr.CriteriaNumber,
                             sa.Score
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/StudentAssessment/GetStudentAssessment/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<StudentAssessment>> GetStudentAssessment(string id)
        {
            var studentAssessment = await _context.StudentAssessment.FindAsync(id);

            if (studentAssessment == null)
            {
                return NotFound();
            }

            return studentAssessment;
        }

        // GET: api/StudentAssessment/GetAssessmentCriteria
        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<AssessmentCriteria>>> GetAssessmentCriteria()
        {
            return await _context.AssessmentCriteria.OrderBy(c => c.CriteriaNumber).ToListAsync().ConfigureAwait(false);
        }

        private bool LogDataExists(string id)
        {
            return _context.TeacherLessonLogData.Any(e => e.Id == id);
        }
    }
}