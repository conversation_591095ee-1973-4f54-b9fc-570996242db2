require('dotenv').config();
const express = require("express");
const http = require("http");
const socket = require("socket.io");

const app = express();
const server = http.createServer(app);
const io = socket(server);

const users = [];
const socketToRoom = [];

io.on('connection', socket => {
    socket.on("join room", ({ roomID, userRole, userId, userName }) => {
        socket.join(roomID);
        let info = { "socketId": socket.id, userRole, userId, userName, "video": true, "audio": true };
        console.log("==== socket id: " + socket.id + " === Join room: " + roomID + " === User name: " + userName);
        if (users[roomID]) {
            const length = users[roomID].length;
            if (length === 40) {
                socket.emit("room full");
                return;
            }
            users[roomID].push(info);
        } else {
            users[roomID] = [info];
        }
        console.log(users[roomID]);
        socketToRoom[socket.id] = roomID;
        const usersInThisRoom = users[roomID].filter(u => u.socketId !== socket.id);

        socket.emit("all users", usersInThisRoom);
    });

    socket.on("sending signal", payload => {
        console.log("sending signal " + payload.userToSignal);
        io.to(payload.userToSignal).emit('user joined', { signal: payload.signal, callerID: payload.callerID, userRole: payload.userRole, userId: payload.userId, userName: payload.userName });
    });

    socket.on("returning signal", payload => {
        console.log("returning signal " + payload.callerID);
        io.to(payload.callerID).emit('receiving returned signal', { signal: payload.signal, id: socket.id });
    });

    socket.on('disconnect', () => {
        const roomID = socketToRoom[socket.id];
        console.log("disconnect " + socket.id + ", leaving room " + roomID);
        let room = users[roomID];
        if (room) {
            const leavedUser = room.find(u => u.socketId === socket.id);
            room = room.filter(u => u.socketId !== socket.id);
            users[roomID] = room;
            io.emit("user leaved", leavedUser);
        }
        socket.leave(roomID);
    });

    socket.on('BE-toggle-camera-audio', ({ roomID, switchTarget }) => {
        // const user = users[roomId].find((u) => u.socket.id);
        // if (switchTarget === 'video') {
        //     socketList[socket.id].video = !socketList[socket.id].video;
        // } else {
        //     socketList[socket.id].audio = !socketList[socket.id].audio;
        // }
        console.log('BE-toggle-camera-audio:' + roomID);
        socket.broadcast
            .to(roomID)
            .emit('FE-toggle-camera', { userId: socket.id, switchTarget });
    });

    socket.on('BE-start-share-screen', ({ roomID, userRole, userId, userName }) => {
        console.log('BE-start-share-screen: ' + userId);
        socket
            .to(roomID)
            .emit('FE-start-share-screen', { userId: userId });
    });

    socket.on('BE-stop-share-screen', ({ roomID, userRole, userId, userName }) => {
        console.log('BE-stop-share-screen: ' + userId);
        socket
            .to(roomID)
            .emit('FE-stop-share-screen', { userId: userId });
    });

});

server.listen(process.env.PORT || 8000, () => console.log('server is running on port 8000'));


