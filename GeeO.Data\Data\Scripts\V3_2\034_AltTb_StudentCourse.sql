﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentCourse]    Script Date: 21-Mar-21 6:37:26 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER TABLE [dbo].[StudentCourse] DROP CONSTRAINT [FK_StudentCourse_ClassStudent]
GO

ALTER TABLE [dbo].[StudentCourse] ADD
	[StudentId] [nvarchar](450) NULL
GO

ALTER TABLE [dbo].[StudentCourse]  WITH CHECK ADD  CONSTRAINT [FK_StudentCourse_StudentId] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO
ALTER TABLE [dbo].[StudentCourse] CHECK CONSTRAINT [FK_StudentCourse_StudentId]
GO

UPDATE [StudentCourse] SET StudentId = (SELECT StudentId FROM ClassStudent WHERE Id = [StudentCourse].ClassStudentId)
GO


ALTER TABLE [dbo].[ClassStudent] ADD
	[StudentType] [int] NULL
GO
UPDATE [ClassStudent] SET [StudentType] = [ClassType]
GO
