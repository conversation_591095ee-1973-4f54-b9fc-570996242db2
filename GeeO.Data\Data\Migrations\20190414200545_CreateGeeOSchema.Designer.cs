// <auto-generated />
using System;
using GeeO.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace GeeO.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20190414200545_CreateGeeOSchema")]
    partial class CreateGeeOSchema
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.0.0-preview4.19216.3");

            modelBuilder.Entity("GeeO.Models.StudyLevel", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd();

                    b.Property<string>("Name")
                        .HasMaxLength(256);

                    b.Property<string>("Description")
                        .HasMaxLength(256);

                    b.Property<int>("NumberOfLesson");

                    b.Property<int>("LessonTime");

                    b.<PERSON>("Id");

                    b.ToTable("StudyLevel");
                });

            modelBuilder.Entity("GeeO.Models.Material", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("Name")
                    .HasMaxLength(256);

                b.Property<string>("Description")
                    .HasMaxLength(256);

                b.Property<int>("MaterialFormat");

                b.Property<string>("Url")
                    .HasMaxLength(1024);

                b.Property<string>("FileName")
                    .HasMaxLength(256);

                b.Property<string>("TeacherId");
                b.Property<bool>("Approved");

                b.HasKey("Id");

                b.ToTable("Material");
            });

            modelBuilder.Entity("GeeO.Models.LessonPlan", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("Lesson")
                    .HasMaxLength(256);

                b.Property<string>("Subject")
                    .HasMaxLength(256);

                b.Property<string>("Content")
                    .HasMaxLength(512);

                b.Property<string>("Tb")
                    .HasMaxLength(256);

                b.Property<string>("LevelId")
                    .IsRequired();

                b.HasKey("Id");

                b.ToTable("LessonPlan");
            });

            modelBuilder.Entity("GeeO.Models.LessonPlanUnit", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<int>("SortOrder");

                b.Property<int>("Time");

                b.Property<string>("Procedures")
                    .HasMaxLength(512);

                b.Property<string>("Description")
                    .HasMaxLength(2048);

                b.Property<string>("Materials")
                    .HasMaxLength(512);

                b.Property<string>("TeacherActivities")
                    .HasMaxLength(2048);

                b.Property<string>("LearningOutcome")
                    .HasMaxLength(2048);

                b.Property<string>("Note")
                    .HasMaxLength(1024);

                b.Property<string>("LessonPlanId")
                    .IsRequired();

                b.Property<string>("MaterialId");

                b.HasKey("Id");

                b.ToTable("LessonPlanUnit");
            });

            modelBuilder.Entity("GeeO.Models.ClassCourse", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("Name")
                    .HasMaxLength(256);

                b.Property<string>("Description")
                    .HasMaxLength(512);

                b.Property<string>("Course")
                    .HasMaxLength(512);

                b.Property<string>("LevelId")
                    .IsRequired();

                b.HasKey("Id");

                b.ToTable("ClassCourse");
            });

            modelBuilder.Entity("GeeO.Models.ClassTeacher", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("ClassId")
                    .IsRequired();

                b.Property<string>("TeacherId")
                    .IsRequired();

                b.HasKey("Id");

                b.ToTable("ClassTeacher");
            });

            modelBuilder.Entity("GeeO.Models.ClassLesson", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("ClassId")
                    .IsRequired();

                b.Property<string>("LessonId")
                    .IsRequired();

                b.HasKey("Id");

                b.ToTable("ClassLesson");
            });

            modelBuilder.Entity("GeeO.Models.TeacherLessonUnit", b =>
            {
                b.Property<string>("Id")
                    .ValueGeneratedOnAdd();

                b.Property<string>("TeacherId")
                    .IsRequired();

                b.Property<string>("ClassLessonId")
                    .IsRequired();

                b.Property<string>("UnitId")
                    .IsRequired();

                b.Property<string>("MaterialId")
                    .IsRequired();

                b.Property<string>("Note")
                    .HasMaxLength(1024);

                b.HasKey("Id");

                b.ToTable("TeacherLessonUnit");
            });

            modelBuilder.Entity("GeeO.Models.TeacherLessonUnit", b =>
            {
                b.HasOne("GeeO.Models.AspNetUsers")
                    .WithMany()
                    .HasForeignKey("TeacherId")
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne("GeeO.Models.ClassLesson")
                    .WithMany()
                    .HasForeignKey("ClassLessonId")
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne("GeeO.Models.LessonPlanUnit")
                    .WithMany()
                    .HasForeignKey("UnitId")
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne("GeeO.Models.Material")
                    .WithMany()
                    .HasForeignKey("MaterialId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity("GeeO.Models.ClassTeacher", b =>
            {
                b.HasOne("GeeO.Models.ClassCourse")
                    .WithMany()
                    .HasForeignKey("ClassId")
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne("GeeO.Models.AspNetUsers")
                    .WithMany()
                    .HasForeignKey("TeacherId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity("GeeO.Models.ClassLesson", b =>
            {
                b.HasOne("GeeO.Models.ClassCourse")
                    .WithMany()
                    .HasForeignKey("ClassId")
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne("GeeO.Models.LessonPlan")
                    .WithMany()
                    .HasForeignKey("LessonId")
                    .OnDelete(DeleteBehavior.Cascade);
            });


            modelBuilder.Entity("GeeO.Models.ClassCourse", b =>
            {
                b.HasOne("GeeO.Models.StudyLevel")
                    .WithMany()
                    .HasForeignKey("LevelId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity("GeeO.Models.Material", b =>
            {
                b.HasOne("GeeO.Models.AspNetUsers")
                    .WithMany()
                    .HasForeignKey("TeacherId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity("GeeO.Models.LessonPlan", b =>
            {
                b.HasOne("GeeO.Models.StudyLevel")
                    .WithMany()
                    .HasForeignKey("LevelId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity("GeeO.Models.LessonPlanUnit", b =>
            {
                b.HasOne("GeeO.Models.LessonPlan")
                    .WithMany()
                    .HasForeignKey("LessonPlanId")
                    .OnDelete(DeleteBehavior.Cascade);
                b.HasOne("GeeO.Models.Material")
                    .WithMany()
                    .HasForeignKey("MaterialId")
                    .OnDelete(DeleteBehavior.Cascade);
            });

#pragma warning restore 612, 618
        }
    }
}
