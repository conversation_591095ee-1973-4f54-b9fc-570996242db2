namespace AuthAPI.Config;

using System.Collections.Generic;
using System.Security.Claims;
using GeeO.Data;
using GeeO.Models;
using IdentityServer4.Extensions;
using IdentityServer4.Models;
using IdentityServer4.Services;
using Microsoft.AspNetCore.Identity;

public class ProfileService : IProfileService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly GeeODbContext _context;

    public ProfileService(UserManager<ApplicationUser> userManager, GeeODbContext context)
    {
        _userManager = userManager;
        _context = context;
    }

    public async Task GetProfileDataAsync(ProfileDataRequestContext context)
    {
        var user = await _userManager.FindByIdAsync(context.Subject.GetSubjectId());
        var claims = new List<Claim>
        {
            new Claim("userId", user.Id),
            new Claim("email", user.Email ?? ""),
            new Claim("name", $"{user.FirstName} {user.LastName}")
        };

        var role = await _context.AspNetRoles.FindAsync(user.RoleId);
        claims.Add(new Claim("role", role?.Name.ToLower() ?? string.Empty));

        context.IssuedClaims = claims;
    }

    public async Task IsActiveAsync(IsActiveContext context)
    {
        var user = await _userManager.FindByIdAsync(context.Subject.GetSubjectId());
        context.IsActive = user != null;
    }
}
