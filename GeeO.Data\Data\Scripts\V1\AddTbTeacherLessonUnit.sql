/****** Object:  Table [dbo].[TeacherLessonUnit]    Script Date: 20-Oct-19 2:38:33 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TeacherLessonUnit](
	[Id] [nvarchar](450) NOT NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
	[ClassLessonId] [nvarchar](450) NOT NULL,
	[UnitId] [nvarchar](450) NOT NULL,
	[MaterialId] [nvarchar](450) NULL,
	[Time] int NULL,
	[Note] [nvarchar](1024) NULL,
 CONSTRAINT [PK_TeacherLessonUnit] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TeacherLessonUnit]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnit_ClassLessonId] FOREIGN KEY([ClassLessonId])
REFERENCES [dbo].[ClassLesson] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonUnit] CHECK CONSTRAINT [FK_TeacherLessonUnit_ClassLessonId]
GO

ALTER TABLE [dbo].[TeacherLessonUnit]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnit_MaterialId] FOREIGN KEY([MaterialId])
REFERENCES [dbo].[Material] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonUnit] CHECK CONSTRAINT [FK_TeacherLessonUnit_MaterialId]
GO

ALTER TABLE [dbo].[TeacherLessonUnit]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnit_TeacherId] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonUnit] CHECK CONSTRAINT [FK_TeacherLessonUnit_TeacherId]
GO

ALTER TABLE [dbo].[TeacherLessonUnit]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnit_UnitId] FOREIGN KEY([UnitId])
REFERENCES [dbo].[LessonPlanUnit] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonUnit] CHECK CONSTRAINT [FK_TeacherLessonUnit_UnitId]
GO


