﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

using GeeO.Data;
using GeeO.Models;
using GeeO.GridVo;
using GeeO.Services;
using System.Globalization;
using Microsoft.AspNetCore.Identity;
using GeeO.Data.Dto;
using Microsoft.Extensions.Logging;
using GeeO.Data.Extensions;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class LessonPlansController : GeoOControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;
        private readonly ISSISService _ssisService;
        private readonly ILogger _logger;
        private readonly IClassLessonService _classLessonService;
        private readonly ILessonPlanService _lessonPlanService;

        public LessonPlansController(UserManager<ApplicationUser> userManager,
                                     GeeODbContext context,
                                     IUploadService uploadService,
                                     IFileService fileService,
                                     ISSISService ssisService,
                                     ILogger<LessonPlansController> logger,
                                     IClassLessonService classLessonService,
                                     ILessonPlanService lessonPlanService
                                     )
            : base(userManager)
        {
            _context = context;
            _uploadService = uploadService;
            _fileService = fileService;
            _ssisService = ssisService;
            _logger = logger;
            _classLessonService = classLessonService;
            _lessonPlanService = lessonPlanService;
        }

        [HttpPost("[action]/{importType}")]
        public async Task<IActionResult> UploadImportFile(string importType, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(importType))
            {
                throw new ArgumentNullException(nameof(importType));
            }
            if (files is null)
            {
                throw new ArgumentNullException(nameof(files));
            }

            _fileService.BackupImportFile(importType);
            await _uploadService.UploadLessonImport(importType, files).ConfigureAwait(false);
            _fileService.RenameImportFile(importType, files[0].FileName);
            _ssisService.RunPackage(importType);

            return Ok(new { count = files.Count });
        }

        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> UploadFile(string id, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }
            if (files is null)
            {
                throw new ArgumentNullException(nameof(files));
            }

            await _uploadService.UploadLessonWorksheet(id, files).ConfigureAwait(false);

            return Ok(new { count = files.Count });
        }

        [HttpGet("[action]/{id}/{filename}")]
        public IActionResult GetForView(string id, string filename)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(filename))
            {
                return NotFound();
            }

            return _fileService.ViewDownloadLessonWorksheet(id, filename, false) ?? (IActionResult)NotFound();
        }

        // GET: api/LessonPlans/GetLessonPlanContent/5
        [HttpGet("[action]/{lessonId}")]
        public async Task<ActionResult<IEnumerable<MyLessonUnitVo>>> GetLessonPlanContent(string lessonId)
        {
            var data = _context.LessonPlan
                    .Where(c => c.Id == lessonId && !c.IsDeleted)
                    .Include(c => c.LessonPlanUnits)
                    .AsNoTracking();

            var result = from l in data
                         from u in l.LessonPlanUnits.Where(u => !u.IsDeleted)
                         orderby u.SortOrder
                         select new MyLessonUnitVo
                         {
                             Id = l.Id + u.Id,
                             ClassLessonId = l.Id,
                             UnitId = u.Id,
                             Time = Convert.ToString(u.Time, CultureInfo.CurrentCulture),
                             Procedures = u.Procedures,
                             Description = u.Description,
                             Materials = u.Materials,
                             TeacherActivities = u.TeacherActivities,
                             LearningOutcome = u.LearningOutcome,
                             Note = u.Note,
                             Material = u.Material
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/LessonPlans
        [HttpGet]
        public async Task<ActionResult<IEnumerable<LessonPlan>>> GetLessonPlan()
        {
            var lessonPlans = _context.LessonPlan
                .Include(l => l.Level)
                .Where(l => !l.IsDeleted)
                .OrderBy(l => l.Level.Name).ThenBy(l => l.CreatedDate);
            foreach (var plan in lessonPlans)
            {
                plan.Level.LessonPlans = null;
            }

            List<LessonPlan> plans = await lessonPlans.ToListAsync().ConfigureAwait(false);
            return plans;
        }

        // GET: api/LessonPlans/GetByLevel/5
        [HttpGet("[action]/{levelId}")]
        public async Task<ActionResult<IEnumerable<LessonPlanDto>>> GetByLevel(string levelId)
        {
            var lessonPlans = from lvl in _context.StudyLevel.Where(lvl => lvl.Id == levelId)
                              from lp in _context.LessonPlan.Where(lp => lp.LevelId == lvl.Id && !lp.IsDeleted)
                              orderby lvl.Name, lp.CreatedDate
                              select new LessonPlanDto
                              {
                                  Id = lp.Id,
                                  Level = lvl.Name,
                                  Lesson = lp.Lesson,
                                  Subject = lp.Subject,
                                  Content = lp.Content,
                                  Tb = lp.Tb,
                                  WorksheetFileName = lp.WorksheetFileName,
                                  CreatedDate = lp.CreatedDate
                              };
            return await lessonPlans.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/LessonPlans/5
        [HttpGet("{id}")]
        public async Task<ActionResult<LessonPlan>> GetLessonPlan(string id)
        {
            var lessonPlan = await _context.LessonPlan
                    .Where(l => l.Id == id)
                    .Include(l => l.Level)
                    .FirstAsync().ConfigureAwait(false);

            if (lessonPlan == null)
            {
                return NotFound();
            }

            return lessonPlan;
        }

        // GET: api/LessonPlans/GetLessonPlanOld/
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<List<LessonPlan>>> GetDeletedLessonPlans(string id)
        {
            try
            {
                var lessonPlan = await _context.LessonPlan.FindAsync(id);

                var deletedLessonPlan = await _context.LessonPlan
                    .Include(lp => lp.Level)
                    .Where(lp => lp.LevelId == lessonPlan.LevelId && lp.Lesson == lessonPlan.Lesson && lp.IsDeleted)
                    .OrderByDescending(lp => lp.CreatedDate)
                    .ToListAsync();

                return Ok(deletedLessonPlan);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        // GET: api/LessonPlans/GetLessonContent/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<LessonContent>> GetLessonContent(string id)
        {
            var lessonContent = await _context.LessonContent
                                       .Include(lc => lc.LessonPlan)
                                       .Where(lc => lc.LessonPlanId.Equals(id))
                                       .OrderByDescending(lc => lc.LessonPlan.CreatedDate)
                                       .FirstOrDefaultAsync();

            if (lessonContent == null)
            {
                return NoContent();
            }

            return lessonContent;
        }

        // POST: api/LessonPlans/SaveLessonContent
        [HttpPost("[action]")]
        public async Task<ActionResult<LessonContent>> SaveLessonContent(LessonContent lessonContent)
        {
            if (lessonContent.Id == null)
            {
                _context.LessonContent.Add(new LessonContent
                {
                    LessonPlanId = lessonContent.LessonPlanId,
                    Textbook = lessonContent.Textbook,
                    Page = lessonContent.Page,
                    CoreWords = lessonContent.CoreWords,
                    KeyWords = lessonContent.KeyWords,
                    KeyQuestions = lessonContent.KeyQuestions,
                    KeyAnswers = lessonContent.KeyAnswers,
                    Activities = lessonContent.Activities,
                    Media = lessonContent.Media,
                });
            }
            else
            {
                _context.Entry(lessonContent).State = EntityState.Modified;
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("SaveLessonContent", new { id = lessonContent.Id }, lessonContent);
        }

        // PUT: api/LessonPlans/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutLessonPlan(string id, LessonPlan lessonPlan)
        {
            if (id != lessonPlan.Id)
            {
                return BadRequest();
            }

            _context.Entry(lessonPlan).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LessonPlanExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/LessonPlans
        [HttpPost]
        public async Task<ActionResult<LessonPlan>> PostLessonPlan(LessonPlan lessonPlan)
        {
            try
            {
                _context.LessonPlan.Add(lessonPlan);
                lessonPlan.CreatedDate = DateTime.Now;
                await _context.SaveChangesAsync().ConfigureAwait(false);

                await _classLessonService.SyncForNewLessonPlan(lessonPlan.Id, lessonPlan.Lesson);

                return CreatedAtAction("GetLessonPlan", new { id = lessonPlan.Id }, lessonPlan);
            }
            catch (Exception e)
            {
                _logger.LogError($"Create LessonPlan has error: {e.Message} - {e.StackTrace}");
                return BadRequest("Error occurred while creating Lesson Plan.");
            }
        }

        // DELETE: api/LessonPlans/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<LessonPlan>> DeleteLessonPlan(string id)
        {
            try
            {
                var lessonPlan = await _context.LessonPlan.FindAsync(id);
                lessonPlan.IsDeleted = true;
                _context.Entry(lessonPlan).SetActivityState(ActivityActionType.Archived);
                await _context.SaveChangesAsync().ConfigureAwait(false);

                return lessonPlan;
            }
            catch (Exception ex)
            {
                _logger.LogError($"DeleteLessonPlan - {ex.Message}");
                return BadRequest("An error occurred during the process.");
            }
        }

        [HttpPost("ids")]
        public async Task<ActionResult<List<string>>> DeleteLessonPlans(List<string> lpIds)
        {
            try
            {
                var lessonPlans = await _context.LessonPlan.Where(x => lpIds.Contains(x.Id)).ToListAsync();
                lessonPlans.ForEach(x =>
                {
                    x.IsDeleted = true;
                    _context.Entry(x).State = EntityState.Modified;
                });
                await _context.SaveChangesAsync().ConfigureAwait(false);

                return lpIds;
            }
            catch (Exception ex)
            {
                _logger.LogError($"DeleteLessonPlans - {ex.Message}");
                return BadRequest("An error occurred during the process.");
            }
        }

        // DELETE: api/LessonPlans/DeleteWorksheet/5
        [HttpDelete("[action]/{id}")]
        public async Task<ActionResult<LessonPlan>> DeleteWorksheet(string id)
        {
            var lessonPlan = await _context.LessonPlan.FindAsync(id);
            if (lessonPlan == null)
            {
                return NotFound();
            }

            _uploadService.DeleteLessonWorksheet(id, lessonPlan.WorksheetFileName);
            lessonPlan.WorksheetFileName = null;
            _context.Entry(lessonPlan).State = EntityState.Modified;
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return lessonPlan;
        }

        private bool LessonPlanExists(string id)
        {
            return _context.LessonPlan.Any(e => e.Id == id);
        }

        // GET: api/LessonPlans/5
        [HttpGet("class-lesson/{id}")]
        public async Task<ActionResult<LessonPlan>> GetLessonPlanByClassLessonId(string id)
        {
            var lessonPlan = await _context.LessonPlan
                    .Include(l => l.Level)
                    .Where(x => x.ClassLessons.Select(o => o.Id).Contains(id))
                    .FirstAsync().ConfigureAwait(false);

            if (lessonPlan == null)
            {
                return NotFound();
            }

            return lessonPlan;
        }

        [HttpGet("{levelId}/{lesson}/{id?}")]
        public async Task<ActionResult<bool>> ExistsOrExceedsByLesson(string levelId, string lesson, string id)
        {
            var existingLessonPlan = await _context.LessonPlan
                    .FirstOrDefaultAsync(x => x.Id.Equals(id) && !x.IsDeleted);

            if (existingLessonPlan != null && existingLessonPlan.Lesson.Equals(lesson))
            {
                return false;
            }

            var result = from cl in _context.ClassLesson.Where(cl => cl.LessonId == id)
                         from tll in _context.TeacherLessonLog.Where(tll => tll.ClassLessonId == cl.Id)
                         select tll;
            var lessonLog = await result.FirstOrDefaultAsync();

            var lessonPlan = await _context.LessonPlan
                .FirstOrDefaultAsync(x => x.LevelId.Equals(levelId) && x.Lesson.Equals(lesson) && !x.IsDeleted);

            var level = await _context.StudyLevel.FindAsync(levelId);

            return lessonPlan != null || Convert.ToInt32(lesson) > level.NumberOfLesson || lessonLog != null;
        }

        [HttpPost("[action]/{levelId}")]
        public async Task<IActionResult> CopySortLessonPlans(string levelId, List<LessonPlan> lstSortLessonPlan)
        {
            try
            {
                var result = await _lessonPlanService.CopySortLessonPlans(levelId, lstSortLessonPlan);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, Error = ex.Message });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> RemoveContent(List<string> lessonIds)
        {
            try
            {
                var result = await _lessonPlanService.RemoveContent(lessonIds);
                return Ok(new { Status = StatusCodes.Status200OK, Data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, Error = ex.Message });
            }
        }
    }
}
