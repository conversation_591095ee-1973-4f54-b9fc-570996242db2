using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto.ActivityLog;
using GeeO.Models;
using GeeO.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Services.Services
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly GeeODbContext _context;
        public ActivityLogService(GeeODbContext context)
        {
            _context = context;
        }

        public async Task<List<string>> GetActions()
        {
            try
            {
                var actions = Enum.GetValues(typeof(ActivityActionType))
                      .Cast<ActivityActionType>()
                      .Select(a => new { Name = a.ToString(), Value = a.GetEnumMemberValue() })
                      .ToList();
                return await Task.FromResult(actions.Select(a => a.Value).ToList());
            }
            catch (Exception ex)
            {
                var errorMessage = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                throw new Exception($"An error occurred in GetActions: {errorMessage}", ex);
            }
        }

        public async Task<PagedResult<ActivityLog>> FilterAll(ActivityLogDto activityLogDto)
        {
            try
            {
                var query = _context.ActivityLogs.AsQueryable();

                if (activityLogDto.TableName != null && activityLogDto.TableName.Count() > 0)
                {
                    query = query.Where(x => activityLogDto.TableName.Contains(x.TableName));
                }
                if (activityLogDto.Action != null && activityLogDto.Action.Count > 0)
                {
                    query = query.Where(x => activityLogDto.Action.Contains(x.Action));
                }
                if (activityLogDto.UserName != null && activityLogDto.UserName.Count > 0)
                {
                    var normalizedUserNames = activityLogDto.UserName
                        .Select(name => name.Trim())
                        .ToList();

                    var userIds = await _context.AspNetUsers
                        .Where(u =>
                            normalizedUserNames.Contains(u.UserInfo.EnglishName.Trim()) ||
                            normalizedUserNames.Contains((u.UserInfo.EnglishName + " - " + u.FirstName + " " + u.LastName).Trim()) ||
                            normalizedUserNames.Contains(u.FirstName.Trim()) ||
                            normalizedUserNames.Contains(u.LastName.Trim()))
                        .Select(u => u.Id)
                        .ToListAsync();

                    query = query.Where(x => userIds.Contains(x.UserId));
                }

                if (activityLogDto.StartDate != null && activityLogDto.EndDate != null)
                {
                    query = query.Where(x => x.CreatedAt >= activityLogDto.StartDate && x.CreatedAt <= activityLogDto.EndDate);
                }
                if (!string.IsNullOrEmpty(activityLogDto.Changes))
                {
                    string changes = activityLogDto.Changes;
                    query = query.Where(x => EF.Functions.Like(x.Changes, $"%\"Field\":\"{changes}\"%")
                                  || EF.Functions.Like(x.Changes, $"%\"OldValue\":\"{changes}\"%")
                                  || EF.Functions.Like(x.Changes, $"%\"NewValue\":\"{changes}\"%"));
                }

                if (!string.IsNullOrEmpty(activityLogDto.SortBy))
                {
                    bool isDescending = activityLogDto.SortOrder?.ToLower() == "desc";
                    query = isDescending
                        ? query.OrderByDescending(x => EF.Property<object>(x, activityLogDto.SortBy))
                        : query.OrderBy(x => EF.Property<object>(x, activityLogDto.SortBy));
                }

                int totalRecords = await query.CountAsync();
                int pageNumber = activityLogDto.PageNumber;
                int pageSize = activityLogDto.PageSize;

                var pagedActivityLogs = await query
                    .Include(x => x.User)
                        .ThenInclude(x => x.UserInfo)
                    .OrderByDescending(log => log.CreatedAt)
                    .Skip(pageNumber * pageSize)
                    .Take(pageSize)
                    .Select(x => new ActivityLog
                    {
                        Id = x.Id,
                        Title = x.Title,
                        UserName = x.User != null ? x.User.UserInfo.EnglishName + " - " + x.User.FirstName + " " + x.User.LastName : "Unknown",
                        TableName = x.TableName,
                        Action = x.Action,
                        Changes = x.Changes,
                        CreatedAt = x.CreatedAt
                    })
                    .ToListAsync();

                return new PagedResult<ActivityLog>
                {
                    Items = pagedActivityLogs,
                    TotalRecords = totalRecords,
                    CurrentPage = pageNumber,
                    PageSize = pageSize,
                };
            }
            catch (Exception ex)
            {
                var errorMessage = ex.InnerException?.Message ?? ex.Message;
                throw new Exception(errorMessage);
            }
        }
    }
}
