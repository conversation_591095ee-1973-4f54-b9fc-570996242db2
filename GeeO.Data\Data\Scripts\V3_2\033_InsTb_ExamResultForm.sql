﻿USE [GeeODb]
GO

-- insert exam result forms
-- Level M
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'db181096-5297-4344-95e4-fa05aad7e9db'
           ,1 -- 3 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "<PERSON><PERSON><PERSON> ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "<PERSON>àm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 4},
								{"name": "Vocabulary (V)", "score": 0, "weight": 6},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 5}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'db181096-5297-4344-95e4-fa05aad7e9db'
           ,2 -- 6 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 4},
								{"name": "Vocabulary (V)", "score": 0, "weight": 9},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 4}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'db181096-5297-4344-95e4-fa05aad7e9db'
           ,3 -- 9 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 2},
								{"name": "Vocabulary (V)", "score": 0, "weight": 9},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 4}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'db181096-5297-4344-95e4-fa05aad7e9db'
           ,4 -- 12 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 5},
								{"name": "Vocabulary (V)", "score": 0, "weight": 13},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 4}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')

-- Level V
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'1574100e-f465-4280-8f95-226060505c7f'
           ,1 -- 3 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 4},
								{"name": "Vocabulary (V)", "score": 0, "weight": 21},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 4},
								{"name": "Sight word (S)", "score": 0, "weight": 3}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'1574100e-f465-4280-8f95-226060505c7f'
           ,2 -- 6 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 10},
								{"name": "Vocabulary (V)", "score": 0, "weight": 20},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 2},
								{"name": "Sight word (S)", "score": 0, "weight": 12}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'1574100e-f465-4280-8f95-226060505c7f'
           ,3 -- 9 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 4},
								{"name": "Vocabulary (V)", "score": 0, "weight": 34},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 3},
								{"name": "Sight word (S)", "score": 0, "weight": 5}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'1574100e-f465-4280-8f95-226060505c7f'
           ,4 -- 12 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Math (M)", "score": 0, "weight": 10},
								{"name": "Vocabulary (V)", "score": 0, "weight": 48},
								{"name": "Phonics Foundation (P)", "score": 0, "weight": 3},
								{"name": "Sight word (S)", "score": 0, "weight": 3}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')

-- Level E
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'d30af7ce-3693-4feb-a0a6-577e67e073db'
           ,1 -- 3 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Structures", "score": 0, "weight": 15},
								{"name": "Math", "score": 0, "weight": 4},
								{"name": "Science", "score": 0, "weight": 5},
								{"name": "Phonics, Vocabulary and Sight word", "score": 0, "weight": 19}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'d30af7ce-3693-4feb-a0a6-577e67e073db'
           ,2 -- 6 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Structures", "score": 0, "weight": 7},
								{"name": "Math", "score": 0, "weight": 3},
								{"name": "Science", "score": 0, "weight": 15},
								{"name": "Phonics, Vocabulary and Sight word", "score": 0, "weight": 25}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'d30af7ce-3693-4feb-a0a6-577e67e073db'
           ,3 -- 9 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Structures", "score": 0, "weight": 24},
								{"name": "Math", "score": 0, "weight": 3},
								{"name": "Science", "score": 0, "weight": 15},
								{"name": "Phonics, Vocabulary and Sight word", "score": 0, "weight": 17}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')
INSERT INTO [dbo].[ExamResultForm]
           ([Id]
           ,[LevelId]
           ,[ExamType]
           ,[ExamFormJson]
           ,[CreatedDate]
           ,[CreatedBy])
     VALUES
           (LOWER(CONVERT(nvarchar(450), NEWID()))
           ,'d30af7ce-3693-4feb-a0a6-577e67e073db'
           ,4 -- 12 months
           ,N'{ "overall": [
								{"name": "Confidence", "altName": "Sự tự tin", "evaluation": 0},
								{"name": "Behavior", "altName": "Cách ứng xử", "evaluation": 0},
								{"name": "Concentration", "altName": "Sự tập trung", "evaluation": 0},
								{"name": "Progression", "altName": "Sự tiến bộ", "evaluation": 0},
								{"name": "Teamwork", "altName": "Làm việc nhóm", "evaluation": 0}
						   ],
				"academic": [
								{"name": "Structures", "score": 0, "weight": 4},
								{"name": "Math", "score": 0, "weight": 11},
								{"name": "Science", "score": 0, "weight": 14},
								{"name": "Phonics, Vocabulary and Sight word", "score": 0, "weight": 18}
						   ] }'
		   ,GETDATE()
		   ,'sysadmin')

GO

