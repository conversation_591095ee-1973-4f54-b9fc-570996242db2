﻿ALTER TABLE CatchUpSchedules ADD ClassLessonId nvarchar(450);
GO

ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_ClassLesson] FOREIGN KEY([ClassLessonId])
REFERENCES [dbo].[ClassLesson] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_ClassLesson]
GO

IF COL_LENGTH('CatchUpSchedules','Type') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN [Type];
END;
IF COL_LENGTH('CatchUpSchedules','ClassCourseId') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN ClassCourseId;
END;
IF COL_LENGTH('CatchUpSchedules','StartTime') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN StartTime;
END;
IF COL_LENGTH('CatchUpSchedules','EndTime') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN EndTime;
END;
IF COL_LENGTH('CatchUpSchedules','ClassCourseCurrentId') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN ClassCourseCurrentId;
END;
IF COL_LENGTH('CatchUpSchedules','StartDate') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN StartDate;
END;
IF COL_LENGTH('CatchUpSchedules','EndDate') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[CatchUpSchedules] DROP COLUMN EndDate;
END;
GO
ALTER TABLE CatchUpSchedules ADD CatchUpType int;
ALTER TABLE CatchUpSchedules ADD CatchUpTime int;
ALTER TABLE CatchUpSchedules ADD CatchUpStatus bit not null;
ALTER TABLE CatchUpSchedules ADD StudentId nvarchar(450);
GO
DROP TABLE IF EXISTS StudentCatchUp
GO
ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_Student]
GO