USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplateCampus]    Script Date: 3/26/2025 5:24:50 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplateCampus](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[CampusId] [nvarchar](450) NULL,
 CONSTRAINT [PK__FormTemp__3214EC07B2877464] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplateCampus]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateCampus_Campus] FOREIGN KEY([CampusId])
REFERENCES [dbo].[Campus] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateCampus] CHECK CONSTRAINT [FK_FormTemplateCampus_Campus]
GO

ALTER TABLE [dbo].[FormTemplateCampus]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateCampus_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateCampus] CHECK CONSTRAINT [FK_FormTemplateCampus_FormTemplate]
GO