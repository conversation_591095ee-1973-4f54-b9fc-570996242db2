﻿/* Gee-<PERSON> styles */

:root {
    --font-family-sans-serif: 'RalewayRegular', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

html {
    font-family: "RalewayRegular", "Helvetica", "Arial", sans-serif;
}

body {
    font-family: "RalewayRegular", "Helvetica", "Arial", sans-serif;
    color: rgba(0, 0, 0, 0.9);
}

@media (min-width: 1500px) {
    .container {
        max-width: 1400px;
    }
}

/* Header navbar styles
-------------------------------------------------- */
a.geeo-brand {
    font-family: "RalewayBold", "Helvetica", "Arial", sans-serif;
}

/* Common elements styles
-------------------------------------------------- */
input {
    font-family: "RalewayRegular", "Helvetica", "Arial", sans-serif;
}

.geeo-text-primary {
    color: rgba(0, 0, 0, 0.9);
}

.geeo-text-secondary {
    color: rgba(0, 0, 0, 0.6);
}

.btn-primary {
    background-color: #b22c2c;
    border-color: #b22c2c;
}

.btn-primary:hover {
    background-color: rgb(124, 30, 30);
    border-color: rgb(124, 30, 30);
}

.btn-secondary {
    background-color: #50d282;
    border-color: #50d282;
}

.btn-secondary:hover {
    background-color: rgb(56, 147, 91);
    border-color: rgb(56, 147, 91);
}
