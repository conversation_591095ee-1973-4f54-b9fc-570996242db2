using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace GeeO
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    string geeOJson = hostingContext.HostingEnvironment.IsProduction() ? "/GeeO/GeeO.json" : "./GeeO.json";
                    config.AddJsonFile(geeOJson, optional: true, reloadOnChange: false);

                    //var keyVaultEndpoint = new Uri(Environment.GetEnvironmentVariable("VaultUri"));
                    //config.AddAzureKeyVault(keyVaultEndpoint, new DefaultAzureCredential());
                })
                .ConfigureLogging(logging =>
                {
                    logging.AddLog4Net("log4net.config");
                })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    //string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                    //Console.WriteLine("Assembly location: {0}", assemblyLocation);
                    webBuilder.UseSentry();
                    webBuilder
                        .ConfigureKestrel(serverOptions =>
                        {
                            serverOptions.ListenAnyIP(5001, opts => opts.UseHttps());
                            //serverOptions.Limits.MinResponseDataRate = null;
                            //serverOptions.ConfigureEndpointDefaults(lo => lo.Protocols = Microsoft.AspNetCore.Server.Kestrel.Core.HttpProtocols.Http1);
                        })
                        //.UseContentRoot(Path.GetDirectoryName(assemblyLocation))
                        .UseStartup<Startup>();
                    //webBuilder.UseKestrel(opts =>
                    //{
                    //    // Bind directly to a socket handle or Unix socket
                    //    // opts.ListenHandle(123554);
                    //    // opts.ListenUnixSocket("/tmp/kestrel-test.sock");
                    //    //opts.Listen(IPAddress.Loopback, port: 5002);
                    //    opts.ListenAnyIP(5001, opts => opts.UseHttps());
                    //    //opts.ListenLocalhost(5004, opts => opts.UseHttps());
                    //    //opts.ListenLocalhost(5005, opts => opts.UseHttps());
                    //});
                });
    }
}
