﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using GeeO.Common;
using System.Text.Json;
using System.Security.Claims;
using GeeO.Services;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.AspNetCore.Authorization;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class CampusController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;

        public CampusController(GeeODbContext context, IAcadManageService acadManageService)
        {
            _context = context;
            _acadManageService = acadManageService;
        }

        // GET: api/Campus
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<Campus>>> GetCampusList(string userId)
        {
            //var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            return await _context.Campus.Where(x => assignedCampus == null || assignedCampus.Contains(x.Id)).ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("assigned")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<Campus>>> GetAssignedCampus()
        {
            string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
            var assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            return await _context.Campus.Where(x => assignedCampus == null || assignedCampus.Contains(x.Id)).ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Campus>>> GetCampusByClass(string classId)
        {
            try
            {
                var campuses = await (from cc in _context.ClassCourse.Where(cc => cc.Id == classId)
                                      join c in _context.Campus on cc.CampusId equals c.Id
                                      select c).Distinct().ToListAsync();
                return Ok(campuses);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        // GET: api/Campus/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Campus>> GetCampus(string id)
        {
            var campus = await _context.Campus.FindAsync(id).ConfigureAwait(false);

            if (campus == null)
            {
                return NotFound();
            }

            return campus;
        }

        // PUT: api/Campus/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPut("{id}")]
        public async Task<IActionResult> PutCampus(string id, Campus campus)
        {
            if (id != campus.Id)
            {
                return BadRequest();
            }

            _context.Entry(campus).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CampusExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Campus
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPost]
        public async Task<ActionResult<Campus>> PostCampus(Campus campus)
        {
            _context.Campus.Add(campus);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetCampus", new { id = campus.Id }, campus);
        }

        // DELETE: api/Campus/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<Campus>> DeleteCampus(string id)
        {
            var campus = await _context.Campus.FindAsync(id);
            if (campus == null)
            {
                return NotFound();
            }

            _context.Campus.Remove(campus);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return campus;
        }

        [HttpGet("{domain}/{ipAddress}")]
        public async Task<IActionResult> UpdateIpByDomain(string domain, string ipAddress)
        {
            try
            {
                if (string.IsNullOrEmpty(domain))
                {
                    return BadRequest("Domain name cannot be null.");
                }

                var campus = await _context.Campus.FirstOrDefaultAsync(x => x.Website.Equals(domain));
                if (campus == null)
                {
                    return BadRequest("Campus not found.");
                }

                campus.IpAddress = ipAddress;
                _context.Entry(campus).State = EntityState.Modified;
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException ex)
            {
                return BadRequest(ex.Message);
            }

            return Ok(true);
        }

        [HttpGet]
        public async Task<IActionResult> GetListCampus()
        {
            try
            {
                var campuses = await _context.Campus.ToListAsync();
                return Ok(campuses);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }


        private bool CampusExists(string id)
        {
            return _context.Campus.Any(e => e.Id == id);
        }
    }
}
