import React, { Component } from 'react';
import authService from './AuthorizeService';

// The main responsibility of this component is to handle the user's logout process.
// This is the starting point for the logout process, which is usually initiated when a
// user clicks on the logout button on the LoginMenu component.
export class Logout extends Component {
  constructor(...args) {
    super(...args);

    this.state = {
      message: undefined,
      isReady: false,
      authenticated: false
    };
  }

  componentDidMount() {
    this.logout();
    this.navigateToReturnUrl('/');
  }

  render() {
    const { isReady, message } = this.state;
    return <div>{message}</div>;
  }

  async logout() {
    // const state = { returnUrl };
    const isauthenticated = await authService.isAuthenticated();
    if (isauthenticated) {
      await authService.signOut();
      this.setState({ message: 'You successfully logged out!' });
      // this.navigateToReturnUrl('/');
      // if (result.status) {
      // await this.navigateToReturnUrl(returnUrl);
      // }
      // switch (result.status) {
      //   case AuthenticationResultStatus.Redirect:
      //     break;
      //   case AuthenticationResultStatus.Success:
      //     await this.navigateToReturnUrl(returnUrl);
      //     break;
      //   case AuthenticationResultStatus.Fail:
      //     this.setState({ message: result.message });
      //     break;
      //   default:
      //     throw new Error('Invalid authentication result status.');
      // }
    }
  }

  // async processLogoutCallback() {
  //   const url = window.location.href;
  //   const result = await authService.completeSignOut(url);
  //   switch (result.status) {
  //     case AuthenticationResultStatus.Redirect:
  //       // There should not be any redirects as the only time completeAuthentication finishes
  //       // is when we are doing a redirect sign in flow.
  //       throw new Error('Should not redirect.');
  //     case AuthenticationResultStatus.Success:
  //       await this.navigateToReturnUrl(this.getReturnUrl(result.state));
  //       break;
  //     case AuthenticationResultStatus.Fail:
  //       this.setState({ message: result.message });
  //       break;
  //     default:
  //       throw new Error('Invalid authentication result status.');
  //   }
  // }

  // async populateAuthenticationState() {
  //   const authenticated = await authService.isAuthenticated();
  //   this.setState({ isReady: true, authenticated });
  // }

  // getReturnUrl(state) {
  //   const params = new URLSearchParams(window.location.search);
  //   const fromQuery = params.get(QueryParameterNames.ReturnUrl);
  //   if (fromQuery && !fromQuery.startsWith(`${window.location.origin}/`)) {
  //     // This is an extra check to prevent open redirects.
  //     throw new Error(
  //       'Invalid return url. The return url needs to have the same origin as the current page.'
  //     );
  //   }
  //   return (
  //     (state && state.returnUrl) ||
  //     fromQuery ||
  //     `${window.location.origin}${ApplicationPaths.LoggedOut}`
  //   );
  // }

  navigateToReturnUrl(returnUrl) {
    return window.location.replace(returnUrl);
  }
}
