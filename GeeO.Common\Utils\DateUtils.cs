﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GeeO.Common
{
    public static class DateUtils
    {
        public static DateTime GetMondayThisWeek()
        {
            var now = DateTime.Today;
            int days = WeekDayOffsetToMonday(now.DayOfWeek);
            DateTime monday = now.AddDays(-days);
            return monday;
        }

        public static DateTime GetSundayThisWeek()
        {
            var now = DateTime.Today;
            int days = WeekDayOffsetToMonday(now.DayOfWeek);
            DateTime sunday = now.AddDays(6-days);
            return sunday;
        }

        public static DateTime GetMondayOfWeek(DateTime date)
        {
            int days = WeekDayOffsetToMonday(date.DayOfWeek);
            DateTime monday = date.AddDays(-days);
            return monday;
        }

        public static DateTime GetSundayOfWeek(DateTime date)
        {
            int days = WeekDayOffsetToMonday(date.DayOfWeek);
            DateTime sunday = date.AddDays(6-days);
            return sunday;
        }

        public static int WeekDayOffsetToMonday(DayOfWeek currentDay)
        {
            return currentDay == DayOfWeek.Sunday ? 6 : (int)currentDay - 1;
        }
    }
}
