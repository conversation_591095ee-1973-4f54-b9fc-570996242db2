﻿IF EXISTS (SELECT 1 FROM sys.objects WHERE [name] = N'trg_StudentAddedToClass' AND [type] = 'TR')
BEGIN
    DROP TRIGGER [dbo].[trg_StudentAddedToClass];
END
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER dbo.trg_StudentAddedToClass
   ON  dbo.ClassStudent 
   AFTER INSERT
AS 
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @studentName nvarchar(500),
			@studentType nvarchar(100) = '',
			@classId   nvarchar(450),
			@className   nvarchar(200),
			@eventTitle  nvarchar(1000);

	DECLARE @TimeToCheck datetime = SYSDATETIMEOFFSET() AT TIME ZONE 'SE Asia Standard Time';

	SELECT @studentName = IIF(NULLIF(std.EnglishName, '') IS NULL, '', std.EnglishName + ', ') + std.StudentName, 
			@classId = ct.ClassId,
			@className = cls.[Name], 
			@studentType = case 
								when ct.[ClassType] = 1 then 'regular'
								when ct.[ClassType] = 3 then 'demo'
								else ''
							end
	FROM Student std WITH(NOLOCK)
	JOIN INSERTED ct WITH(NOLOCK) ON std.Id = ct.StudentId
	JOIN ClassCourse cls WITH(NOLOCK) ON ct.ClassId = cls.Id

	SET @eventTitle = N'Học sinh ' + @studentName + N' (' + @studentType + N') vừa được thêm vào lớp ' + @className;

	IF @studentType <> '' 
		INSERT INTO [dbo].[Notifications]
				   ([Id]
				   ,[ClassId]
				   ,[Title]
				   ,[Content]
				   ,[StartTime]
				   ,[EndTime]
				   ,[CreatedDate]
				   ,[CreatedBy])
			 VALUES
				   (LOWER(CONVERT(nvarchar(450), NEWID()))
				   ,@classId
				   ,@eventTitle
				   ,@eventTitle
				   ,@TimeToCheck
				   ,DATEADD(mi, 5, @TimeToCheck)
				   ,@TimeToCheck
				   ,'sysadmin');
END
GO
