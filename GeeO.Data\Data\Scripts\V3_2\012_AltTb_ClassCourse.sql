USE [GeeODb]
GO

/****** Object:  Table [dbo].[ClassCourse]    Script Date: 10-Jun-19 10:43:16 AM ******/

ALTER TABLE [dbo].[ClassCourse] DROP COLUMN [Course]
ALTER TABLE [dbo].[ClassCourse] DROP COLUMN [LessonScheduleId]
GO

ALTER TABLE [dbo].[ClassCourse] ADD CampusId [nvarchar](450) NULL
GO

ALTER TABLE [dbo].[ClassCourse]  WITH CHECK ADD  CONSTRAINT [FK_ClassCourse_CampusId] FOREIGN KEY([CampusId])
REFERENCES [dbo].[Campus] ([Id])
GO
ALTER TABLE [dbo].[ClassCourse] CHECK CONSTRAINT [FK_ClassCourse_CampusId]
GO

