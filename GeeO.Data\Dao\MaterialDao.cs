﻿using GeeO.Data;
using GeeO.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Dao
{
    public class MaterialDao
    {
        protected readonly GeeODbContext _context;

        public MaterialDao(GeeODbContext context)
        {
            _context = context;
        }

        public async Task<Material> GetMaterial(string id)
        {
            var material = await _context.Material.FindAsync(id);

            return material;
        }

        public async Task<int> UpdateMaterial(Material material)
        {
            _context.Entry(material).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MaterialExists(material.Id))
                {
                    return 0;
                }
                else
                {
                    throw;
                }
            }

            return 0;
        }

        private bool MaterialExists(string id)
        {
            return _context.Material.Any(e => e.Id == id);
        }
    }
}
