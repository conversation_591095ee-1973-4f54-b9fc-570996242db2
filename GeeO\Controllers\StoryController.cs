﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Identity;
using GeeO.Data.Dto;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class StoryController : GeoOControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;
        private readonly INewsFeedService _newsFeedService;

        public StoryController(UserManager<ApplicationUser> userManager,
                               GeeODbContext context,
                               IUploadService uploadService,
                               IFileService fileService,
                               INewsFeedService newsFeedService)
                    : base(userManager)
        {
            _context = context;
            _uploadService = uploadService;
            _fileService = fileService;
            _newsFeedService = newsFeedService;
        }

        // POST: api/Story
        [HttpPost("[action]/{storyId}/{userId}")]
        public async Task<ActionResult> Like(string storyId, string userId)
        {
            var story = await _context.GeeOStory.FindAsync(storyId);
            story.CreatedDate = DateTime.Now;
            _context.Entry(story).State = EntityState.Modified;
            await _context.SaveChangesAsync().ConfigureAwait(false);

            await _context.SaveChangesAsync().ConfigureAwait(false);
            
            return Ok();
        }

        // GET: api/Story
        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Story>>> GetStories(string classId)
        {
            var newsFeed = await _newsFeedService.GetStories(classId);
            return Ok(newsFeed);
        }

        // GET: api/Story/5
        [HttpGet("{id}")]
        public async Task<ActionResult<GeeOStory>> GetStory(string id)
        {
            var story = await _context.GeeOStory.FindAsync(id);

            if (story == null)
            {
                return NotFound();
            }

            return story;
        }

        // GET: api/Story/GetComment/5
        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<GeeOStoryComment>> GetComment(string id)
        {
            var story = await _context.GeeOStoryComment.FindAsync(id);

            if (story == null)
            {
                return NotFound();
            }

            return story;
        }

        // PUT: api/Story/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutStory(string id, GeeOStory story)
        {
            if (id != story.Id)
            {
                return BadRequest();
            }

            _context.Entry(story).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StoryExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Story
        [HttpPost]
        public async Task<ActionResult<GeeOStory>> PostStory(GeeOStory story)
        {
            story.CreatedDate = DateTime.Now;
            _context.GeeOStory.Add(story);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetStory", new { id = story.Id }, story);
        }

        // POST: api/Story/PostComment
        [HttpPost("[action]")]
        public async Task<ActionResult<GeeOStory>> PostComment(GeeOStoryComment comment)
        {
            comment.CreatedDate = DateTime.Now;
            _context.GeeOStoryComment.Add(comment);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetComment", new { id = comment.Id }, comment);
        }

        // DELETE: api/Story/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<GeeOStory>> DeleteStory(string id)
        {
            var story = await _context.GeeOStory.FindAsync(id);
            if (story == null)
            {
                return NotFound();
            }

            _context.GeeOStory.Remove(story);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return story;
        }

        private bool StoryExists(string id)
        {
            return _context.GeeOStory.Any(e => e.Id == id);
        }

        [HttpPost("[action]/{storyId}")]
        public async Task<IActionResult> UploadFile(string storyId, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(storyId))
            {
                return NotFound();
            }

            await _uploadService.UploadStoryPhoto(storyId, files).ConfigureAwait(false);

            return Ok(new { count = files.Count });
        }

        [HttpGet("[action]/{id}/{filename}")]
        public IActionResult GetForView(string id, string filename)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(filename))
            {
                return NotFound();
            }

            return _fileService.ViewDownloadStoryPhoto(id, filename, false) ?? (IActionResult)NotFound();
        }
    }
}
