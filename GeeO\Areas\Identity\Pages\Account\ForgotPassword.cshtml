﻿@page
@model ForgotPasswordModel
@{
    ViewData["Title"] = "Forgot your password?";
}

<div class="row account--login-pnl">
    <div class="col-md-7 account--login-left">
        <img src="~/images/geeo-bgn.png" />
    </div>
    <div class="col-md-5 account--login-right">
        <section>
            <form id="forgot-pwd" method="post">
                <img src="~/images/logo.svg" class="gee-o-logo" />
                <div class="login-title">
                    <h4>@ViewData["Title"]</h4>
                    <p class="body1 geeo-text-secondary">Enter your email and we send you a password reset link.</p>
                </div>
                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group">
                    <label asp-for="Input.Email" class="ctrl-label">Email address</label>
                    <input asp-for="Input.Email" class="form-control" />
                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-secondary btn-login">Send Request</button>
                </div>
            </form>
        </section>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
