﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;
using GeeO.Common;

namespace GeeO.Data.Dto
{
    public class Campus
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public int NumberOfRooms { get; set; }
        public int NumberOfStaffs { get; set; }
        public string Website { get; set; }
        public string Location { get; set; }
        public string Image { get; set; }
        public string IpAddress { get; set; }
    }
}
