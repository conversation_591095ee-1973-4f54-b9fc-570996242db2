import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import CollapseContent from '../ui/table/CollapseContent';

const stylesInfo = theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
    margin: theme.spacing(1, 0, 2, 0),
    overflowX: 'auto',
    minWidth: '700px'
  },
  cell: {
    padding: theme.spacing(1, 0, 1, 2)
  }
});

class TeacherInfoComp extends Component {
  static displayName = TeacherInfoComp.name;

  constructor(...args) {
    super(...args);
    this.state = {
      data: this.props.data
    };
  }

  render() {
    const { classes } = this.props;
    const { data } = this.state;
    const cols = [
      { name: 'fullName', header: 'Name', align: 'center', xs: 4 },
      { name: 'phoneN<PERSON>ber', header: 'Phone', align: 'left', xs: 2 },
      { name: 'email', header: 'Email', align: 'left', xs: 4 }
    ];

    return (
      <Paper className={classes.root}>
        <Grid container>
          {cols.map((col, idx) => (
            <Grid item xs={col.xs} className={classes.cell} key={idx}>
              <Typography color="textSecondary" variant="caption">
                {col.header}
              </Typography>
              <Typography color="textPrimary" variant="body2">
                {col.isObj ? (
                  <CollapseContent content={data[col.name].name} index={40} />
                ) : (
                  <CollapseContent content={data[col.name]} index={40} />
                )}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Paper>
    );
  }
}

TeacherInfoComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const TeacherInfo = withStyles(stylesInfo)(TeacherInfoComp);
