﻿ALTER TABLE dbo.StudentParent DROP CONSTRAINT FK_StudentParent_Student;

ALTER TABLE dbo.Student DROP CONSTRAINT FK_ClassData_ClassId;
ALTER TABLE dbo.Student DROP COLUMN ClassId;

--ALTER TABLE dbo.ClassStudent DROP CONSTRAINT FK_ClassStudent_Student;

ALTER TABLE [dbo].[StudentParent]  WITH CHECK ADD  CONSTRAINT [FK_StudentParent_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id]);
GO

ALTER TABLE [dbo].[StudentParent] CHECK CONSTRAINT [FK_StudentParent_Student];
GO

ALTER TABLE [dbo].[ClassStudent]  WITH CHECK ADD  CONSTRAINT [FK_ClassStudent_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[ClassStudent] CHECK CONSTRAINT [FK_ClassStudent_Student]
GO

ALTER TABLE [dbo].[ClassCourse] ADD LessonScheduleId nvarchar(450);

