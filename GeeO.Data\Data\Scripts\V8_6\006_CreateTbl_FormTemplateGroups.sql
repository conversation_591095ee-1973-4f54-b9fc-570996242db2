USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplateGroups]    Script Date: 3/26/2025 6:07:19 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplateGroups](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[GroupId] [nvarchar](450) NULL,
	[GroupName] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplateGroups] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FormTemplateGroups] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FormTemplateGroups]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateGroups_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateGroups] CHECK CONSTRAINT [FK_FormTemplateGroups_FormTemplate]
GO

ALTER TABLE [dbo].[FormTemplateGroups]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateGroups_RequestGroups] FOREIGN KEY([GroupId])
REFERENCES [dbo].[RequestGroups] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateGroups] CHECK CONSTRAINT [FK_FormTemplateGroups_RequestGroups]
GO


