USE [GeeODb]
GO

/****** Object:  Table [dbo].[RequestFormWatchers]    Script Date: 3/26/2025 5:55:25 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[RequestFormWatchers](
	[Id] [nvarchar](450) NOT NULL,
	[RequestFormId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[FullName] [nvarchar](450) NULL,
	[UserName] [nvarchar](450) NULL,
	[Email] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequestFormWatchers] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[RequestFormWatchers] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[RequestFormWatchers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormWatcher_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[RequestFormWatchers] CHECK CONSTRAINT [FK_RequestFormWatcher_AspNetUsers]
GO

ALTER TABLE [dbo].[RequestFormWatchers]  WITH CHECK ADD  CONSTRAINT [FK_RequestFormWatcher_RequestForm] FOREIGN KEY([RequestFormId])
REFERENCES [dbo].[RequestForms] ([Id])
GO

ALTER TABLE [dbo].[RequestFormWatchers] CHECK CONSTRAINT [FK_RequestFormWatcher_RequestForm]
GO


