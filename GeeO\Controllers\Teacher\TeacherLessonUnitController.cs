﻿using GeeO.Data;
using GeeO.Dto;
using GeeO.GridVo;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using GeeO.Services;
using GeeO.Common;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TeacherLessonUnitController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;

        public TeacherLessonUnitController(GeeODbContext context, IAcadManageService acadManageService)
        {
            _context = context;
            _acadManageService = acadManageService;
        }

        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassesToCopy(string teacherId, string classId, string level)
        {
            var data = _context.ClassTeacher
                    .Where(c => c.TeacherId == teacherId && c.ClassId != classId && c.ClassCourse.Level.Name == level)
                    .Include(c => c.Teacher)
                    .Include(c => c.ClassCourse)
                        .ThenInclude(c => c.Level)
                    .AsNoTracking();
            var query = from x in data
                        orderby x.ClassCourse.Level.Name, x.ClassCourse.Name
                        select new ClassCourseGrid
                        {
                            Id = x.ClassCourse.Id,
                            Level = x.ClassCourse.Level.Name,
                            Class = x.ClassCourse.Name,
                            Description = x.ClassCourse.Description,
                            Teacher = x.Teacher.FullName
                        };
            return await query.ToListAsync().ConfigureAwait(false);
        }
        [HttpPost("[action]")]
        public async Task<ActionResult> CopyLessonsToClasses(CopyLessonsInfo copyLessonsInfo)
        {
            List<TeacherLessonUnit> srcLessonUnits = await _context.TeacherLessonUnit.Where(x => !x.Unit.IsDeleted)
                .Where(tl => tl.ClassLesson.ClassId == copyLessonsInfo.srcClassId)
                .Include(tl => tl.ClassLesson)
                .ToListAsync().ConfigureAwait(false);
            foreach (string classId in copyLessonsInfo.destClassIds)
            {
                List<TeacherLessonUnit> existLessonUnits = await _context.TeacherLessonUnit.Where(tl => tl.ClassLesson.ClassId == classId && !tl.Unit.IsDeleted).ToListAsync().ConfigureAwait(false);
                foreach (TeacherLessonUnit srcLessonUnit in srcLessonUnits)
                {
                    TeacherLessonUnit uptLessonUnit = existLessonUnits.Count > 0 ? existLessonUnits.Where(lu => lu.UnitId == srcLessonUnit.UnitId).FirstOrDefault() : null;
                    if (uptLessonUnit != null)
                    {
                        uptLessonUnit.MaterialId = srcLessonUnit.MaterialId;
                        uptLessonUnit.Note = srcLessonUnit.Note;
                        uptLessonUnit.Time = srcLessonUnit.Time;
                        _context.Entry(uptLessonUnit).State = EntityState.Modified;
                    }
                    else
                    {
                        _context.TeacherLessonUnit.Add(new TeacherLessonUnit()
                        {
                            TeacherId = copyLessonsInfo.teacherId,
                            ClassLessonId = _context.ClassLesson.Where(cl => cl.ClassId == classId && cl.LessonId == srcLessonUnit.ClassLesson.LessonId).First().Id,
                            UnitId = srcLessonUnit.UnitId,
                            MaterialId = srcLessonUnit.MaterialId,
                            Note = srcLessonUnit.Note,
                            Time = srcLessonUnit.Time
                        });
                    }
                }
            }

            await _context.SaveChangesAsync().ConfigureAwait(false);
            return NoContent();
        }
        public class CopyLessonsInfo
        {
            public string teacherId;
            public string srcClassId;
            public List<string> destClassIds;
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassByTeacher(string userId)
        {
            List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var user = await _context.AspNetUsers.Where(x => x.Id == userId).Include(x => x.Role).FirstOrDefaultAsync();
            if (user == null)
            {
                return NotFound();
            }

            var query = from ct in _context.ClassTeacher.Where(c => (user.Role.Name != EnumsHelper.GetDescription(AcadRoles.Teacher) || c.TeacherId == userId) &&
                                                                    (assignedCampus == null || assignedCampus.Contains(c.ClassCourse.CampusId)))
                        orderby ct.ClassCourse.Level.Name, ct.ClassCourse.Name
                        select new ClassCourseGrid
                        {
                            Id = ct.ClassCourse.Id,
                            Level = ct.ClassCourse.Level.Name,
                            Class = ct.ClassCourse.Name,
                            Description = ct.ClassCourse.Description,
                            Schedule = ct.ClassCourse.Schedule != null ? ct.ClassCourse.Schedule.ScheduleFormat : "",
                            StartTimeLocal = ct.ClassCourse.Schedule != null ? ct.ClassCourse.Schedule.StartTimeLocal : "",
                            EndTimeLocal = ct.ClassCourse.Schedule != null ? ct.ClassCourse.Schedule.EndTimeLocal : "",
                            Teacher = ct.Teacher.FullName
                        };

            if (user.Role.Name == EnumsHelper.GetDescription(AcadRoles.Teacher))
            {
                return await query.ToListAsync().ConfigureAwait(false);
            }

            var result = query.AsEnumerable().GroupBy(cc => cc.Id).Select(dd => new ClassCourseGrid
                        {
                            Id = dd.Key,
                            Level = dd.Select(ee => ee.Level).First(),
                            Class = dd.Select(ee => ee.Class).First(),
                            Description = dd.Select(ee => ee.Description).First(),
                            Schedule = dd.Select(ee => ee.Schedule).First(),
                            StartTimeLocal = dd.Select(ee => ee.StartTimeLocal).First(),
                            EndTimeLocal = dd.Select(ee => ee.EndTimeLocal).First(),
                            Teacher = string.Join(", ", dd.Select(ee => ee.Teacher).ToArray())
                        });

            return result.ToList();
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<ClassCourseGrid>>> GetClassLessonByTeacher(string id)
        {
            var data = _context.ClassCourse
                    .Include(c => c.Level)
                    .Include(c => c.ClassLessons)
                    .Where(c => c.Id == id)
                    .AsNoTracking();
            var result = from x in data
                         from l in x.ClassLessons where !l.Lesson.IsDeleted
                         orderby l.Lesson.CreatedDate
                         select new ClassCourseGrid
                         {
                             Id = l.Id,
                             RouteId = x.Id,
                             Level = x.Level.Name,
                             Class = x.Name,
                             Description = x.Description,
                             Lesson = l.Lesson.Lesson,
                             Subject = l.Lesson.Subject,
                             Content = l.Lesson.Content,
                             Tb = l.Lesson.Tb,
                             SubId = (l.TeacherLessonLogs.FirstOrDefault(c=> c.ClassLessonId == l.Id).Id ?? "0") + "/" + l.Id + "/" + x.Id //url: logid/classlessonId/classcourseId
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        //param: id => lessonId
        [HttpGet]
        [Route("unit-plan/{id}")]
        public async Task<ActionResult<IEnumerable<TeacherLessonUnitGrid>>> GetUnitPlanByLessonId(string id)
        {
            var classLesson = await _context.ClassLesson.FindAsync(id);
            if (classLesson == null)
            {
                return NotFound();
            }
            var data = from u in _context.LessonPlanUnit
                       join tl in _context.TeacherLessonUnit.Where(c => c.ClassLessonId == classLesson.Id).Include(x => x.Material) on u.Id equals tl.UnitId into t
                       from teacherLesson in t.DefaultIfEmpty()
                       where u.LessonPlanId == classLesson.LessonId && !u.IsDeleted
                       orderby u.SortOrder
                       select new TeacherLessonUnitGrid
                       {
                           Id = teacherLesson != null ? teacherLesson.Id : "NA--" + Guid.NewGuid().ToString(),
                           UnitPlanId = u.Id,
                           Time = teacherLesson != null && teacherLesson.Time != null ? teacherLesson.Time : u.Time,
                           Procedures = u.Procedures,
                           Description = u.Description,
                           Materials = u.Materials,
                           LearningOutcome = u.LearningOutcome,
                           TeacherActivities = u.TeacherActivities,
                           Note = teacherLesson != null && teacherLesson.Note != null ? teacherLesson.Note : u.Note,
                           MaterialsTeacher = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material.Name : (u.Material != null ? u.Material.Name : ""),
                           //MaterialsTeacher =  (u.Material != null ? u.Material.Name : ""),
                           TeacherId = "",
                           ClassLessonId = "",
                           MaterialId = teacherLesson != null && teacherLesson.Material != null ? teacherLesson.Material.Id : (u.Material != null ? u.Material.Id : "NA"),
                           //MaterialId = (u.Material != null ? u.Material.Id : "NA"),
                           LessonPlanId = u.LessonPlanId,
                           HasTeacherLessonUnit = teacherLesson != null && teacherLesson.Material != null ? true : false
                       };
            return await data.ToListAsync().ConfigureAwait(false);
        }
        [HttpPost]
        [Route("unit-plan/save")]
        public async Task<ActionResult> SaveTeacherLessonUnitPlan(TeacherLessonUnitDto model)
        {
            if (model != null)
            {
                string formatId = model.Id.Trim().ToUpper().Substring(0, 4);
                if (string.IsNullOrEmpty(model.Id) || formatId == "NA--")
                {

                    var unitPlan = await _context.LessonPlanUnit.FindAsync(model.UnitPlanId);
                    var teacher = await _context.AspNetUsers.FindAsync(model.TeacherId);
                    if (unitPlan == null || teacher == null)
                    {
                        return NotFound();
                    }
                    TeacherLessonUnit teacherLessonUnit = new TeacherLessonUnit()
                    {
                        UnitId = model.UnitPlanId,
                        TeacherId = teacher.Id,
                        ClassLessonId = model.ClassLessonId,
                    };
                    if (model.NameAct.ToLower() == "editnote")
                    {
                        teacherLessonUnit.Note = model.Note;
                    }
                    else if (model.NameAct.ToLower() == "editmaterial")
                    {
                        teacherLessonUnit.MaterialId = model.MaterialId;
                    } 
                    else
                    {
                        teacherLessonUnit.Time = model.Time;
                    }
                    _context.TeacherLessonUnit.Add(teacherLessonUnit);
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                else
                {
                    var teacherLessonUnit = await _context.TeacherLessonUnit.FindAsync(model.Id);
                    if (teacherLessonUnit == null)
                    {
                        return NotFound();
                    }
                    if (model.NameAct.ToLower() == "editnote")
                    {
                        teacherLessonUnit.Note = model.Note;
                    }
                    else if (model.NameAct.ToLower() == "editmaterial")
                    {
                        teacherLessonUnit.MaterialId = model.MaterialId;
                    }
                     else
                    {
                        teacherLessonUnit.Time = model.Time;
                    }
                    _context.Entry(teacherLessonUnit).State = EntityState.Modified;
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                return Ok();
            }
            return NotFound();
        }
    }
}
