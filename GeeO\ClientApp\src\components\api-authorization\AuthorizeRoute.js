import React, { useContext } from 'react';
import { Route } from 'react-router-dom';
import { Loading } from '../ui/Loading';
import { AuthContext } from './../../auth/AuthContext'; // Import AuthContext

const AuthorizeRoute = ({ component: Component, layout: Layout, ...rest }) => {
  const context = useContext(AuthContext);

  const { authenticated, loading } = useContext(AuthContext);
  const returnUrl = encodeURIComponent(window.location.href);
  const redirectUrl = `${process.env.REACT_APP_GEEO_LOGIN_APP}?returnUrl=${returnUrl}`;

  if (loading) {
    return <Loading />;
  }

  return (
    <Route
      {...rest}
      render={props =>
        authenticated ? (
          <Layout>
            <Component {...props} />
          </Layout>
        ) : (
          (window.location.href = redirectUrl)
          // <Redirect to={redirectUrl} />
        )
      }
    />
  );
};

export default AuthorizeRoute;
