@font-face {
  font-family: 'RalewayBlack';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/Raleway-Black.ttf');
}
@font-face {
  font-family: 'SVN-RalewayExtraBold';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/Raleway-Black.ttf');
}
@font-face {
  font-family: 'RalewayRegular';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Regular.ttf');
}
@font-face {
  font-family: 'SVN-Raleway';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Regular.ttf');
}
@font-face {
  font-family: 'RalewayBold';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Bold.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway Bold';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Bold.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewayBoldItalic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Bold-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway Bold Italic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Bold-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewayItalic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway Italic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewayMedium';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Medium.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway Medium';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Medium.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewayMediumItalic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Medium-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway Medium Italic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-Medium-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewaySemiBold';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-SemiBold.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway SemiBold';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-SemiBold.ttf') format('truetype');
}
@font-face {
  font-family: 'RalewaySemiBoldItalic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-SemiBold-Italic.ttf') format('truetype');
}
@font-face {
  font-family: 'SVN-Raleway SemiBold Italic';
  font-weight: normal;
  font-style: normal;
  src: url('./fonts/SVN-Raleway-SemiBold-Italic.ttf') format('truetype');
}

:root {
  --font-family-sans-serif: 'RalewayRegular', 'SVN-Raleway', -apple-system,
    BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;
}

html {
  font-family: 'RalewayRegular', 'SVN-Raleway', 'RalewayItalic', 'RalewayMedium',
    'RalewayMediumItalic', 'RalewaySemiBold', 'RalewaySemiBoldItalic',
    'RalewayBold', 'RalewayBoldItalic', 'RalewayBlack', 'SVN-RalewayExtraBold';
}

body {
  -ms-touch-action: none;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  --color-background: #ced5e1;
  --color-on-background: #fff;

  --color-tooltip: rgba(0, 5, 11, 0.9);
  --color-on-tooltip: #fff;

  --color-handle: #67c2e4;
  --color-handle-drag: #2c343a;
}

a:hover {
  color: rgba(0, 0, 0, 0.54) !important;
}

/* .swiper-container-horizontal > .swiper-scrollbar {
  margin-bottom: -10px;
} */
