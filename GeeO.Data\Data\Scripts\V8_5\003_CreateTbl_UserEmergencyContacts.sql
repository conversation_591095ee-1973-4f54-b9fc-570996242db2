﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[UserEmergencyContacts]    Script Date: 11/25/2024 11:48:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[UserEmergencyContacts](
	[Id] [nvarchar](450) NOT NULL,
	[UserInfoId] [nvarchar](450) NULL,
	[ContactName] [nvarchar](450) NULL,
	[ContactPhone] [nvarchar](450) NULL,
	[Relationship] [nvarchar](450) NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
 CONSTRAINT [PK__UserEmer__3214EC072B43DAFB] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserEmergencyContacts]  WITH CHECK ADD  CONSTRAINT [FK_UserEmergencyContacts_UserInfo] FOREIGN KEY([UserInfoId])
REFERENCES [dbo].[UserInfo] ([Id])
GO

ALTER TABLE [dbo].[UserEmergencyContacts] CHECK CONSTRAINT [FK_UserEmergencyContacts_UserInfo]
GO


