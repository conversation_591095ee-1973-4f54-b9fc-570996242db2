﻿insert into ClassStudent(Id, ClassId, StudentId, StudentType, SortOrder, NumberOfDemoSessions)
select LOWER(CONVERT(nvarchar(450), NEWID())), dt.ClassId, dt.StudentInfoId, 1, 1, 0
from (
select distinct cl.ClassId, sll.StudentInfoId
from StudentLessonLogData sll
join TeacherLessonLog tll on sll.LogId = tll.Id
join ClassLesson cl on tll.ClassLessonId = cl.Id
where not exists (select 1 from ClassStudent cs where cs.StudentId = sll.StudentInfoId)
--order by cl.ClassId
) dt


--select * from ClassStudent cs where cs.StudentId = 'f44302b9-c173-4685-badc-e4bfd6c75fcd'