﻿using GeeO.Data;
using GeeO.Data.Utils;
using GeeO.Mobile.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class ClassController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly IAzureAdService _azureAdService;

        public ClassController(GeeODbContext context,
                                IAzureAdService azureAdService,
                                IStudentPaymentService studentPaymentService)
        {
            _context = context;
            _azureAdService = azureAdService;
            _studentPaymentService = studentPaymentService;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<IEnumerable<string>>> GetClassTeacherInfo(string studentId)
        {
            var result = from cls in _context.ClassCourse
                         from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                         from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId).DefaultIfEmpty()
                         from tch in _context.UserInfo.Where(tch => ct.TeacherId == tch.UserId)
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                         select $"{tch.TitleName} {tch.EnglishName}";

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<List<ExamResult>>> GetExamResults(string studentId, string classId)
        {
            var classCourse = await _context.ClassCourse
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == classId);

            var query =
                from er in _context.ExamResult
                where er.ClassId == classId && er.StudentId == studentId
                join erf in _context.ExamResultForm
                    on new { er.ExamType, classCourse.LevelId }
                    equals new { erf.ExamType, erf.LevelId }
                    into erfGroup
                from erf in erfGroup.DefaultIfEmpty()
                orderby er.ExamType
                select new ExamResult
                {
                    ExamDate = er.CreatedDate,
                    ExamType = er.ExamType,
                    ExamName = erf.ExamName ?? null,
                    TeacherComment = er.TeacherComment,
                    ExamResultJson = er.ExamResultJson
                };

            var results = await query
                .AsNoTracking()
                .ToListAsync();

            return results;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<IEnumerable<ExamResult>>> GetExamResults(string studentId)
        {
            var result = from er in _context.ExamResult.Where(er => er.StudentId == studentId)
                         from cls in _context.ClassCourse.Where(cls => cls.Id == er.ClassId)
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                         orderby er.ExamType
                         select new ExamResult
                         {
                             ExamDate = er.CreatedDate,
                             ExamType = er.ExamType,
                             TeacherComment = er.TeacherComment,
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<ClassInfo>> GetCurrentWeekInfo(string studentId)
        {
            var currentweekInfo = await _studentPaymentService.GetCurrentWeekInfoAsync(studentId);
            currentweekInfo.TotalOfStarScore = await _studentPaymentService.GetTotalStarScore(studentId);

            return currentweekInfo;
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<IEnumerable<ClassInfo>>> GetClassByStudent(string studentId)
        {
            var query =
                from cs in _context.ClassStudent
                where cs.StudentId == studentId
                join cls in _context.ClassCourse on cs.ClassId equals cls.Id
                join lv in _context.StudyLevel on cls.LevelId equals lv.Id
                join cps in _context.Campus on cls.CampusId equals cps.Id
                join sch in _context.Schedule on cls.Id equals sch.ClassCourseId
                join aan in _context.AcadAnnounce on cls.Id equals aan.ClassId into aanGroup
                from aan in aanGroup.Where(a => a.Latest).DefaultIfEmpty()
                let primaryTeacher = cls.ClassTeachers
                    .Where(ct => ct.IsPrimary)
                    .Select(ct => new
                    {
                        ct.Teacher.Id,
                        FullName = ct.Teacher.FullName,
                        ElAccount = ct.Teacher.UserInfo.ElAccount
                    })
                    .FirstOrDefault()
                orderby sch.StartDate descending
                select new ClassInfo()
                {
                    StartDate = sch.StartDate,
                    Id = cls.Id,
                    Name = cls.Name,
                    Campus = cps.Address,
                    TeacherEmail = primaryTeacher.ElAccount,
                    TeacherId = primaryTeacher.Id,
                    TeacherName = primaryTeacher.FullName,
                    NumberOfLessons = lv.NumberOfLesson.Value,
                    AcademicAnnounce = aan != null ? aan.Content : null,
                    IsCurrentClass = cs.StudentType == Models.ClassType.Regular,
                    CurrentLesson = (from cl in _context.ClassLesson
                                     where cl.ClassId == cls.Id
                                     join tll in _context.TeacherLessonLog on cl.Id equals tll.ClassLessonId
                                     join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                                     orderby tll.LogDateTime descending
                                     select lp.Lesson)
                                    .FirstOrDefault()
                };

            return await query.AsNoTracking().ToListAsync();
        }

        [HttpGet("[action]/{studentId}")]
        public async Task<ActionResult<IEnumerable<LessonInfo>>> GetLessonBook(string studentId)
        {
            var result = from cls in _context.ClassCourse
                         from lv in _context.StudyLevel.Where(lv => cls.LevelId == lv.Id)
                         from lp in _context.LessonPlan.Where(lp => lv.Id == lp.LevelId)
                         from lc in _context.LessonContent.Where(lc => lp.Id == lc.LessonPlanId).DefaultIfEmpty()
                         from cs in _context.ClassStudent.Where(cs => cls.Id == cs.ClassId && cs.StudentId == studentId)
                         from cl in _context.ClassLesson.Where(cl => cls.Id == cl.ClassId && cl.LessonId == lp.Id)
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId /*&& sch.EndDate.Date >= DateTime.Today*/)
                         orderby sch.StartDate descending, lp.CreatedDate
                         select new LessonInfo()
                         {
                             LevelName = lv.Name,
                             ClassId = cls.Id,
                             LessonId = lp.Id,
                             NumberOfLessons = lv.NumberOfLesson.Value,
                             LessonNumber = lp.Lesson,
                             LessonSubject = lp.Subject,
                             Textbook = lc.Textbook,
                             Page = lc.Page,
                             CoreWords = lc.CoreWords,
                             KeyWords = lc.KeyWords,
                             KeyQuestions = lc.KeyQuestions,
                             KeyAnswers = lc.KeyAnswers,
                             Activities = lc.Activities,
                             Media = lc.Media,
                             LessonMediaList = (from lm in _context.LessonMedia.Where(m => m.StudentId == studentId && m.ClassLessonId == cl.Id) select lm).ToList()
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<IEnumerable<ClassInfo>>> GetClassByTeacher(string teacherId)
        {
            List<ClassInfo> classes = await (from cls in _context.ClassCourse.Include(cls => cls.ClassStudents)
                                             from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId && ct.TeacherId == teacherId)
                                             from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date >= DateTime.Today)
                                             orderby sch.StartDate descending
                                             select new ClassInfo()
                                             {
                                                 Id = cls.Id,
                                                 Name = cls.Name,
                                                 Campus = cls.Campus.Name,
                                                 NumberOfStudents = cls.ClassStudents.Count(x => x.StudentType == Models.ClassType.Regular && x.Student.SuspendDate == null && x.Student.TerminateDate == null)
                                             })
                                             .ToListAsync().ConfigureAwait(false);

            foreach (ClassInfo item in classes)
            {
                List<StudentInfo> students = await (from cs in _context.ClassStudent.Where(cs => cs.ClassId == item.Id)
                                                    from std in _context.Student.Where(std => std.Id == cs.StudentId)
                                                    orderby std.StudentName
                                                    select new StudentInfo
                                                    {
                                                        Id = std.Id,
                                                        Name = std.StudentName,
                                                        EnglishName = std.EnglishName,
                                                        Birthday = std.Birthday
                                                    })
                                                    .ToListAsync().ConfigureAwait(false);

                List<LessonPlanData> lessons = await (from cl in _context.ClassLesson.Where(cl => cl.ClassId == item.Id)
                                                      from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                                                      orderby cl.StartTime
                                                      select new LessonPlanData
                                                      {
                                                          ClassLessonId = cl.Id,
                                                          LessonId = lp.Id,
                                                          Lesson = lp.Lesson,
                                                          Subject = lp.Subject,
                                                          Content = lp.Content,
                                                          StartTime = cl.StartTime.Value,
                                                          EndTime = cl.EndTime.Value
                                                      })
                                                     .ToListAsync().ConfigureAwait(false);

                LessonUtils.GetWeekLessonInfo(lessons, out int weekNumber, out List<string> thisWeekLessons);

                item.CurrentWeek = weekNumber;
                item.CurrentWeekLesson = string.Join(",", thisWeekLessons);
                item.LessonPlanDataList = lessons;
                item.StudentList = students;
            }

            return classes;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ClassInfo>> GetClass(string id)
        {
            var classInfo = await _context.ClassCourse
                .Where(c => c.Id == id)
                .Select(cls => new ClassInfo
                {
                    Id = cls.Id,
                    Name = cls.Name,
                    Campus = cls.Campus.Name,
                    NumberOfStudents = cls.ClassStudents.Count(cs =>
                        cs.StudentType == Models.ClassType.Regular &&
                        cs.Student.SuspendDate == null &&
                        cs.Student.TerminateDate == null),
                    StudentList = cls.ClassStudents
                        .Where(cs => cs.ClassId == cls.Id)
                        .Select(cs => new StudentInfo
                        {
                            Id = cs.Student.Id,
                            Name = cs.Student.StudentName,
                            EnglishName = cs.Student.EnglishName,
                            Birthday = cs.Student.Birthday
                        })
                        .OrderBy(s => s.Name)
                        .ToList(),
                    LessonPlanDataList = cls.ClassLessons
                        .Select(cl => new LessonPlanData
                        {
                            ClassLessonId = cl.Id,
                            LessonId = cl.Lesson.Id,
                            Lesson = cl.Lesson.Lesson,
                            Subject = cl.Lesson.Subject,
                            Content = cl.Lesson.Content,
                            StartTime = cl.StartTime.Value,
                            EndTime = cl.EndTime.Value
                        })
                        .OrderBy(l => l.StartTime)
                        .ToList()
                })
                .AsNoTracking()
                .SingleOrDefaultAsync();

            if (classInfo == null)
            {
                return NotFound();
            }

            // Process lessons to get week information
            LessonUtils.GetWeekLessonInfo(
                classInfo.LessonPlanDataList,
                out int weekNumber,
                out List<string> thisWeekLessons
            );

            classInfo.CurrentWeek = weekNumber;
            classInfo.CurrentWeekLesson = string.Join(",", thisWeekLessons);

            return classInfo;
        }

        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<IEnumerable<ClassInfo>>> GetEndedClassByTeacher(string teacherId)
        {
            List<ClassInfo> classes = await (from cls in _context.ClassCourse.Include(cls => cls.ClassStudents)
                                             from ct in _context.ClassTeacher.Where(ct => cls.Id == ct.ClassId && ct.TeacherId == teacherId)
                                             from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId && sch.EndDate.Date < DateTime.Today)
                                             orderby sch.StartDate descending
                                             select new ClassInfo()
                                             {
                                                 Id = cls.Id,
                                                 Name = cls.Name,
                                                 NumberOfStudents = cls.ClassStudents.Count
                                             })
                                             .ToListAsync().ConfigureAwait(false);

            return classes;
        }

        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Models.Student>>> GetStudentsByClass(string classId)
        {
            List<Models.Student> students = await (from cs in _context.ClassStudent.Where(cs => cs.ClassId == classId &&
                                                                                                cs.StudentType == Models.ClassType.Regular)
                                                   from std in _context.Student.Where(std => std.Id == cs.StudentId &&
                                                                                             std.SuspendDate == null && std.TerminateDate == null)
                                                   from sea in _context.StudentExternalAccount.Where(sea => std.Id == sea.StudentId)
                                                   orderby std.StudentName
                                                   select new Models.Student()
                                                   {
                                                       Id = std.Id,
                                                       StudentName = std.StudentName,
                                                       EnglishName = std.EnglishName,
                                                       ElAccount = sea.Email
                                                   })
                                                    .ToListAsync().ConfigureAwait(false);

            foreach (Models.Student student in students)
            {
                student.ImageBase64 = await _azureAdService.GetUserPhotoAsync(student.ElAccount);
            }

            return students;
        }

    }
}
