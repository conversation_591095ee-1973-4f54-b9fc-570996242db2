﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GeeO.Api.Hubs;
using GeeO.Api.Hubs.Clients;
using GeeO.Api.Models;
using GeeO.Common;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace GeeO.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class LiveClassController : ControllerBase
    {
        private readonly IHubContext<LiveClassHub, ILiveClassClient> _liveClassHub;
        private readonly GeeODbContext _context;

        public LiveClassController(IHubContext<LiveClassHub, ILiveClassClient> liveClassHub, GeeODbContext context)
        {
            _liveClassHub = liveClassHub;
            _context = context;
        }

        [HttpPost("[action]")]
        public async Task CallRemoteLearning(LiveClassMessage message)
        {
            IReadOnlyList<string> connectionIds = LookUpClassStudents(message);
            await _liveClassHub.Clients.Clients(connectionIds).CallRemoteLearning();
        }

        [HttpPost("[action]")]
        public async Task CallSelfLearning(LiveClassMessage message)
        {
            IReadOnlyList<string> connectionIds = LookUpClassStudents(message);
            await _liveClassHub.Clients.Clients(connectionIds).CallSelfLearning();
        }

        private static IReadOnlyList<string> LookUpClassStudents(LiveClassMessage message)
        {
            IEnumerable<string> sKeyList = message.Recipients.Select(x => $"{message.ClassLessonId}/student/{x}");
            List<KeyValuePair<string, string>> onlineUsers = LiveClassHub.OnlineClientsDict.Where(x => sKeyList.Contains(x.Key)).ToList();
            IReadOnlyList<string> connectionIds = onlineUsers.Select(x => x.Value).ToList().AsReadOnly();
            return connectionIds;
        }
    }
}
