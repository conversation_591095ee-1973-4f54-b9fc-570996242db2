{
    "https_port": 443,
    "ConnectionStrings": {
        "GeeODbConnection": "Server=.;Database=GeeODb;Integrated Security=True;MultipleActiveResultSets=true;TrustServerCertificate=True;",
        "SSISConnection": "Data Source=localhost\\GEEODBSQL;Initial Catalog=master;Integrated Security=SSPI;TrustServerCertificate=True;",
        "GeeODbConnectionAzure": "Data Source=tcp:geeodbsrv.database.windows.net,1433;Initial Catalog=GeeODb;User Id=acadazure@geeodbsrv;Password=***************;TrustServerCertificate=True;",
        "SSISConnectionAzure": "Data Source=localhost;Initial Catalog=master;Integrated Security=SSPI;"
    },
    "Logging": {
        "LogLevel": {
            "Default": "Warning"
        }
    },
    "IdentityServer": {
        "Authority": "http://localhost:57538",
        "Clients": {
            "GeeO": {
                "Profile": "IdentityServerSPA"
            }
        },
        "Key": {
            "Type": "File",
            "StorageFlags": "MachineKeySet",
            "FilePath": "GeeOAuth.pfx",
            "Password": "GeeO@Local"
        }
    },
    "Authentication": {
        "Google": {
            "OrgDomain": "gee-o.edu.vn",
            "ClientId": "736023093316-bnauop4r1scmg2sbte9egdhgln0sjr83.apps.googleusercontent.com",
            "ClientSecret": "ZgY7BhbAUpfEHY4j3025dcm0"
        },
        "Microsoft": {
            "ClientId": "240e6539-fabe-4cbf-a4b8-3f75f396b496",
            "ClientSecret": "**********************************"
        }
    },
    "IssuerUri": "http://localhost:5001",
    "Sentry": {
        // The DSN can also be set via environment variable
        "Dsn": "https://<EMAIL>/3339",
        "Release": "1.0",
        // Enable Sentry tracing features
        "EnableTracing": true,
        // Opt-in for payload submission
        "MaxRequestBodySize": "Always",
        // Sends Cookies, User Id when one is logged on and user IP address to sentry. It's turned off by default.
        "SendDefaultPii": true,
        // Whether to add System.Diagnostics.Activity data to the event::
        // For more: https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/ActivityUserGuide.md
        "IncludeActivityData": true,
        // Send the stack trace of captured messages (e.g: a LogWarning without an exception)
        "AttachStackTrace": true,
        // The flag below can be used to see the internal logs of the SDK in the applications log (it's off by default)
        "Debug": true,
        // By default the level is Debug but it can be changed to any level of SentryLevel enum
        "DiagnosticLevel": "Error"
    },
    "AllowedHosts": "*"
}
