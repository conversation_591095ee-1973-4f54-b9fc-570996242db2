﻿###
### GEE-O ENGLISH script for deleting duplicated student
###

# read settings 
$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

# load common utilities
.$PSScriptRoot/../Common/Common.ps1


# declare sql in a variable and pass it to -Query switch
$sqlServer = $settings.database.server
$database = $settings.database.database
$outFolderRoot = $settings.outFolder
$ExcelFile = $settings.excelFile


function Delete-DupStudent
{
    Write-RunLog "====================== Starting ======================"

	$ExcelObject = New-Object -ComObject Excel.Application  
	$ExcelObject.Visible = $false 
	$ExcelObject.DisplayAlerts = $false

	$WorkBook = $ExcelObject.Workbooks.Open($ExcelFile)
	$Worksheet = $Workbook.Worksheets.Item(1)
	$totalNoOfRecords = ($WorkSheet.UsedRange.Rows).Count
	Write-Output "Total No. of records: $($totalNoOfRecords - 1)"
	
	for ($Row = 2; $Row -le $totalNoOfRecords; $Row++)
	{
		$DupStudentId = $Worksheet.Cells.Item($Row, 1).Text
		Write-Output "DupStudentId: $DupStudentId"
		
		$sqldeldupstudent="
			delete from StudentCourse where StudentId = '$DupStudentId'
			delete from StudentExternalAccount where StudentId = '$DupStudentId'
			delete from StudentParent where StudentId = '$DupStudentId'
			delete from StudentLessonLogData where StudentInfoId = '$DupStudentId'
			delete from ClassStudent where StudentId = '$DupStudentId'
			delete from StudentClassChange where StudentId = '$DupStudentId'
			delete from Student where Id = '$DupStudentId'
		"
		execute-dbquery -query $sqldeldupstudent
	}

	$ExcelObject.Quit()
    Write-RunLog "====================== Ending ======================"
}

Write-Host "PowerShell version $($PSVersionTable.PSVersion)"

Delete-DupStudent
