import React, { Component, Fragment } from 'react';
import { PropsRoute } from '../common/route/PropsRoute';
import { AdminUtilsPaths, AdminUtilsActions } from './AdminUtilsConstants';
import AdminUtils from './AdminUtils';

export default class AdminUtilsRoutes extends Component {
  render() {
    return (
      <Fragment>
        <PropsRoute
          path={AdminUtilsPaths.CheckStudentLog}
          component={AdminUtils}
          action={AdminUtilsActions.CheckStudentLog}
        />
      </Fragment>
    );
  }
}
