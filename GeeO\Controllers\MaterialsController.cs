﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Identity;
using GeeO.Data.Extensions;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class MaterialsController : GeoOControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;

        public MaterialsController(UserManager<ApplicationUser> userManager, GeeODbContext context, IUploadService uploadService, IFileService fileService)
                    : base(userManager)
        {
            _context = context;
            _uploadService = uploadService;
            _fileService = fileService;
        }

        // GET: api/Materials
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Material>>> GetMaterial()
        {
            return await _context.Material.Where(m => m.Approved == true).ToListAsync().ConfigureAwait(false);
        }

        // POST: api/Materials/Approve/5
        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> Approve(string id)
        {
            var material = await _context.Material.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }

            material.Approved = true;
            _context.Entry(material).SetActivityState(ActivityActionType.Approved);

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MaterialExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // GET: api/Materials/GetForApproval
        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<Material>>> GetForApproval()
        {
            return await GetApprovedList(false).ConfigureAwait(false);
        }

        // GET: api/Materials/GetApproved
        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<Material>>> GetApproved()
        {
            return await GetApprovedList(true).ConfigureAwait(false);
        }

        private async Task<ActionResult<IEnumerable<Material>>> GetApprovedList(bool isApproved)
        {
            var materials = _context.Material
                    .Where(m => m.TeacherId != null && m.Approved == isApproved)
                    .Include(m => m.Teacher);
            foreach (var material in materials)
            {
                if (material.Teacher != null)
                {
                    material.Teacher.Materials = null;
                }
            }

            List<Material> results = await materials.ToListAsync().ConfigureAwait(false);
            return results;
        }

        // GET: api/Materials/GetApprovedFor/5
        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<IEnumerable<Material>>> GetApprovedFor(string teacherId)
        {
            var materials = _context.Material
                    .Where(m => m.TeacherId == teacherId && m.Approved == true)
                    .Include(m => m.Teacher);
            foreach (var material in materials)
            {
                material.Teacher.Materials = null;
            }

            List<Material> results = await materials.ToListAsync().ConfigureAwait(false);
            return results;
        }

        // GET: api/Materials/GetByTeacher/5
        [HttpGet("[action]/{teacherId}")]
        public async Task<ActionResult<IEnumerable<Material>>> GetByTeacher(string teacherId)
        {
            var materials = _context.Material
                    .Where(m => m.TeacherId == teacherId && m.Approved == false)
                    .Include(m => m.Teacher);
            foreach (var material in materials)
            {
                material.Teacher.Materials = null;
            }

            List<Material> results = await materials.ToListAsync().ConfigureAwait(false);
            return results;
        }

        // GET: api/Materials/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Material>> GetMaterial(string id)
        {
            var material = await _context.Material.FindAsync(id);

            if (material == null)
            {
                return NotFound();
            }

            return material;
        }

        // PUT: api/Materials/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutMaterial(string id, Material material)
        {
            if (id != material.Id)
            {
                return BadRequest();
            }

            _context.Entry(material).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MaterialExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Materials
        [HttpPost]
        public async Task<ActionResult<Material>> PostMaterial(Material material)
        {
            _context.Material.Add(material);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetMaterial", new { id = material.Id }, material);
        }

        // DELETE: api/Materials/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<Material>> DeleteMaterial(string id)
        {
            var material = await _context.Material.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }

            _context.Material.Remove(material);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return material;
        }

        private bool MaterialExists(string id)
        {
            return _context.Material.Any(e => e.Id == id);
        }

        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> UploadFile(string id, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            await _uploadService.UploadMaterialFile(id, files).ConfigureAwait(false);

            return Ok(new { count = files.Count });
        }

        [HttpGet("[action]/{id}/{filename}")]
        public IActionResult GetForView(string id, string filename)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(filename))
            {
                return NotFound();
            }

            if (!filename.EndsWith(".zip", true, null))
            {
                AddContentType(filename);
            }

            return _fileService.ViewDownloadMaterial(id, filename, null, null, false) ?? (IActionResult)NotFound();
        }

        [HttpGet("[action]/{id}/{filename}/{subFolder}/{resourceFileName}")]
        public IActionResult GetForViewContent(string id, string filename, string subFolder, string resourceFileName)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(filename))
            {
                return NotFound();
            }

            AddContentType(filename);
            if (!filename.EndsWith("zip", true, null))
            {
            }

            return _fileService.ViewDownloadMaterial(id, filename, subFolder, resourceFileName, false) ?? (IActionResult)NotFound();
        }

        [HttpGet("[action]/{id}/{lessonPlanUnitId}/{isUnitPlan}")]
        public async Task<ActionResult<IEnumerable<Material>>> GetMaterialInTeacherUnit(string id, string lessonPlanUnitId, bool isUnitPlan)
        {
            if (!isUnitPlan)
            {
                var materialCurrent = await _context.Material.Where(m => m.Id == id && (m.Approved == null || m.Approved == true))
                    .Include(x => x.TeacherLessonUnits)
                    .ToListAsync().ConfigureAwait(false);
                if (materialCurrent.Count > 0 && materialCurrent[0].TeacherLessonUnits.Count == 0)
                {
                    return materialCurrent;
                }

                var objLessonPlanUnit = await _context.LessonPlanUnit
                    .Where(x => x.Id == lessonPlanUnitId && string.IsNullOrEmpty(x.MaterialId) && !string.IsNullOrEmpty(x.Category)).FirstOrDefaultAsync().ConfigureAwait(false);
                if (objLessonPlanUnit != null)
                {
                    var objCategory = JsonConvert.DeserializeObject<Material>(objLessonPlanUnit.Category);
                    if (objCategory != null)
                    {
                        string mainSql = "SELECT * FROM Material ";
                        string sqlCondition = "WHERE (";
                        if (objCategory.Math)
                        {
                            sqlCondition += "Math = 1 AND ";
                        }
                        if (objCategory.Science)
                        {
                            sqlCondition += "Science = 1 AND ";
                        }
                        if (objCategory.Music)
                        {
                            sqlCondition += "Music = 1 AND ";
                        }
                        if (objCategory.Phonics)
                        {
                            sqlCondition += "Phonics = 1 AND ";
                        }
                        if (objCategory.Sightwords)
                        {
                            sqlCondition += "Sightwords = 1 AND ";
                        }
                        if (objCategory.English)
                        {
                            sqlCondition += "English = 1 AND ";
                        }
                        if (objCategory.SoftSkills)
                        {
                            sqlCondition += "SoftSkills = 1 AND ";
                        }
                        if (objCategory.A)
                        {
                            sqlCondition += "A = 1 AND ";
                        }
                        if (objCategory.M)
                        {
                            sqlCondition += "M = 1 AND ";
                        }
                        if (objCategory.E)
                        {
                            sqlCondition += "E = 1 AND ";
                        }
                        if (objCategory.V)
                        {
                            sqlCondition += "V = 1 AND ";
                        }
                        if (objCategory.C)
                        {
                            sqlCondition += "C = 1 AND ";
                        }
                        if (objCategory.J)
                        {
                            sqlCondition += "J = 1 AND ";
                        }
                        if (objCategory.S)
                        {
                            sqlCondition += "S = 1 AND ";
                        }

                        sqlCondition += "1 = 1) AND ( Approved = null OR Approved = 1 )";
                        return await _context.Material.FromSqlRaw(mainSql + sqlCondition).ToListAsync().ConfigureAwait(false);
                    }
                }
            }
            return await _context.Material.Where(m => m.Id != id && (m.Approved == null || m.Approved == true)).ToListAsync().ConfigureAwait(false);
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> ApproveAll(List<string> ids)
        {
            if (ids.Count == 0)
                return NoContent();
            foreach (string id in ids)
            {
                var material = await _context.Material.FindAsync(id);
                if (material == null)
                {
                    return NotFound();
                }

                material.Approved = true;
                _context.Entry(material).SetActivityState(ActivityActionType.Approved);

            }
            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                throw;
            }
            return NoContent();
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> DeleteApproves(List<string> ids)
        {
            if (ids.Count == 0)
                return NoContent();
            foreach (string id in ids)
            {
                var material = await _context.Material.FindAsync(id);
                if (material == null)
                {
                    return NotFound();
                }
                _context.Material.Remove(material);
            }
            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                throw;
            }
            return NoContent();
        }
    }
}
