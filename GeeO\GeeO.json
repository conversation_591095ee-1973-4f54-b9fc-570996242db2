{
  "SmsApi": {
    //"Url": "https://api.myhub.vn/api/Gateways/sendSMS?m_from=b98f18b0-e135-11e9-b39b-918e16596528&m_to={0}&m_message={1}&m_accessToken=5830d93813fad977e3f2b62703d34534b061e5c4adc49a7c2224fd82ef87626ef9902733dd831f1e0d8bdb2feffd043c"
    "Url": "http://************:8888/smsbn/api",
    "RQST": {
      "name": "send_sms_list",
      "REQID": "1",
      "LABELID": "181286",
      "CONTRACTTYPEID": "1",
      "CONTRACTID": "14810",
      "TEMPLATEID": "1131021",
      "PARAMS": [
        {
          "NUM": "1",
          "CONTENT": "{1}"
        }
      ],
      "SCHEDULETIME": "",
      "MOBILELIST": "{0}",
      "ISTELCOSUB": "0",
      "AGENTID": "199",
      "APIUSER": "geeo_api",
      "APIPASS": "M@tKh@u4332o22!@#",
      "USERNAME": "gee-o",
      "DATACODING": "0",
      "SALEORDERID": "",
      "PACKAGEID": ""
    }
  },
  "EmailTemplates": {
    "LessonContent": {
      "Subject": "Lesson Content",
      "Body": "<p>Dear,<br><p><br><p>Thanks,<br><p>GeeO English<br>",
      "AttachFilename": "{0} - Lesson Content.pdf"
    },
    "eSSLFeedbackContent": {
      "Name": "GeeO",
      "To": "support+{0}@gee-o.edu.vn",
      "Subject": "Thư góp ý từ eSSL App của khách hàng {0}, bé {1} lớp {2}",
      "Body": "<body><h1 style='color:#514f4f;'>Bạn nhận được một tin nhắn góp ý từ eSSL App vào lúc {0} </h1><p><strong>Tên phụ huynh:</strong> {1}</p><p><strong>Số điện thoại:</strong> {2}</p><p><strong>Email phụ huynh:</strong> {3}</p><p><strong>Thông tin góp ý:</strong></p><p>{4}</p></body>"
    }
  },
  "EmailSettings": {
    "SmtpHost": "smtp.gmail.com",
    "Port": "465",
    "UseSsl": "true",
    "SenderEmail": "<EMAIL>",
    "SpporterEmail": "<EMAIL>",
    "Authenticate": {
      //"Username": "<EMAIL>",
      "Username": "<EMAIL>",
      "Password": "Gee0123!123"
    },
    "CredentialPath": "C:\\GeeO\\token.json"
  },
  "AzureAdElAccount": {
    "applicationId": "5a7f7fbe-38fe-4c73-88fd-d1b6ee3ada73",
    "applicationSecret": "**********************************",
    "tenantId": "el.gee-o.edu.vn",
    "redirectUri": "https://localhost:8080",
    "domain": "el.gee-o.edu.vn"
  },
  "Import": {
    "Location": "C:\\GeeO\\Import",
    "Done": "Done",
    "Overall": "LessonPlans",
    "LessonPlan": "Units",
    "Attendance": "Attendance"
  },
  "SSIS": {
    "TargetServerName": "localhost",
    "FolderName": "GeeO",
    "ProjectName": "Gee-O-Import",
    "Packages": {
      "Overall": "1.LessonPlanImport.dtsx",
      "LessonPlan": "2.LessonPlanUnitImport.dtsx",
      "Material": "6.MaterialImport.dtsx",
      "Attendance": "Attendance.dtsx"
    }
  },
  "LessonContent": {
    "Store": "C:\\GeeO\\LessonContent"
  },
  "LessonPlan": {
    "Store": "C:\\GeeO\\LessonPlan"
  },
  "GeeOStory": {
    "Store": "C:\\GeeO\\GeeOStory"
  },
  "Chat": {
    "Store": "C:\\GeeO\\Chat"
  },
  "UserInfo": {
    "Store": "C:\\GeeO\\Assets\\UserInfo\\{userId}"
  },
  "Material": {
    "Store": "C:\\GeeO\\Materials",
    "HtmlContent": {
      "DefaultIndexFile": "index.html"
    }
  },
  "Media": {
    "Store": "C:\\GeeO\\Media"
  },
  "Holidays": [
    "2019-01-01",
    "2019-02-04",
    "2019-02-05",
    "2019-02-06",
    "2019-02-07",
    "2019-02-08",
    "2019-02-09",
    "2019-02-10",
    "2019-04-15",
    "2019-04-30",
    "2019-05-01",
    "2019-09-02",
    "2019-09-10",
    "2020-01-01",
    "2020-01-17",
    "2020-01-18",
    "2020-01-19",
    "2020-01-20",
    "2020-01-21",
    "2020-01-22",
    "2020-01-23",
    "2020-01-24",
    "2020-01-25",
    "2020-01-26",
    "2020-01-27",
    "2020-01-28",
    "2020-01-29",
    "2020-01-30",
    "2020-01-31",
    "2020-02-01",
    "2020-02-02",
    "2020-02-03",
    "2020-04-30",
    "2020-05-01",
    "2020-09-02"
  ]
}
