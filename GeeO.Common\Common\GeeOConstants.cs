using System.Collections.Generic;

namespace GeeO.Common
{
    public static class GeeOConstants
    {
        public static string ROLE__ADMIN = "Admin";
        public static string ROLE__PLANNER = "Planner";
        public static string ROLE__ACAD_MANAGER = "Acad Manager";
        public static string ROLE__TEACHER = "Teacher";
        public static string ROLE__SALE_MANAGER = "Sale Manager";
        public static string ROLE__SALE_EXECUTIVE = "Sale Executive";
        public static string ROLE__BRANCH_ADMIN = "Branch Admin";

        public static string[] TeacherRoles = new string[]
            {
                ROLE__TEACHER.ToUpper(),
                ROLE__ACAD_MANAGER.ToUpper()
            };

        public static string[] AdminRoles = new string[]
           {
                ROLE__ADMIN.ToUpper(),
                ROLE__BRANCH_ADMIN.ToUpper(),
           };

        public static string[] SaleRoles = new string[]
            {
                ROLE__SALE_EXECUTIVE.ToUpper(),
                ROLE__SALE_MANAGER.ToUpper()
            };

        public static Dictionary<ActivityActionType, string> ActionTitles = new()
        {
            { ActivityActionType.Imported, "Import" },
            { ActivityActionType.Created, "Tạo mới" },
            { ActivityActionType.Updated, "Cập nhật" },
            { ActivityActionType.Deleted, "Xóa" },
            { ActivityActionType.Archived, "Archived" },
            { ActivityActionType.Arranged, "Arranged" },
            { ActivityActionType.Started, "Started" },
            { ActivityActionType.Exported, "Exported" },
            { ActivityActionType.Performed, "Performed" },
            { ActivityActionType.Suspended, "Suspended" },
            { ActivityActionType.Terminated, "Terminated" },
            { ActivityActionType.Launched, "Launched" },
            { ActivityActionType.Copied, "Copied" },
            { ActivityActionType.Approved, "Approved" }
        };
    }
}
