﻿using GeeO.Common;
using GeeO.Controllers;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Data.Dto.Student.External;
using GeeO.Extensions;
using GeeO.GridVo;
using GeeO.Model.Student.Response;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.External.Controllers
{
    [Route("api/[controller]/external")]
    [ApiController]
    //[Authorize]
    public class StudentController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IStudentPaymentService _studentPaymentService;
        private readonly ILogger _logger;

        public StudentController(GeeODbContext context,
            UserManager<ApplicationUser> userManager,
            IAcadManageService acadManageService,
            IAzureAdService azureAdService,
            IStudentPaymentService studentPaymentService,
            ILogger<StudentCoursesController> logger
            )
        {
            _context = context;
            _studentPaymentService = studentPaymentService;
            _logger = logger;
        }

        // GET: api/Student
        [HttpGet("list")]
        public async Task<ActionResult<IEnumerable<StudentRemainSessionReport>>> GetStudent()
        {
            var query = from s in _context.Student
                        from sp in _context.StudentPayments.Where(x => x.StudentId == s.Id)
                        select new StudentRemainSessionReport
                        {
                            Id = s.Id,
                            StudentName = s.StudentName,
                            EnglishName = s.EnglishName,
                            StudentType = (sp.StudentType == ClassType.Regular || sp.StudentType == ClassType.Demo || sp.StudentType == ClassType.CatchUp)
                 ? EnumExtensionMethods.GetDescription(ClassType.Regular)
                 : EnumExtensionMethods.GetDescription(sp.StudentType),
                            RemainSessions = sp.NumberOfSessions - sp.StudiedSessions
                        };

            return await query.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("{id}/log/list")]
        public async Task<IActionResult> GetStudentLogById(string id)
        {
            try
            {
                var students = await _studentPaymentService.GetStudentLogById(id);
                var studentsResponse = students.Select(s => new StudentLogListResponse
                {
                    Id = s.Id,
                    StudentLogDataId = s.StudentLogDataId,
                    Date = DateTimeUtils.FormatDate(s.Date),
                    Type = s.Type,
                }).ToList();

                return Ok(new { Status = StatusCodes.Status200OK, Data = studentsResponse });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{id}/payment")]
        public async Task<IActionResult> GetPaymentById(string id)
        {
            try
            {
                var students = await _studentPaymentService.GetStudentPymentById(id);
                var studentResponse = students.Select(st => new StudentPaymentListResponse
                {
                    Id = st.Id,
                    Name = st.Name,
                    StartDate = DateTimeUtils.FormatDate(st.StartDate),
                    Amount = st.Amount,
                    NumberSession = st.NumberSession,
                }).ToList();
                
                return Ok(new { Status = StatusCodes.Status200OK, Data = studentResponse });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        // GET: api/Student/5
        [HttpGet("{id}/payments")]
        public async Task<ActionResult<List<StudentPaymentList>>> GetPayments(string id)
        {
            try
            {
                var studentPayments = await _studentPaymentService.GetStudentPayment(id);
                return studentPayments;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetPayments with id: {id} has error: {ex.Message} - {ex.StackTrace}");
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}/lesson-log")]
        public async Task<ActionResult<List<StudentLogReport>>> GetLessonLogReport(string id)
        {
            try
            {
                var query = from s in _context.Student.Where(s => s.Id == id)
                            from sll in _context.StudentLessonLogData.Where(x => x.StudentInfoId == s.Id)
                            from tll in _context.TeacherLessonLog.Where(t => t.Id == sll.LogId)
                            from cl in _context.ClassLesson.Include(x => x.Lesson).Where(l => l.Id == tll.ClassLessonId)
                            from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                            from b in _context.Campus.Where(x => x.Id == cls.CampusId)

                            select new StudentLogReport()
                            {
                                StudentName = s.StudentName,
                                Class = cls.Name,
                                Branch = b.Address,
                                Lesson = cl.Lesson.Lesson,
                                LessonDate = cl.StartTime.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                StudentAttendance = sll.Present == 0 ? "Vắng mặt" : sll.Present == 1 ? "Có mặt" : "Không điểm danh",
                            };
                return await query.OrderBy(x => x.Class).ThenBy(x => x.Lesson).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetLessonLogReport with id: {id} has error: {ex.Message} - {ex.StackTrace}");
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}/fees")]
        public async Task<ActionResult<List<StudentPaymentReport>>> GetFees(string id)
        {
            try
            {
                var query = from s in _context.Student.Where(s => s.Id == id)
                            from sll in _context.StudentLessonLogData.Where(x => x.StudentInfoId == s.Id && (x.Present == (int)AttendanceType.Present || x.Present == (int)AttendanceType.Absent))
                            from sc in _context.StudentCourse.Where(x => x.StudentId == s.Id)
                            from tll in _context.TeacherLessonLog.Where(t => t.Id == sll.LogId)
                            from cl in _context.ClassLesson.Include(x => x.Lesson).Where(l => l.Id == tll.ClassLessonId)
                            from cls in _context.ClassCourse.Where(cls => cl.ClassId == cls.Id)
                            from b in _context.Campus.Where(x => x.Id == cls.CampusId)

                            select new StudentPaymentReport()
                            {
                                PaymentName = sc.Name,
                                Amount = sc.Amount,
                                NumberOfSession = sc.NumberOfSession,
                                PaymentDate = sc.StartDate,
                                StudentName = s.StudentName,
                                Fee = sll.Fee,
                                Class = cls.Name,
                                Lesson = cl.Lesson.Lesson,
                                LessonDate = cl.StartTime.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                StudentAttendance = sll.Present == 0 ? "Vắng mặt" : sll.Present == 1 ? "Có mặt" : "Không điểm danh",
                            };
                return await query.OrderBy(x => x.Class).ThenBy(x => x.Lesson).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetFees with id: {id} has error: {ex.Message} - {ex.StackTrace}");
                return BadRequest(ex.Message);
            }
        }
    }
}
