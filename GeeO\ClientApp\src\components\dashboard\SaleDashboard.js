import React, { Component } from 'react';

import { AdminPage } from '../ui/page/AdminPage';
import DemoStudents from './DemoStudents';
import { Grid } from '@material-ui/core';
import Notifications from './Notifications';

export class SaleDashboard extends Component {
  constructor(...args) {
    super(...args);
    this.state = {
      displayComponent: <div>Sale Dashboard</div>
    };
  }

  renderContent() {
    const { calendarComponent } = this.props?.parentComponent?.state;

    return (
      <Grid container alignItems="flex-start" spacing={2}>
        <Grid item xs={8}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              {calendarComponent}
            </Grid>
            <Grid item xs={12}>
              <DemoStudents />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={4}>
          <Notifications />
        </Grid>
      </Grid>
    );
  }

  render() {
    const displayContent = this.renderContent();
    return <AdminPage title="Sale Dashboard" content={displayContent} />;
  }
}
