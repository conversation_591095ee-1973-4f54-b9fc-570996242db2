﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace GeeO.Common.FileLogging
{
    public static class FileLoggerExtensions
    {
        public static ILoggerFactory AddFile(this ILoggerFactory factory, string filePath, string logLevel)
        {
            factory.AddProvider(new FileLoggerProvider(filePath, logLevel));
            return factory;
        }
    }
}
