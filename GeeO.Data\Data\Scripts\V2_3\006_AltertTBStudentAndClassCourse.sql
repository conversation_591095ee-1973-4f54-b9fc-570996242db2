﻿GO
--DROP TABLE IF EXISTS Student;
EXEC sp_rename 'ClassD<PERSON>', 'Student'
GO
ALTER TABLE [dbo].[Student] DROP COLUMN FatherName;
ALTER TABLE [dbo].[Student] DROP COLUMN MotherName;
IF COL_LENGTH('Student','SortOrder') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN SortOrder;
END;
IF COL_LENGTH('Student','FatherN<PERSON>') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN FatherName;
END;
IF COL_LENGTH('Student','MotherName') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN MotherName;
END;
IF COL_LENGTH('Student','FatherPhone') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN FatherPhone;
END;
IF COL_LENGTH('Student','MotherP<PERSON>') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN MotherPhone;
END;
IF COL_LENGTH('Student','FatherEmail') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN FatherEmail;
END;
IF COL_LENGTH('Student','MotherEmail') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[Student] DROP COLUMN MotherEmail;
END;
-------------------- DROP COLUMN  CLASS COURSE
GO
IF COL_LENGTH('ClassCourse','Monday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Monda__0D7A0286
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Monday;
END;
IF COL_LENGTH('ClassCourse','Tuesday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Tuesd__0E6E26BF
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Tuesday;
END;
IF COL_LENGTH('ClassCourse','Wednesday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Wedne__0F624AF8
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Wednesday;
END;
IF COL_LENGTH('ClassCourse','Thursday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Thurs__10566F31
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Thursday;
END;
IF COL_LENGTH('ClassCourse','Friday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Frida__114A936A
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Friday;
END;
IF COL_LENGTH('ClassCourse','Saturday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Satur__123EB7A3
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Saturday;
END;
IF COL_LENGTH('ClassCourse','Sunday') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP CONSTRAINT DF__ClassCour__Sunda__1332DBDC
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN Sunday;
END;
IF COL_LENGTH('ClassCourse','StartTime') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN StartTime;
END;
IF COL_LENGTH('ClassCourse','EndTime') IS NOT NULL
BEGIN
	ALTER TABLE [dbo].[ClassCourse] DROP COLUMN EndTime;
END;
ALTER TABLE dbo.ClassCourse ADD NumberOfPupils [int] NULL