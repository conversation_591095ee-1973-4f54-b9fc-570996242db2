using GeeO.Api.Hubs.Clients;
using GeeO.Api.Models;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Api.Hubs
{
    public class ChatHub : Hub<IChatClient>
    {
        public static readonly ConcurrentDictionary<string, string> OnlineClientsDict = new();

        public async Task<string> LoginAsync(string userId)
        {
            _ = OnlineClientsDict.TryRemove(userId, out _);
            _ = OnlineClientsDict.TryAdd(userId, Context.ConnectionId);

            return await Task.FromResult(Context.ConnectionId);
        }

        public override Task OnDisconnectedAsync(Exception exception)
        {
            string offUserId = OnlineClientsDict.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
            if (offUserId != null)
            {
                _ = OnlineClientsDict.TryRemove(offUserId, out _);
            }

            return base.OnDisconnectedAsync(exception);
        }
    }
}