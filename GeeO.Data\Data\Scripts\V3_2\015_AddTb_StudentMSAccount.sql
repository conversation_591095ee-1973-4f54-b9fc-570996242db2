USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentExternalAccount]    Script Date: 10-Jun-19 10:43:16 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentExternalAccount](
	[Id] [nvarchar](450) NOT NULL,
	[StudentId] [nvarchar](450) NOT NULL,
	AuthenticationScheme [nvarchar](200) NOT NULL,
	Email [nvarchar](300) NOT NULL,
	CreatedDate datetime2 NULL,
	CreatedBy nvarchar(450) NULL,
	ModifiedDate datetime2 NULL,
	ModifiedBy nvarchar(450) NULL
 CONSTRAINT [PK_StudentExternalAccount] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentExternalAccount]  WITH CHECK ADD  CONSTRAINT [FK_StudentExternalAccount_StudentId] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO
ALTER TABLE [dbo].[StudentExternalAccount] CHECK CONSTRAINT [FK_StudentExternalAccount_StudentId]
GO

