-- ================================================
-- Template generated from Template Explorer using:
-- Create Procedure (New Menu).SQL
--
-- Use the Specify Values for Template Parameters 
-- command (Ctrl-Shift-M) to fill in the parameter 
-- values below.
--
-- This block of comments will not be included in
-- the definition of the procedure.
-- ================================================
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		Lan <PERSON>
-- Create date: 10-May-2020
-- Description:	ResetOrderLessonPlan
-- =============================================
CREATE PROCEDURE ResetOrderLessonPlan 
	@LevelId nvarchar(450)
AS
BEGIN
	DECLARE @DateNow datetime = GETDATE()

	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    UPDATE LessonPlan
	SET CreatedDate = DATEADD(MINUTE, CONVERT(int, Lesson), @DateNow)
	WHERE LevelId = @LevelId
END
GO
