import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { ListMySchedule } from './ListMySchedule';
import { MyScheduleActions } from './MyScheduleConstans';
import { AdminPage } from '../ui/page/AdminPage';

const styles = theme => ({
  fab: {
    margin: theme.spacing(0, 1, 1)
  }
});

class MyScheduleComp extends Component {
  static displayName = MyScheduleComp.name;
  constructor(...args) {
    super(...args);
    this.child = React.createRef();
    this.state = {
      action: this.props.action,
      content: null,
      pageTitle: '',
      redirect: null
    };
    switch (this.props.action) {
      case MyScheduleActions.List:
        this.state.pageTitle = 'My Schedule';
        this.state.content = <ListMySchedule />;
        break;
      case undefined:
      default:
    }
  }

  render() {
    return (
      <Fragment>
        <AdminPage
          title={this.state.pageTitle}
          content={this.state.content}
          actions={null}
        />
        {this.state.redirect}
      </Fragment>
    );
  }
}

MyScheduleComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const MySchedule = withStyles(styles)(MyScheduleComp);
