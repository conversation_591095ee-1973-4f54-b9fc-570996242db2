using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using GeeO.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;

namespace GeeO.Controllers
{
    [Route("api/media")]
    [ApiController]
    public class FileController : ControllerBase
    {
        private readonly IConfiguration _config;
        public FileController(IConfiguration config)
        {
            _config = config;
        }

        [HttpPost(nameof(Upload))]
        public async Task<IActionResult> Upload([FromForm] FileUploadDto uploadFiles)
        {
            try
            {
                if (uploadFiles.Files.Count == 0)
                {
                    return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = "No file uploaded" });
                }
                var userFolderPath = _config[GeeOConfigKeys.Media];

                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(uploadFiles.Files[0].FileName)}";
                var filePath = Path.Combine(userFolderPath, fileName);
                if (!Directory.Exists(userFolderPath))
                {
                    Directory.CreateDirectory(userFolderPath);
                }

                var fileResponses = new List<FileInfoResponseDto>();
                foreach (var file in uploadFiles.Files)
                {
                    var uniqueFileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                    var uniqueFilePath = Path.Combine(userFolderPath, uniqueFileName);

                    using (var stream = new FileStream(uniqueFilePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }
                    var fileUrl = $"{_config["IssuerUri"]}/api/media/{uniqueFileName}";
                    fileResponses.Add(new FileInfoResponseDto(uniqueFileName, fileUrl));
                }

                return Ok(fileResponses);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        [HttpGet("{fileName}")]
        public IActionResult GetFile(string userId, string fileName)
        {
            try
            {
                var userFolderPath = _config[GeeOConfigKeys.Media];
                var filePath = Path.Combine(userFolderPath, fileName);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound(new { Status = StatusCodes.Status404NotFound, ErrorMessage = "File not found" });
                }

                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

                var provider = new FileExtensionContentTypeProvider();
                if (!provider.TryGetContentType(fileName, out var contentType))
                {
                    contentType = "application/octet-stream";
                }

                return File(fileStream, contentType, fileName);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }
    }
}