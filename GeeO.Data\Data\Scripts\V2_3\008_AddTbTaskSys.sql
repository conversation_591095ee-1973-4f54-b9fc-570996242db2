﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[TaskSys]    Script Date: 9/29/2019 7:04:19 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TaskSys](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](450) NULL,
	[Note] [nvarchar](max) NULL,
	[Status] [int] NOT NULL,
	[CreateDate] [datetime] NULL,
	[TaskSysParentId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NOT NULL,
	[AssignUserId] [nvarchar](450) NULL,
 CONSTRAINT [PK_TaskSys] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[TaskSys]  WITH CHECK ADD  CONSTRAINT [FK_TaskSys_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[TaskSys] CHECK CONSTRAINT [FK_TaskSys_AspNetUsers]
GO

ALTER TABLE [dbo].[TaskSys]  WITH CHECK ADD  CONSTRAINT [FK_TaskSys_AssignUser] FOREIGN KEY([AssignUserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON UPDATE SET NULL
ON DELETE SET NULL
GO

ALTER TABLE [dbo].[TaskSys] CHECK CONSTRAINT [FK_TaskSys_AssignUser]
GO


