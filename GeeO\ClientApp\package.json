{"name": "gee-o", "version": "2.0.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@date-io/core": "^1.3.13", "@date-io/date-fns": "^1.3.11", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-brands-svg-icons": "^5.15.1", "@fortawesome/free-regular-svg-icons": "^5.15.1", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/react-fontawesome": "^0.1.12", "@iconify/react": "^5.0.2", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.5.1", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "^3.2.7", "@material-ui/styles": "^4.10.0", "@mdi/font": "^5.8.55", "@mdi/js": "^5.8.55", "@mdi/react": "^1.4.0", "@microsoft/signalr": "^6.0.2", "@mui/material": "^5.16.7", "@popperjs/core": "^2.5.4", "apexcharts": "^3.28.3", "bootstrap": "^4.5.3", "chroma-js": "^2.1.2", "clsx": "^1.1.1", "core-js": "^3.6.5", "date-fns": "^2.16.1", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "husky": "^1.3.1", "jquery": "^3.5.1", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "lint-staged": "^8.2.1", "material-table": "^1.69.1", "merge": "^1.2.1", "moment": "^2.29.1", "notistack": "^3.0.1", "oidc-client": "^1.9.1", "popper.js": "^1.16.1", "prettier": "^1.18.2", "prop-types": "latest", "react": "^16.14.0", "react-apexcharts": "^1.3.9", "react-beautiful-dnd": "^12.1.1", "react-big-calendar": "^0.23.0", "react-charts": "^2.0.0-beta.7", "react-cookies": "^0.1.1", "react-dom": "^16.14.0", "react-full-screen": "^1.1.0", "react-number-format": "^4.4.4", "react-quill": "^2.0.0", "react-responsive-pinch-zoom-pan": "^0.2.2", "react-router-bootstrap": "^0.24.4", "react-router-dom": "^5.2.0", "react-scripts": "^3.4.4", "react-sortable-hoc": "^1.10.1", "reactstrap": "^7.1.0", "reaviz": "^10.4.4", "rimraf": "^2.7.1", "screenfull": "^6.0.1", "semantic-ui-css": "^2.4.1", "semantic-ui-react": "^0.88.2", "simple-peer": "^9.11.1", "socket.io-client": "^2.3.0", "styled-components": "^5.1.0", "swiper": "^6.3.5", "typescript": "^5.5.4"}, "devDependencies": {"cross-env": "^7.0.3", "sass": "^1.60.0"}, "eslintConfig": {"extends": "react-app"}, "lint-staged": {"src/**/*.{js,jsx,json,css}": ["prettier --single-quote --write", "git add"]}, "scripts": {"precommit": "lint-staged", "start": "rimraf ./build && react-scripts --openssl-legacy-provider start", "build": "set NODE_OPTIONS=--openssl-legacy-provider && node --max_old_space_size=4096 node_modules/react-scripts/scripts/build.js", "build:staging": "cross-env NODE_ENV=staging react-scripts --openssl-legacy-provider build", "test": "cross-env CI=true react-scripts test --env=jsdom", "eject": "react-scripts eject", "lint": "eslint ./src/"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}