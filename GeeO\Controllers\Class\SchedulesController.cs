﻿using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SchedulesController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;
        private readonly IScheduleService _scheduleService;

        public SchedulesController(GeeODbContext context, IAcadManageService acadManageService, IScheduleService scheduleService)
        {
            _context = context;
            _acadManageService = acadManageService;
            _scheduleService = scheduleService;
        }

        // GET: api/Schedules
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Schedule>>> GetSchedule()
        {
            return await GetScheduleList("0", 1);
        }

        // GET: api/Schedules/1
        [HttpGet("[action]/{userId}/{withEndedClass}")]
        public async Task<ActionResult<IEnumerable<Schedule>>> GetScheduleList(string userId, int withEndedClass)
        {
            try
            {
                List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);
                var result = _context.Schedule
                                .Where(sch => (withEndedClass == 1 || sch.EndDate.Date >= DateTime.Today) &&
                                              (assignedCampus == null || assignedCampus.Contains(sch.ClassCourses.CampusId)))
                                .Include(x => x.ClassRoom)
                                    .ThenInclude(x => x.Campus)
                                .Include(x => x.ClassCourses)
                                .OrderByDescending(x => x.StartDate);
                return await result.ToListAsync().ConfigureAwait(false);
            }
            catch(Exception ex)
            {
                var exMessage = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                return BadRequest(exMessage);
            }
        }

        // GET: api/Schedules/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Schedule>> GetSchedule(string id)
        {
            var schedule = await _context.Schedule.Include(x => x.ClassCourses).ThenInclude(x => x.Level).FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);

            if (schedule == null)
            {
                return NotFound();
            }
            return schedule;
        }

        // PUT: api/Schedules/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPut("{id}")]
        public async Task<IActionResult> PutSchedule(string id, Schedule schedule)
        {
            var scheduleCurrent = await _context.Schedule.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
            if (await _context.Schedule.Where(x => x.ClassCourseId == schedule.ClassCourseId).AsNoTracking().FirstOrDefaultAsync().ConfigureAwait(false) != null && scheduleCurrent.ClassCourseId != schedule.ClassCourseId)
            {
                return BadRequest("Duplicate class course in schedule");
            }

            if (id != schedule.Id)
            {
                return BadRequest();
            }

            if (!string.IsNullOrEmpty(schedule.ClassCourseId) && schedule.StartDate < schedule.EndDate)
            {
                List<ClassLesson> lstClassLessons = _context.ClassLesson.Where(x => x.ClassId == schedule.ClassCourseId)
                    .OrderBy(x => x.Lesson.CreatedDate).ToList();

                // set null when update
                foreach (var item in lstClassLessons)
                {
                    item.StartTime = null;
                    item.EndTime = null;
                    _context.Entry(item).State = EntityState.Modified;
                }
                try
                {
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ScheduleExists(id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }

                int studentTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                DateTime tempDate = schedule.StartDate;
                int j = 0;

                var holidays = await _acadManageService.GetHolidays(schedule.ClassCourseId).ConfigureAwait(false);
                List<DayOfWeek> lstDayOfWeeks = await _scheduleService.ScheduleDayOfWeeks(schedule);
                for (int i = 0; i < studentTime; i++)
                {
                    var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                    bool matchedDate = false;
                    while (!matchedDate)
                    {
                        matchedDate = !holidays.Contains(tempDate.Date) && lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (matchedDate && lstClassLessons.Count > j)
                        {
                            lstClassLessons[j].StartTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.StartTime.Hour, schedule.StartTime.Minute, 0);
                            lstClassLessons[j].EndTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.EndTime.Hour, schedule.EndTime.Minute, 0);
                            _context.Entry(lstClassLessons[j]).State = EntityState.Modified;
                            j++;
                        }
                        tempDate = tempDate.AddDays(1);
                    }

                }
            }
            _context.Entry(schedule).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ScheduleExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Schedules
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for
        // more details see https://aka.ms/RazorPagesCRUD.
        [HttpPost]
        public async Task<ActionResult<Schedule>> PostSchedule(Schedule schedule)
        {
            if (await _context.Schedule.FirstOrDefaultAsync(x => x.ClassCourseId == schedule.ClassCourseId).ConfigureAwait(false) != null)
            {
                return BadRequest("Duplicate class course in schedule");
            }

            if (string.IsNullOrEmpty(schedule.ClassRoomId))
            {
                schedule.ClassRoomId = null;
            }

            _context.Schedule.Add(schedule);

            if (!string.IsNullOrEmpty(schedule.ClassCourseId) && schedule.StartDate < schedule.EndDate)
            {
                List<ClassLesson> lstClassLessons = _context.ClassLesson.Where(x => x.ClassId == schedule.ClassCourseId)
                    .OrderBy(x => x.Lesson.CreatedDate).ToList();

                // set null when update
                foreach (var item in lstClassLessons)
                {
                    item.StartTime = null;
                    item.EndTime = null;
                    _context.Entry(item).State = EntityState.Modified;
                }
                try
                {
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                catch (DbUpdateConcurrencyException)
                {
                    throw;
                }

                int studentTime = schedule.EndDate.Subtract(schedule.StartDate).Days;
                DateTime tempDate = schedule.StartDate;
                int j = 0;

                var holidays = await _acadManageService.GetHolidays(schedule.ClassCourseId).ConfigureAwait(false);
                List<DayOfWeek> lstDayOfWeeks = await _scheduleService.ScheduleDayOfWeeks(schedule);
                for (int i = 0; i < studentTime; i++)
                {
                    var checkDayOfWeeks = lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                    bool matchedDate = false;
                    while (!matchedDate)
                    {
                        matchedDate = !holidays.Contains(tempDate.Date) && lstDayOfWeeks.Contains(tempDate.DayOfWeek);
                        if (matchedDate && lstClassLessons.Count > j)
                        {
                            lstClassLessons[j].StartTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.StartTime.Hour, schedule.StartTime.Minute, 0);
                            lstClassLessons[j].EndTime = new DateTime(tempDate.Year, tempDate.Month, tempDate.Day, schedule.EndTime.Hour, schedule.EndTime.Minute, 0);
                            _context.Entry(lstClassLessons[j]).State = EntityState.Modified;
                            j++;
                        }
                        tempDate = tempDate.AddDays(1);
                    }
                }
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetSchedule", new { id = schedule.Id }, schedule);
        }

        // DELETE: api/Schedules/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<Schedule>> DeleteSchedule(string id)
        {
            var schedule = await _context.Schedule.FindAsync(id);
            if (schedule == null)
            {
                return NotFound();
            }

            _context.Schedule.Remove(schedule);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return schedule;
        }

        private bool ScheduleExists(string id)
        {
            return _context.Schedule.Any(e => e.Id == id);
        }
        [HttpGet("[action]/{id}/{classCourseId}")]
        public async Task<IActionResult> UpdateScheduleInClassCourse(string id, string classCourseId)
        {
            Schedule schedule = await _context.Schedule.Include(x => x.ClassCourses).ThenInclude(x => x.ClassLessons).FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
            if (schedule == null)
            {
                return NotFound();
            }
            if (!string.IsNullOrEmpty(classCourseId) && schedule.StartDate < schedule.EndDate)
            {
                var checkSchedule = await _context.Schedule.Where(x => x.ClassCourseId == classCourseId).FirstOrDefaultAsync().ConfigureAwait(false);
                if (checkSchedule != null)
                {
                    return Conflict();
                }
                schedule.ClassCourseId = classCourseId;
                _context.Entry(schedule).State = EntityState.Modified;
                try
                {
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                catch (DbUpdateConcurrencyException)
                {
                    throw;
                }
            }
            return NoContent();
        }
        [HttpGet("[action]/{id}/{classRoomId}")]
        public async Task<IActionResult> UpdateScheduleInClassRoom(string id, string classRoomId)
        {
            Schedule schedule = _context.Schedule.Find(id);
            if (schedule == null)
            {
                return NotFound();
            }

            if (!string.IsNullOrEmpty(classRoomId))
            {
                schedule.ClassRoomId = classRoomId;
                _context.Entry(schedule).State = EntityState.Modified;
                try
                {
                    await _context.SaveChangesAsync().ConfigureAwait(false);
                }
                catch (DbUpdateConcurrencyException)
                {
                    throw;
                }
            }
            return NoContent();
        }

        [HttpGet("[action]/{id}/{classId}")]
        public async Task<ActionResult<IEnumerable<MyScheduleGrid>>> GetMySchedule(string id, string classId)
        {
            var data = await (from cl in _context.ClassLesson
                              join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                              join ct in _context.ClassTeacher on cc.Id equals ct.ClassId
                              join s in _context.Schedule on cc.Id equals s.ClassCourseId
                              join cr in _context.ClassRoom on s.ClassRoomId equals cr.Id into ljCR
                              from _cr in ljCR.DefaultIfEmpty()
                              join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                              where ct.TeacherId == id && cl.StartTime != null && cl.EndTime != null && cc.Id == classId
                              orderby cl.StartTime
                              select new MyScheduleGrid()
                              {
                                  Id = cl.Id,
                                  SubId = cl.Id,
                                  ClassId = cc.Id,
                                  ClassCourse = cc.Name,
                                  ClassRoom = _cr.Name,
                                  StartTime = s.StartTime.ToString("HH:mm"),
                                  EndTime = s.EndTime.ToString("HH:mm"),
                                  StartTimeNoFormat = cl.StartTime,
                                  Lesson = lp.Lesson
                              }).ToListAsync().ConfigureAwait(false);
            return data;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<CalendarGrid>>> GetDataCalendarBySchedule(string id)
        {
            var data = await (from cl in _context.ClassLesson
                              join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                              join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                              join s in _context.Schedule on cc.Id equals s.ClassCourseId
                              where cl.StartTime != null && cl.EndTime != null && s.Id == id && (!lp.IsDeleted || cl.TeacherLessonLogs.Count() > 0)
                              select new CalendarGrid()
                              {
                                  Id = cl.Id,
                                  Title = $"{cc.Name} - Lesson {lp.Lesson}" + (!string.IsNullOrEmpty(lp.Subject) ? $" - {lp.Subject}" : ""),
                                  StartTime = cl.StartTime.GetValueOrDefault(),
                                  EndTime = cl.EndTime.GetValueOrDefault(),
                                  CampusId = s.ClassRoom != null ? s.ClassRoom.CampusId : ""
                              }).ToListAsync().ConfigureAwait(false);
            return data;
        }

        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<CalendarGrid>>> GetAllDataCalendar()
        {
            var data = from cl in _context.ClassLesson.Where(cl => cl.StartTime != null && cl.EndTime != null)
                       from cc in _context.ClassCourse.Where(cc => cl.ClassId == cc.Id)
                       from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                       from cps in _context.Campus.Where(cps => cc.CampusId == cps.Id).DefaultIfEmpty()
                       select new CalendarGrid()
                       {
                           Id = cl.Id,
                           Title = $"{cc.Name} - Lesson {lp.Lesson}" + (!string.IsNullOrEmpty(lp.Subject) ? $" - {lp.Subject}": ""),
                           StartTime = cl.StartTime.GetValueOrDefault(),
                           EndTime = cl.EndTime.GetValueOrDefault(),
                           CampusId = cps != null ? cps.Id : string.Empty
                       };
            return await data.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<CalendarGrid>>> GetAllDataCalendarByUserId(string id)
        {
            var data = from cl in _context.ClassLesson.Where(cl => cl.StartTime != null && cl.EndTime != null)
                       from cc in _context.ClassCourse.Where(cc => cl.ClassId == cc.Id)
                       from ct in _context.ClassTeacher.Where(ct => cc.Id == ct.ClassId && ct.TeacherId == id)
                       from lp in _context.LessonPlan.Where(lp => cl.LessonId == lp.Id)
                       from cps in _context.Campus.Where(cps => cc.CampusId == cps.Id).DefaultIfEmpty()
                       select new CalendarGrid()
                       {
                           Id = cl.Id,
                           Title = $"{cc.Name} - Lesson {lp.Lesson}" + (!string.IsNullOrEmpty(lp.Subject) ? $" - {lp.Subject}" : ""),
                           StartTime = cl.StartTime.GetValueOrDefault(),
                           EndTime = cl.EndTime.GetValueOrDefault(),
                           CampusId = cps != null ? cps.Id : string.Empty
                       };

            return this.AppendCalendarCatchUp(await data.ToListAsync().ConfigureAwait(false));
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<CalendarGrid>>> GetDataCalendarByClass(string id)
        {
            var data = await (from cl in _context.ClassLesson
                              join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                              join lp in _context.LessonPlan on cl.LessonId equals lp.Id
                              join s in _context.Schedule on cc.Id equals s.ClassCourseId
                              where cl.StartTime != null && cl.EndTime != null && cc.Id == id
                              select new CalendarGrid()
                              {
                                  Id = cl.Id,
                                  Title = $"{cc.Name} - Lesson {lp.Lesson}" + (!string.IsNullOrEmpty(lp.Subject) ? $" - {lp.Subject}" : ""),
                                  StartTime = cl.StartTime.GetValueOrDefault(),
                                  EndTime = cl.EndTime.GetValueOrDefault(),
                                  CampusId = s.ClassRoom != null ? s.ClassRoom.CampusId : ""
                              }).ToListAsync().ConfigureAwait(false);
            return this.AppendCalendarCatchUp(data);
        }

        private List<CalendarGrid> AppendCalendarCatchUp(List<CalendarGrid> data)
        {
            var dataCatchUp = (from d in data
                               join cs in _context.CatchUpSchedules on d.Id equals cs.ClassLessonId
                               where cs.CatchUpType == 1 || cs.CatchUpType == 2
                               select new CalendarGrid()
                               {
                                   Id = d.Id + " --- " + Guid.NewGuid().ToString(),
                                   Title = d.Title + " (Catch-Up)" + "/" + d.StartTime.AddHours(-0.5).ToShortTimeString() + "-" + d.EndTime.ToShortTimeString(),
                                   StartTime = d.StartTime.AddHours(-0.5),
                                   EndTime = d.EndTime,
                                   CampusId = d.CampusId
                               }).ToList();

            data.AddRange(dataCatchUp);

            return data;
        }
        [HttpPost("[action]/{classId}")]
        public async Task<DateTime> BuildEndDateSchedule(string classId, Schedule schedule)
        {
            var holidays = await _acadManageService.GetHolidays(classId).ConfigureAwait(false);

            List<ClassLesson> lstClassLessons = _context.ClassLesson.Where(x => x.ClassId == classId)
                    .OrderBy(x => x.Lesson.CreatedDate).ToList();

            Dictionary<DayOfWeek, bool> scheduleDict = new Dictionary<DayOfWeek, bool>()
                {
                    { DayOfWeek.Monday, schedule.Monday },
                    { DayOfWeek.Tuesday, schedule.Tuesday },
                    { DayOfWeek.Wednesday, schedule.Wednesday },
                    { DayOfWeek.Thursday, schedule.Thursday },
                    { DayOfWeek.Friday, schedule.Friday },
                    { DayOfWeek.Saturday, schedule.Saturday },
                    { DayOfWeek.Sunday, schedule.Sunday }
                };
            List<DayOfWeek> lstDayOfWeeks = scheduleDict.Where(x => x.Value == true).Select(day => day.Key).ToList();
            DateTime dateRun = schedule.StartDate;

            if (lstDayOfWeeks.Count > 0 & lstClassLessons.Count > 0)
            {
                for (int i = 0; i < lstClassLessons.Count; i++)
                {
                    bool matchedDate = false;
                    while (!matchedDate)
                    {
                        matchedDate = !holidays.Contains(dateRun.Date) && lstDayOfWeeks.Contains(dateRun.DayOfWeek);
                        dateRun = dateRun.AddDays(1);
                    }
                }
            }

            return dateRun;
        }
    }
}
