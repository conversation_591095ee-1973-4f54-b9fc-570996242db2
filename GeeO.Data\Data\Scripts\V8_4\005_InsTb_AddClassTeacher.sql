﻿USE [GeeODb]
GO
/****** Object:  Trigger [dbo].[trg_AddClassTeacher]    Script Date: 12/9/2024 11:55:56 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
ALTER TRIGGER [dbo].[trg_AddClassTeacher]
   ON  [dbo].[ClassTeacher]
   AFTER INSERT
AS 
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @classId    nvarchar(450),
			@className  nvarchar(200),
			@userId    nvarchar(450),
			@startDate    nvarchar(50),
			@eventTitle nvarchar(1000);

	DECLARE @TimeToCheck datetime = SYSDATETIMEOFFSET() AT TIME ZONE 'SE Asia Standard Time';

	SELECT  @classId = cls.Id,
			@className = cls.[Name], 
			@startDate = IIF(sch.StartDate IS NULL, '', FORMAT(sch.StartDate, 'dd/MM/yyyy')),
			@userId = usr.Id
	FROM ClassCourse cls WITH(NOLOCK)
	JOIN INSERTED ct WITH(NOLOCK) ON cls.Id = ct.ClassId
	JOIN AspNetUsers usr WITH(NOLOCK) ON ct.TeacherId = usr.Id
	LEFT OUTER JOIN Schedule sch WITH(NOLOCK) ON cls.Id = sch.ClassCourseId

	SET @eventTitle = N'Bạn vừa được thêm vào lớp ' + @className + N', ngày bắt đầu ' + @startDate;

	IF NOT EXISTS (SELECT * FROM [dbo].[Notifications] WHERE [UserId] = @userId AND [Title] = @eventTitle AND CONVERT(date, [StartTime]) = CONVERT(date, GETDATE()))

		INSERT INTO [dbo].[Notifications]
				   ([Id]
				   ,[ClassId]
				   ,[UserId]
				   ,[Title]
				   ,[Content]
				   ,[StartTime]
				   ,[EndTime]
				   ,[CreatedDate]
				   ,[CreatedBy]
				   ,[Activity]
					,[Subject])
			 VALUES
				   (LOWER(CONVERT(nvarchar(450), NEWID()))
				   ,@classId
				   ,@userId
				   ,@eventTitle
				   ,N'Thông báo Teacher được thêm vào lớp.'
				   ,@TimeToCheck
				   ,DATEADD(mi, 5, @TimeToCheck)
				   ,@TimeToCheck
				   ,'sysadmin'
				   ,'Add new class'
					,'Teacher');

END