﻿
-- Drop reference to ClassCourse
DROP TABLE [dbo].[TeacherLessonUnit]
DROP TABLE [dbo].[StudentLessonLogData]
DROP TABLE [dbo].[TeacherLessonLogData]
DROP TABLE [dbo].[TeacherLessonLog]
DROP TABLE [dbo].[ClassData]
DROP TABLE [dbo].[ClassLesson]
DROP TABLE [dbo].[ClassTeacher]
GO

-- Drop table ClassCourse
DROP TABLE [dbo].[ClassCourse]
GO


/****** Object:  Table [dbo].[ClassCourse]    Script Date: 20-Oct-19 1:23:09 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClassCourse](
	[Id] [nvarchar](450) NOT NULL,
	[LevelId] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](256) NULL,
	[Description] [nvarchar](512) NULL,
	[Course] [nvarchar](512) NULL,
	[NumberOfPupils] [int] NULL,
	[LessonScheduleId] [nvarchar](450) NULL,
 CONSTRAINT [PK_ClassCourse] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClassCourse]  WITH CHECK ADD  CONSTRAINT [FK_ClassCourse_LevelId] FOREIGN KEY([LevelId])
REFERENCES [dbo].[StudyLevel] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClassCourse] CHECK CONSTRAINT [FK_ClassCourse_LevelId]
GO

-- Create dropped tables
--:r "..\\AddTbClassTeacher.sql"
--:r "..\\AddTbClassLesson.sql"
--:r "..\\AddTbTeacherLessonLog.sql"
--:r "..\\AddTbTeacherLessonLogData.sql"
--:r "..\\AddTbStudentLessonLogData.sql"
--:r "..\\AddTbTeacherLessonUnit.sql"