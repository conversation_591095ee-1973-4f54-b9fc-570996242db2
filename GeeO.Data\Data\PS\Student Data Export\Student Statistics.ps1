﻿###
### GEE-O ENGLISH script for getting student statistics
###

# read settings 
$settings = Get-Content -Raw -Path "$PSScriptRoot/config.json" | ConvertFrom-Json

# load common utilities
.$PSScriptRoot/Common.ps1


# declare sql in a variable and pass it to -Query switch
$sqlServer = $settings.database.server
$database = $settings.database.database
$outFolderRoot = $settings.outFolder


function Get-StudentAttendance
{
    $time = Get-Date -Format "yyyyMMdd-HHmmss"
    Write-RunLog "====================== Starting ======================"

	$outFolder = Join-Path $outFolderRoot "StudentStatistics"
	
	if (!(Test-Path $outFolder))
	{
		New-Item -ItemType Directory -Path $outFolder > $null
		Write-Host "$outFolder folder created successfully"
	}

	$ExcelObject = New-Object -ComObject Excel.Application  
	$ExcelObject.Visible = $false 
	$ExcelObject.DisplayAlerts =$false

    $sqlClass="
        select cls.Id as 'ClassId', cls.[Name] as 'ClassName'
	    from ClassCourse cls
	    where cls.[Name] not like '%test%' and cls.[Name] not like 'SC%'
              --and cls.Id = '9b237dc6-4f39-4bfc-b938-44166f8b8594'
    "

	$classList = Execute-DbQuery -Query $sqlClass

    foreach ($class in $classList)
    {
        $outFile = Join-Path $outFolder "$($class.ClassName).xlsx" # name of file to export
        Write-Host $outFile
		Write-Output "Class: $($class.ClassName)"
		
		# Create Excel file  
		$ActiveWorkbook = $ExcelObject.Workbooks.Add()  
		$ActiveWorksheet = $ActiveWorkbook.Worksheets.Item(1)
		$ActiveWorksheet.Columns(5).NumberFormat = "#,###"

		#$sheetName = $lesson.LessonDate.ToString("dd-MM-yyyy")
		#$ActiveWorksheet.Name = $sheetName

		$ActiveWorksheet.Cells.Item(1,1) = "StudentName"
		$ActiveWorksheet.Cells.Item(1,2) = "EnglishName"
		$ActiveWorksheet.Cells.Item(1,3) = "TotalNumberOfSessions"
		$ActiveWorksheet.Cells.Item(1,4) = "RemainNumberOfSessions"
		$ActiveWorksheet.Cells.Item(1,5) = "TotalPayments"
			
		$sqlStudents="
			with res as
            (
            select std.StudentName
	              ,std.EnglishName
	              ,(select SUM(NumberOfSession) from StudentCourse sc where sc.StudentId = std.Id) as 'TotalNumberOfSessions'
	              ,(select COUNT(*) from StudentLessonLogData sll
	                join TeacherLessonLog tll on tll.Id = sll.LogId and tll.HistoryLog = 0
		            where sll.StudentInfoId = std.Id) as 'StudiedNumberOfSessions'
	              ,(select SUM(Amount) from StudentCourse sc where sc.StudentId = std.Id) as 'TotalPayments'
            from ClassCourse cls
            join ClassStudent cs on cs.ClassId = cls.Id and cs.StudentType = 1
            join Student std on std.Id = cs.StudentId and std.SuspendDate is null and std.TerminateDate is null
            where cls.Id = '$($class.ClassId)'
            ) 

            select StudentName
	              ,EnglishName
	              ,TotalNumberOfSessions
	              ,StudiedNumberOfSessions
	              ,[TotalNumberOfSessions] - [StudiedNumberOfSessions] as 'RemainNumberOfSessions'
	              ,TotalPayments
            from res
            order by StudentName
		"

        $studentStatistics = Execute-DbQuery -Query $sqlStudents
            
		$rowIndex = 2

        foreach ($student in $studentStatistics)
        {
			$ActiveWorksheet.Cells.Item($rowIndex,1) = "$($student.StudentName)"
			$ActiveWorksheet.Cells.Item($rowIndex,2) = "$($student.EnglishName)"
			$ActiveWorksheet.Cells.Item($rowIndex,3) = "$($student.TotalNumberOfSessions)"
			$ActiveWorksheet.Cells.Item($rowIndex,4) = "$($student.RemainNumberOfSessions)"
			$ActiveWorksheet.Cells.Item($rowIndex,5) = "$($student.TotalPayments)"
			
			$rowIndex++
        }
		#$ActiveWorksheet.UsedRange.EntireColumn.AutoFit()
		#$ActiveWorksheet.UsedRange.EntireColumn.AutoFilter()
		$ActiveWorksheet.Columns.Item("A:E").EntireColumn.AutoFit()
		$ActiveWorkbook.SaveAs($outFile)
    }

	$ExcelObject.Quit()
    Write-RunLog "====================== Ending ======================"
}

Write-Host "PowerShell version $($PSVersionTable.PSVersion)"

Get-StudentAttendance
