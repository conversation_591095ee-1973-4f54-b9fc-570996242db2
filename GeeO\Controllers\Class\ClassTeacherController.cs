﻿using GeeO.Data;
using GeeO.GridVo;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassTeacherController : ControllerBase
    {
        private readonly GeeODbContext _context;
        public ClassTeacherController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/ClassTeacher
        [HttpGet("{id}")]
        public async Task<ActionResult<IEnumerable<ClassTeacherGrid>>> GetClassTeacher(string id)
        {
            var data = _context.ClassTeacher.Include(x => x.Teacher).Where(x => x.ClassId == id);
            var result = from x in data
                         select new ClassTeacherGrid
                         {
                             Id = x.Id,
                             UserName = x.Teacher.UserName,
                             PhoneNumber = x.Teacher.PhoneNumber,
                             FirstName = x.Teacher.FirstName,
                             LastName = x.Teacher.LastName,
                             EmployeeCode = x.Teacher.EmployeeCode,
                             TeacherId = x.TeacherId,
                             IsPrimary = x.IsPrimary,
                             FullName = x.Teacher.FullName,
                             EnglishName = x.Teacher.EnglishName,
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpPost]
        [Route("list-filter")]
        public async Task<ActionResult<IEnumerable<ClassTeacherGrid>>> GetClassTeacherFilter(List<string> lstId)
        {
            var data = _context.AspNetUsers.Where(x => !lstId.Contains(x.Id)
                                                       && (x.Role.Name.ToLower() == "Acad Manager".ToLower() || x.Role.Name.ToLower() == "Teacher".ToLower()));
            var result = from x in data
                         select new ClassTeacherGrid
                         {
                             Id = x.Id,
                             UserName = x.UserName,
                             PhoneNumber = x.PhoneNumber,
                             FirstName = x.FirstName,
                             LastName = x.LastName,
                             EmployeeCode = x.EmployeeCode
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpPut("{classId}")]
        public async Task<ActionResult> PutClassTeacher(string classId, List<string> lstSelected)
        {
            var classCourse = await _context.ClassCourse.Include(x=> x.ClassTeachers).FirstOrDefaultAsync(x=> x.Id == classId).ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }

            foreach (var teacherId in lstSelected)
            {
                if (classCourse.ClassTeachers.Count == 2)
                {
                    break;
                }

                ClassTeacher obj = new()
                {
                    ClassId = classId,
                    TeacherId = teacherId,
                    IsPrimary = classCourse.ClassTeachers.Count == 0
                };
                _context.ClassTeacher.Add(obj);
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok();
        }

        [HttpGet("swap-primary/{classId}")]
        public async Task<ActionResult> SwapPrimaryTeacher(string classId)
        {
            var classCourse = await _context.ClassCourse.Include(x => x.ClassTeachers).FirstOrDefaultAsync(x => x.Id == classId).ConfigureAwait(false);
            if (classCourse == null)
            {
                return NotFound();
            }

            var teachers = classCourse.ClassTeachers;
            if (teachers.Any())
            {
                var primaryTeacher = teachers.FirstOrDefault(x => x.IsPrimary);
                if (primaryTeacher != null)
                {
                    primaryTeacher.IsPrimary = false;
                    var secondaryTeacher = teachers.FirstOrDefault(x => x.Id != primaryTeacher.Id);
                    if (secondaryTeacher != null)
                    {
                        secondaryTeacher.IsPrimary = true;
                    }
                }
                else
                {
                    // If no primary teacher, set the first teacher as primary
                    teachers.First().IsPrimary = true;
                }
            }

            _context.UpdateRange(teachers);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<ClassTeacher>> DeleteClassTeacher(string id)
        {
            var classTeacher = await _context.ClassTeacher.FindAsync(id);
            if (classTeacher == null)
            {
                return NotFound();
            }

            _context.ClassTeacher.Remove(classTeacher);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return classTeacher;
        }
    }
}
