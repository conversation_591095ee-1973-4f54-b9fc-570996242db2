USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentAssessment]    Script Date: 31-Aug-20 2:48:50 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentAssessment](
	[Id] [nvarchar](450) NOT NULL,
	[StudentLessonLogId] [nvarchar](450) NOT NULL,
	[AssessmentCriteriaId] [nvarchar](450) NOT NULL,
	[Score] [int] NULL,
 CONSTRAINT [PK_StudentAssessment] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentAssessment] ADD  DEFAULT ((0)) FOR [Score]
GO

ALTER TABLE [dbo].[StudentAssessment]  WITH CHECK ADD  CONSTRAINT [FK_StudentAssessment_StudentLessonLogData] FOREIGN KEY([StudentLessonLogId])
REFERENCES [dbo].[StudentLessonLogData] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[StudentAssessment] CHECK CONSTRAINT [FK_StudentAssessment_StudentLessonLogData]
GO

ALTER TABLE [dbo].[StudentAssessment]  WITH CHECK ADD  CONSTRAINT [FK_StudentAssessment_AssessmentCriteriaId] FOREIGN KEY([AssessmentCriteriaId])
REFERENCES [dbo].[AssessmentCriteria] ([Id])
GO

ALTER TABLE [dbo].[StudentAssessment] CHECK CONSTRAINT [FK_StudentAssessment_AssessmentCriteriaId]
GO


