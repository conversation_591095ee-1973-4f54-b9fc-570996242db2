﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using GeeO.GridVo;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ParentsController : ControllerBase
    {
        private readonly GeeODbContext _context;

        public ParentsController(GeeODbContext context)
        {
            _context = context;
        }

        // GET: api/Parents/GetByClass/5
        [HttpGet("[action]/{classId}")]
        public async Task<ActionResult<IEnumerable<Parent>>> GetByClass(string classId)
        {
            var result = await (from cs in _context.ClassStudent.Where(x=> x.Student.SuspendDate == null)
                                join sp in _context.StudentParent on cs.StudentId equals sp.StudentId
                                join pr in _context.Parent on sp.ParentId equals pr.Id
                                where cs.ClassId == classId
                                select pr).ToListAsync().ConfigureAwait(false);
            return result;
        }

        // GET: api/Parents
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Parent>>> GetParents()
        {
            return await _context.Parent.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/Parents/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Parent>> GetParent(string id)
        {
            var parent = await _context.Parent.FindAsync(id);

            if (parent == null)
            {
                return NotFound();
            }

            return parent;
        }

        [HttpPut("{id}/{studentId}")]
        public async Task<IActionResult> UpdateParentByStudent(string id, string studentId, Parent parent)
        {
            try
            {
                if (parent != null && parent.Id == id )
                {
                    _context.Entry(parent).State = EntityState.Modified;
                }
                else 
                {
                    var studentParent = await _context.StudentParent.FirstOrDefaultAsync(x => x.ParentId == id && x.StudentId == studentId);
                    if (studentParent != null)
                    {
                        studentParent.ParentId = parent.Id;
                        _context.Entry(studentParent).State = EntityState.Modified;
                    }
                    else
                    {
                        await _context.StudentParent.AddAsync(new StudentParent() { ParentId = parent.Id, StudentId = studentId });
                    }
                }

                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ParentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // PUT: api/Parents/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutParent(string id, Parent parent)
        {
            try
            {
                if (parent == null || id != parent.Id)
                {
                    var studentParent = await _context.StudentParent.FirstOrDefaultAsync(x => x.ParentId == id);
                    if (studentParent != null)
                    {
                        studentParent.ParentId = parent.Id;
                        _context.Entry(studentParent).State = EntityState.Modified;
                    }
                }

                _context.Entry(parent).State = EntityState.Modified;

                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ParentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Parents
        [HttpPost]
        public async Task<ActionResult<Parent>> PostParent(Parent parent)
        {
            if (parent == null)
            {
                return BadRequest();
            }
            _context.Parent.Add(parent);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetParent", new { id = parent.Id }, parent);
        }

        // POST: api/Parents
        [HttpPost("[action]/{studentId}")]
        public async Task<ActionResult<Parent>> CreateParent(string studentId, Parent parent)
        {
            if (parent == null)
            {
                return BadRequest();
            }
            _context.Parent.Add(parent);
            _context.StudentParent.Add(new StudentParent() { ParentId = parent.Id, StudentId = studentId });
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetParent", new { id = parent.Id }, parent);
        }

        // DELETE: api/Parents/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<Parent>> DeleteParent(string id)
        {
            var parent = await _context.Parent.FindAsync(id);
            if (parent == null)
            {
                return NotFound();
            }

            _context.Parent.Remove(parent);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return parent;
        }

        private bool ParentExists(string id)
        {
            return _context.Parent.Any(e => e.Id == id);
        }

        
    }
}
