USE [GeeODb]
GO

/****** Object:  Table [dbo].[UserRequestGroups]    Script Date: 3/26/2025 5:31:57 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[UserRequestGroups](
	[Id] [nvarchar](450) NOT NULL,
	[GroupId] [nvarchar](450) NULL,
	[UserId] [nvarchar](450) NULL,
	[IsAdmin] [bit] NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserRequestGroups] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[UserRequestGroups] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[UserRequestGroups]  WITH CHECK ADD  CONSTRAINT [FK_UserRequestGroups_AspNetUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[UserRequestGroups] CHECK CONSTRAINT [FK_UserRequestGroups_AspNetUsers]
GO

ALTER TABLE [dbo].[UserRequestGroups]  WITH CHECK ADD  CONSTRAINT [FK_UserRequestGroups_RequestGroups] FOREIGN KEY([GroupId])
REFERENCES [dbo].[RequestGroups] ([Id])
GO

ALTER TABLE [dbo].[UserRequestGroups] CHECK CONSTRAINT [FK_UserRequestGroups_RequestGroups]
GO


