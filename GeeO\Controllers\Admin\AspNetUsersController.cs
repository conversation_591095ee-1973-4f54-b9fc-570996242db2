﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using GeeO.GridVo;
using GeeO.Common;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using GeeO.Services;
using System.Text.Json;
using GeeO.Model;
using GeeO.Data.GridVo;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AspNetUsersController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IAcadManageService _acadManageService;

        public AspNetUsersController(GeeODbContext context, UserManager<ApplicationUser> userManager, IAcadManageService acadManageService)
        {
            _context = context;
            this._userManager = userManager;
            this._acadManageService = acadManageService;
        }

        // GET: api/AspNetUsers/GetUserRole/5
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<AspNetUsers>> GetUserRole(string userId)
        {
            return await _context.AspNetUsers.AsNoTracking()
                                    .Where(u => u.Id == userId)
                                    .Include(u => u.Role).FirstAsync().ConfigureAwait(false);
        }

        // GET: api/AspNetUsers
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AspNetUserVo>>> GetAspNetUsers()
        {
            var result = await _context.AspNetUsers
                                        .Include(x => x.Role)
                                        .OrderBy(r => r.FirstName).ThenBy(r => r.LastName)
                                        .Select(x => new AspNetUserVo()
                                        {
                                            Id = x.Id,
                                            Email = x.Email,
                                            EmployeeCode = x.EmployeeCode,
                                            FirstName = x.FirstName,
                                            LastName = x.LastName,
                                            PhoneNumber = x.PhoneNumber,
                                            Role = x.Role != null ? x.Role.Name : ""
                                        }).ToListAsync().ConfigureAwait(false);
            return result;
        }

        [HttpGet("[action]")]
        public async Task<ActionResult<IEnumerable<AspNetUsersWithRoleVo>>> GetAspNetUsersWithRoles()
        {
            var result = await _context.AspNetUsers
                .Include(x => x.Role)
                .Include(x => x.UserInfo)
                .Where(x => x.RoleId != null)
                .OrderBy(r => r.FirstName)
                .ThenBy(r => r.LastName)
                .Select(x => new AspNetUsersWithRoleVo()
                {
                    Id = x.Id,
                    FullName = x.FirstName + " " + x.LastName,
                    EnglishName = x.UserInfo.EnglishName,
                    Email = x.Email
                })
                .ToListAsync()
                .ConfigureAwait(false);
            return result;
        }


        // GET: api/AspNetUsers/5
        [HttpGet("{id}")]
        public async Task<ActionResult<AspNetUsers>> GetAspNetUsers(string id)
        {
            var aspNetUsers = await _context.AspNetUsers.Where(x => x.Id == id).Include(x => x.UserInfo).FirstOrDefaultAsync();
            if (aspNetUsers == null)
            {
                return NotFound();
            }
            aspNetUsers.TitleName = aspNetUsers.UserInfo?.TitleName ?? string.Empty;
            aspNetUsers.CampusId = aspNetUsers.UserInfo?.CampusId ?? string.Empty;
            aspNetUsers.AssignedCampus = aspNetUsers.UserInfo?.AssignedCampus ?? string.Empty;
            aspNetUsers.EnglishName = aspNetUsers.UserInfo?.EnglishName ?? string.Empty;
            aspNetUsers.ElAccount = aspNetUsers.UserInfo?.ElAccount ?? string.Empty;
            aspNetUsers.IsExpat = aspNetUsers.UserInfo != null ? aspNetUsers.UserInfo.IsExpat : false;
            return aspNetUsers;
        }

        // PUT: api/AspNetUsers/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutAspNetUsers(string id, AspNetUsers aspNetUsers)
        {
            ApplicationUser user = await _userManager.FindByIdAsync(id).ConfigureAwait(false);

            if (user == null)
            {
                return NotFound($"User Id '{id}' not found!");
            }

            user.UserName = aspNetUsers.Email;
            user.NormalizedUserName = aspNetUsers.Email;
            user.Email = aspNetUsers.Email;
            user.EmployeeCode = aspNetUsers.EmployeeCode;
            user.FirstName = aspNetUsers.FirstName;
            user.LastName = aspNetUsers.LastName;
            user.PhoneNumber = aspNetUsers.PhoneNumber;
            user.RoleId = aspNetUsers.RoleId;

            try
            {
                await _userManager.UpdateAsync(user).ConfigureAwait(false);

                if (string.IsNullOrEmpty(aspNetUsers.EnglishName))
                {
                    string englishName = aspNetUsers.Email.Substring(0, aspNetUsers.Email.IndexOf("@"));
                    aspNetUsers.EnglishName = englishName[0..1].ToUpper() + englishName[1..];
                }

                var userInfo = await _context.UserInfo.Where(x => x.UserId == id).FirstOrDefaultAsync();
                if (userInfo != null)
                {
                    userInfo.TitleName = aspNetUsers.TitleName;
                    userInfo.EnglishName = aspNetUsers.EnglishName;
                    userInfo.ElAccount = aspNetUsers.ElAccount;
                    userInfo.CampusId = aspNetUsers.CampusId;
                    userInfo.AssignedCampus = aspNetUsers.AssignedCampus;
                    userInfo.ModifiedDate = DateTime.Now;
                    userInfo.ModifiedBy = "sysadmin";
                    userInfo.IsExpat = aspNetUsers.IsExpat;
                    _context.Entry(userInfo).State = EntityState.Modified;
                }
                else
                {
                    userInfo = new UserInfo
                    {
                        UserId = user.Id,
                        TitleName = aspNetUsers.TitleName,
                        EnglishName = aspNetUsers.EnglishName,
                        ElAccount = aspNetUsers.ElAccount,
                        CampusId = aspNetUsers.CampusId,
                        AssignedCampus = aspNetUsers.AssignedCampus,
                        CreatedDate = DateTime.Now,
                        CreatedBy = "sysadmin",
                        IsExpat = aspNetUsers.IsExpat,
                    };
                    _context.UserInfo.Add(userInfo);
                }
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AspNetUsersExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/AspNetUsers
        [HttpPost]
        public async Task<ActionResult<AspNetUsers>> PostAspNetUsers(AspNetUsers aspNetUsers)
        {
            aspNetUsers.UserName = aspNetUsers.Email;
            aspNetUsers.NormalizedUserName = aspNetUsers.Email;

            var user = new ApplicationUser
            {
                UserName = aspNetUsers.Email,
                Email = aspNetUsers.Email,
                NormalizedUserName = aspNetUsers.Email,
                FirstName = aspNetUsers.FirstName,
                LastName = aspNetUsers.LastName,
                PhoneNumber = aspNetUsers.PhoneNumber,
                EmployeeCode = aspNetUsers.EmployeeCode,
                RoleId = aspNetUsers.RoleId
            };

            if (aspNetUsers.IsTeacher)
            {
                var roleTeacher = await _context.AspNetRoles.Where(x => x.NormalizedName == "TEACHER").FirstOrDefaultAsync().ConfigureAwait(false);
                user.RoleId = roleTeacher.Id;
            }

            try
            {
                var result = await _userManager.CreateAsync(user, "Gee0pw@2022!").ConfigureAwait(false);

                if (string.IsNullOrEmpty(aspNetUsers.EnglishName))
                {
                    string englishName = aspNetUsers.Email.Substring(0, aspNetUsers.Email.IndexOf("@"));
                    aspNetUsers.EnglishName = englishName[0..1].ToUpper() + englishName[1..];
                }

                var userInfo = new UserInfo
                {
                    UserId = user.Id,
                    TitleName = aspNetUsers.TitleName,
                    EnglishName = aspNetUsers.EnglishName,
                    ElAccount = aspNetUsers.ElAccount,
                    CampusId = aspNetUsers.CampusId,
                    AssignedCampus = aspNetUsers.AssignedCampus,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "sysadmin",
                    IsExpat = aspNetUsers.IsExpat
                };
                _context.UserInfo.Add(userInfo);
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateException)
            {
                if (AspNetUsersExists(aspNetUsers.Id))
                {
                    return Conflict();
                }
                else
                {
                    throw;
                }
            }

            return CreatedAtAction("GetAspNetUsers", new { id = aspNetUsers.Id }, aspNetUsers);
        }

        // DELETE: api/AspNetUsers/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<AspNetUsers>> DeleteAspNetUsers(string id)
        {
            var aspNetUsers = await _context.AspNetUsers.FindAsync(id);
            if (aspNetUsers == null)
            {
                return NotFound();
            }

            _context.AspNetUsers.Remove(aspNetUsers);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return aspNetUsers;
        }

        private bool AspNetUsersExists(string id)
        {
            return _context.AspNetUsers.Any(e => e.Id == id);
        }

        // GET: api/AspNetUsers
        [HttpGet("teacher")]
        public async Task<ActionResult<IEnumerable<AspNetUserVo>>> GetTeacher()
        {
            var data = from u in _context.AspNetUsers.Where(x => GeeOConstants.TeacherRoles.Contains(x.Role.NormalizedName))
                       from ui in _context.UserInfo.Where(ui => ui.UserId == u.Id)
                       orderby u.FirstName, u.LastName
                       select new AspNetUserVo()
                       {
                           Id = u.Id,
                           Email = u.Email,
                           EmployeeCode = u.EmployeeCode,
                           FirstName = u.FirstName,
                           LastName = u.LastName,
                           PhoneNumber = u.PhoneNumber,
                           EnglishName = ui.EnglishName,
                           ElAccount = ui.ElAccount,
                           IsExpat = ui.IsExpat,
                       };

            return await data.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/AspNetUsers
        [HttpGet("expat-teacher")]
        public async Task<ActionResult<IEnumerable<AspNetUserVo>>> GetExpatTeacher()
        {
            var data = from u in _context.AspNetUsers.Where(x => GeeOConstants.TeacherRoles.Contains(x.Role.NormalizedName))
                       from ui in _context.UserInfo.Where(ui => ui.UserId == u.Id && ui.IsExpat)
                       orderby u.FirstName, u.LastName
                       select new AspNetUserVo()
                       {
                           Id = u.Id,
                           Email = u.Email,
                           EmployeeCode = u.EmployeeCode,
                           FirstName = u.FirstName,
                           LastName = u.LastName,
                           PhoneNumber = u.PhoneNumber,
                           EnglishName = ui.EnglishName,
                           ElAccount = ui.ElAccount,
                           IsExpat = ui.IsExpat,
                       };

            return await data.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/AspNetUsers
        [HttpGet("teaching-schedule/{id}")]
        public async Task<ActionResult<IEnumerable<TeacherSchedule>>> GeTeachingSchedule(string id)
        {
            var result = from ct in _context.ClassTeacher.Where(x => x.TeacherId == id)
                         from cls in _context.ClassCourse.Where(cls => ct.ClassId == cls.Id).Include(cls => cls.Level)
                         from sch in _context.Schedule.Where(sch => cls.Id == sch.ClassCourseId).DefaultIfEmpty()
                         orderby sch.StartDate descending
                         select new TeacherSchedule()
                         {
                             Id = ct.Id,
                             StartDate = sch != null ? sch.StartDate : null,
                             EndDate = sch != null ? sch.EndDate : null,
                             Schedule = sch != null ? sch.ScheduleFormat : string.Empty,
                             StartTime = sch != null ? sch.StartTime : null,
                             EndTime = sch != null ? sch.EndTime : null,
                             Level = cls.Level.Name,
                             ClassCourseName = cls.Name
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<AspNetUserVo>>> GetTeacherAssignTaskSys(string id)
        {
            var result = await _context.AspNetUsers.Where(x => x.Id != id && x.Role.Name == "Teacher").Select(x => new AspNetUserVo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Email = x.Email,
                PhoneNumber = x.PhoneNumber
            }).ToListAsync().ConfigureAwait(false);
            return result;
        }

        [HttpGet("[action]/{id}")]
        public async Task<ActionResult<IEnumerable<TeacherReport>>> GetReportByTeacher(string id)
        {
            var tempDataTeachingHours = from u in _context.AspNetUsers
                                        join tll in _context.TeacherLessonLog on u.Id equals tll.TeacherId
                                        join tlld in _context.TeacherLessonLogData on tll.Id equals tlld.LogId
                                        join cl in _context.ClassLesson on tll.ClassLessonId equals cl.Id
                                        join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                        where u.Id == id
                                        select new TeacherReport()
                                        {
                                            Id = cc.Id,
                                            Class = cc.Name,
                                            TeachingHours = tlld.Duration
                                        };
            var dataTeachingHours = await (from u in tempDataTeachingHours
                                           group u by new { u.Id, u.Class } into g
                                           select new TeacherReport()
                                           {
                                               Id = g.Key.Id,
                                               Class = g.Key.Class,
                                               TeachingHours = g.Sum(x => x.TeachingHours)
                                           }).ToListAsync().ConfigureAwait(false);
            var tempDataCatchUp = from u in _context.AspNetUsers
                                  join cus in _context.CatchUpSchedules on u.Id equals cus.TeacherId
                                  join cl in _context.ClassLesson on cus.ClassLessonId equals cl.Id
                                  join cc in _context.ClassCourse on cl.ClassId equals cc.Id
                                  join tll in _context.TeacherLessonLog on u.Id equals tll.TeacherId into teacherLessonLogGroup
                                  from _tll in teacherLessonLogGroup.DefaultIfEmpty()
                                  join tlld in _context.TeacherLessonLogData on _tll.Id equals tlld.LogId
                                  where u.Id == id
                                  select new TeacherReport()
                                  {
                                      Id = cc.Id,
                                      Class = cc.Name,
                                      TeachingHoursCatchUp = cus.CatchUpType == 1 || cus.CatchUpType == 2 ? cus.CatchUpTime : tlld.Duration
                                  };
            var dataCatchUp = await (from u in tempDataCatchUp
                                     group u by new { u.Id, u.Class } into g
                                     select new TeacherReport()
                                     {
                                         Id = g.Key.Id,
                                         Class = g.Key.Class,
                                         TeachingHoursCatchUp = g.Sum(x => x.TeachingHoursCatchUp)
                                     }).ToListAsync().ConfigureAwait(false);

            foreach (var item in dataTeachingHours)
            {
                var temp = dataCatchUp.FirstOrDefault(x => x.Id == item.Id);
                if (temp != null)
                {
                    item.TeachingHoursCatchUp = temp.TeachingHoursCatchUp;
                }
            }
            var checkCatchUp = dataCatchUp.Where(x => !dataTeachingHours.Select(d => d.Id).Contains(x.Id)).Select(x =>
                new TeacherReport()
                {
                    Id = x.Id,
                    Class = x.Class,
                    TeachingHoursCatchUp = x.TeachingHoursCatchUp
                });
            foreach (var item in checkCatchUp)
            {
                dataTeachingHours.Add(item);
            }
            return dataTeachingHours;
        }

        //[Authorize(Roles = "Admin")]
        [HttpGet("[action]/{id}/{act}")]
        public async Task<IActionResult> RandomUserPwd(string id, string act)
        {
            if (string.IsNullOrEmpty(act) || act != "8080")
                return BadRequest();

            AspNetUsers user = await _context.AspNetUsers.Where(u => u.Id == id).FirstOrDefaultAsync();
            if (user == null || user.UserName != "<EMAIL>")
                return BadRequest();

            string[] exceptUsers = new string[] { "<EMAIL>", "<EMAIL>" };
            List<AspNetUsers> users = await _context.AspNetUsers.Where(u => !exceptUsers.Contains(u.Email)).ToListAsync();
            PasswordHasher<AspNetUsers> passwordHasher = new();
            foreach (var usr in users)
            {
                string rdmStr = StringUtils.GenerateString(16);
                string hsPwd = passwordHasher.HashPassword(usr, rdmStr);
                usr.PasswordHash = hsPwd;
                _context.Entry(usr).State = EntityState.Modified;
            }
            await _context.SaveChangesAsync().ConfigureAwait(false);
            return Ok("Users password reset successfully....");
        }

        [HttpGet("teacher/branch")]
        public async Task<ActionResult<IEnumerable<UserByBranchResponse>>> GetTeacherByBranch()
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                var adminUser = await _context.AspNetUsers.FirstOrDefaultAsync(u => GeeOConstants.AdminRoles.Contains(u.Role.NormalizedName));
                var assignedCampus = await _acadManageService.GetAssignedCampus(userId);
                var users = await _context.AspNetUsers
                    .Include(u => u.UserInfo)
                    .Where(u => GeeOConstants.TeacherRoles.Contains(u.Role.NormalizedName))
                    .ToListAsync();
                var filteredUsers = assignedCampus == null && adminUser != null ? users :
                    users
                    .Where(u => u.UserInfo.AssignedCampus != null && assignedCampus.Intersect(JsonSerializer.Deserialize<List<string>>(u.UserInfo.AssignedCampus)).Any());

                return filteredUsers.Select(u => new UserByBranchResponse()
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Branchs = u.UserInfo != null && !string.IsNullOrEmpty(u.UserInfo.AssignedCampus)
                                ? JsonSerializer.Deserialize<List<string>>(u.UserInfo.AssignedCampus)
                                : new List<string>()
                }).ToList();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("sale/branch")]
        public async Task<ActionResult<IEnumerable<UserByBranchResponse>>> GetSaleExecutiveByBranch()
        {
            try
            {
                string userId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
                var adminUser = await _context.AspNetUsers.FirstOrDefaultAsync(u => GeeOConstants.AdminRoles.Contains(u.Role.NormalizedName));
                var assignedCampus = await _acadManageService.GetAssignedCampus(userId);
                var users = await _context.AspNetUsers
                    .Include(u => u.UserInfo)
                    .Where(u => GeeOConstants.SaleRoles.Contains(u.Role.NormalizedName))
                    .ToListAsync();
                var filteredUsers = assignedCampus == null && adminUser != null ? users :
                    users
                    .Where(u => u.UserInfo.AssignedCampus != null && assignedCampus.Intersect(JsonSerializer.Deserialize<List<string>>(u.UserInfo.AssignedCampus)).Any());

                return filteredUsers.Select(u => new UserByBranchResponse()
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Branchs = u.UserInfo != null && !string.IsNullOrEmpty(u.UserInfo.AssignedCampus)
                                ? JsonSerializer.Deserialize<List<string>>(u.UserInfo.AssignedCampus)
                                : new List<string>()
                }).ToList();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
