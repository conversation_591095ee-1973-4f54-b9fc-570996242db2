import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';

const styles = theme => ({
  root: {
    width: '100%',
    backgroundColor: theme.palette.background.paper,
    marginBottom: theme.spacing(2),
    overflowX: 'auto'
  },
  cell: {
    padding: theme.spacing(1, 0, 1, 2)
  },
  schedule: {
    margin: theme.spacing(1, 0, 0, 2)
  }
});

class CatchUpInfoComp extends Component {
  static displayName = CatchUpInfoComp.name;

  constructor(...args) {
    super(...args);
    this.state = {
      catchUp: this.props.catchUp
    };
  }

  render() {
    const { classes, catchUp } = this.props;
    const cols = [
      {
        name: 'name',
        header: 'Name',
        xs: 3
      },
      {
        name: 'typeFormat',
        header: 'Type',
        xs: 9
      }
    ];

    return (
      <Paper className={classes.root}>
        <Grid container>
          {cols.map((col, idx) => (
            <Grid item xs={col.xs} className={classes.cell} key={idx}>
              <Typography color="textSecondary" variant="caption">
                {col.header}
              </Typography>
              <Typography color="textPrimary" variant="body2">
                {catchUp[col.name]}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Paper>
    );
  }
}

CatchUpInfoComp.propTypes = {
  classes: PropTypes.object.isRequired
};

export const CatchUpInfo = withStyles(styles)(CatchUpInfoComp);
