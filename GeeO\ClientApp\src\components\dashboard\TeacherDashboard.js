import React, { Component } from 'react';

import { AdminPage } from '../ui/page/AdminPage';
import { Grid } from '@material-ui/core';
import NotReportedLessons from './NotReportedLessons';
import Notifications from './Notifications';
import Tasks from './Tasks';
import UpComingLessons from './UpComingLessons';

export class TeacherDashboard extends Component {
  constructor(...args) {
    super(...args);
    this.state = {
      displayComponent: <div>Teacher Dashboard</div>
    };
  }

  renderContent() {
    const { parentComponent } = this.props;
    const { calendarComponent } = this.props?.parentComponent?.state;

    return (
      <Grid container alignItems="flex-start" spacing={2}>
        <Grid item xs={8}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              {calendarComponent}
            </Grid>
            <Grid item xs={12}>
              <UpComingLessons parentComponent={parentComponent} />
            </Grid>
            <Grid item xs={12}>
              <Tasks />
            </Grid>
            <Grid item xs={12}>
              <NotReportedLessons />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={4}>
          <Notifications />
        </Grid>
      </Grid>
    );
  }

  render() {
    const displayContent = this.renderContent();
    return <AdminPage title="Teacher Dashboard" content={displayContent} />;
  }
}
