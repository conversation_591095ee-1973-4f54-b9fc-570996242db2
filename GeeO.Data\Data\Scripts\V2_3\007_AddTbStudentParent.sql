﻿USE [GeeODb]
GO

/****** Object:  Table [dbo].[StudentParent]    Script Date: 9/12/2019 8:36:43 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[StudentParent](
	[Id] [nvarchar](450) NOT NULL,
	[StudentId] [nvarchar](450) NOT NULL,
	[ParentId] [nvarchar](450) NOT NULL,
 CONSTRAINT [PK_StudentParent] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[StudentParent]  WITH CHECK ADD  CONSTRAINT [FK_StudentParent_Parent] FOREIGN KEY([ParentId])
REFERENCES [dbo].[Parent] ([Id])
GO

ALTER TABLE [dbo].[StudentParent] CHECK CONSTRAINT [FK_StudentParent_Parent]
GO

ALTER TABLE [dbo].[StudentParent]  WITH CHECK ADD  CONSTRAINT [FK_StudentParent_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[StudentParent] CHECK CONSTRAINT [FK_StudentParent_Student]
GO


