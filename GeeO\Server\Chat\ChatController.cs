﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GeeO.Api.Hubs;
using GeeO.Api.Hubs.Clients;
using GeeO.Api.Models;
using GeeO.Common;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace GeeO.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class ChatController : ControllerBase
    {
        private readonly IHubContext<ChatHub, IChatClient> _chatHub;
        private readonly GeeODbContext _context;

        public ChatController(IHubContext<ChatHub, IChatClient> chatHub, GeeODbContext context)
        {
            _chatHub = chatHub;
            _context = context;
        }

        [HttpPost("[action]")]
        public async Task SendMessage(ChatMessageApi message)
        {
            List<KeyValuePair<string, string>> onlineUsers = ChatHub.OnlineClientsDict.Where(x => message.Recipients.Contains(x.Key)).ToList();
            IReadOnlyList<string> connectionIds = onlineUsers.Select(x => x.Value).ToList().AsReadOnly();
            await _chatHub.Clients.Clients(connectionIds).ReceiveMessage(message);

            //insert db
            ChatMessage chatMsg = ObjectUtils.CopyObject<ChatMessage>(message);
            _context.ChatMessage.Add(chatMsg);

            List<string> onlineUserIds = onlineUsers.Select(x => x.Key).ToList();
            List<string> pendingUserIds = message.Recipients.Except(onlineUserIds).ToList();
            List<PendingChatMessage> pendingMsgs = new();
            foreach (string userId in pendingUserIds)
            {
                pendingMsgs.Add(new() { UserId = userId, ChatMessageId = chatMsg.Id });
            }
            await _context.PendingChatMessage.AddRangeAsync(pendingMsgs);
            await _context.SaveChangesAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ChatMessage>>> GetPendingChats(string userId)
        {
            var result = from pdn in _context.PendingChatMessage.Where(x => x.UserId == userId)
                         from msg in _context.ChatMessage.Where(msg => pdn.ChatMessageId == msg.Id)
                         orderby msg.SentTime
                         select msg;

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{userId}")]
        public async Task CleanPendingChats(string userId)
        {
            SqlParameter pUserId = new("userId", userId);
            _ = await _context.Database.ExecuteSqlRawAsync(@"DELETE FROM PendingChatMessage WHERE UserId=@userId", pUserId);
        }

        [HttpPost("[action]")]
        public async Task SendMessageToAll(ChatMessageApi message)
        {
            await _chatHub.Clients.All.ReceiveMessage(message);
        }
    }
}
