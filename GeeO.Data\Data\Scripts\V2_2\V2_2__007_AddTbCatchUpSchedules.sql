﻿/****** Object:  Table [dbo].[CatchUpSchedules]    Script Date: 02-Nov-19 4:28:37 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[CatchUpSchedules](
	[Id] [nvarchar](450) NOT NULL,
	[Name] [nvarchar](256) NULL,
	[CatchUpType] [int] NULL,
	[CatchUpTime] [int] NULL,
	[CatchUpStatus] [bit] NOT NULL,
	[IsChangeClass] [bit] NULL,
	[CreateDate] [datetime] NULL,
	[TeacherId] [nvarchar](450) NOT NULL,
	[ClassLessonId] [nvarchar](450) NULL,
	[StudentId] [nvarchar](450) NULL,
 CONSTRAINT [PK_CatchUpSchedules] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_AspNetUsers] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_AspNetUsers]
GO

ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_ClassLesson] FOREIGN KEY([ClassLessonId])
REFERENCES [dbo].[ClassLesson] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_ClassLesson]
GO

ALTER TABLE [dbo].[CatchUpSchedules]  WITH CHECK ADD  CONSTRAINT [FK_CatchUpSchedules_Student] FOREIGN KEY([StudentId])
REFERENCES [dbo].[Student] ([Id])
GO

ALTER TABLE [dbo].[CatchUpSchedules] CHECK CONSTRAINT [FK_CatchUpSchedules_Student]
GO


