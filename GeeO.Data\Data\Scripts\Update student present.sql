DECLARE @MyCursor CURSOR;
DECLAR<PERSON> @LogId nvarchar(450);
BEGIN
    SET @MyCursor = CURSOR FOR
    SELECT Id
	  FROM StudentLessonLogData
	WHERE Present = 0 AND LogId IN (SELECT Id FROM TeacherLessonLog WHERE LogDateTime > '2021-03-09') 

    O<PERSON>EN @MyCursor 
    FETCH NEXT FROM @MyCursor 
    INTO @LogId

    WHILE @@FETCH_STATUS = 0
    BEGIN
      
		UPDATE [dbo].[StudentLessonLogData]
		SET Present = 0
		WHERE Id = @LogId;

      FETCH NEXT FROM @MyCursor 
      INTO @LogId 
    END; 

    CLOSE @MyCursor ;
    DEALLOCATE @MyCursor;
END;


