﻿using GeeO.Common;
using GeeO.Data;
using GeeO.Data.Dto;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class AcademicManageController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IAcadManageService _acadManageService;

        public AcademicManageController(GeeODbContext context, UserManager<ApplicationUser> userManager, IAcadManageService acadManageService)
        {
            _context = context;
            _userManager = userManager;
            _acadManageService = acadManageService;
        }

        [HttpPost("[action]")]
        public async Task<ActionResult> SaveHolidays(List<Holidays> updateHolidays)
        {
            if (updateHolidays == null)
            {
                return BadRequest();
            }

            foreach (var updateHoliday in updateHolidays)
            {
                var holidays = await _context.Holidays
                                    .Where(x => (updateHoliday.ClassId == null && x.ClassId == null) || x.ClassId == updateHoliday.ClassId)
                                    .FirstOrDefaultAsync().ConfigureAwait(false);

                if (holidays == null)
                {
                    updateHoliday.CreatedDate = DateTime.Now;
                    _context.Holidays.Add(updateHoliday);
                }
                else
                {
                    holidays.HolidayList = updateHoliday.HolidayList;
                    holidays.ExcludeDays = updateHoliday.ExcludeDays;
                    holidays.ModifiedBy = updateHoliday.CreatedBy;
                    holidays.ModifiedDate = DateTime.Now;
                    _context.Entry(holidays).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            return Ok();
        }

        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<Holidays>>> GetHolidays(string userId)
        {
            List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = from hld in _context.Holidays
                         from cls in _context.ClassCourse.Where(cls => cls.Id == hld.ClassId && (assignedCampus == null || assignedCampus.Contains(cls.CampusId))).DefaultIfEmpty()
                         select hld;

            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{classId}/{studentId}/{examType}")]
        public async Task<ActionResult<ExamResult>> GetExamResultForm(string classId, string studentId, int examType)
        {
            var examResult = await _context.ExamResult.Where(x => x.ClassId == classId && x.StudentId == studentId && x.ExamType == examType)
                .FirstOrDefaultAsync().ConfigureAwait(false);

            if (examResult != null && !string.IsNullOrEmpty(examResult.ExamResultJson))
            {
                return examResult;
            }

            var examResultForm = from cls in _context.ClassCourse.Where(x => x.Id == classId)
                                 from erf in _context.ExamResultForm.Where(x => x.ExamType == examType && x.LevelId == cls.LevelId).DefaultIfEmpty()
                                 select new ExamResult
                                 {
                                     ClassId = classId,
                                     StudentId = studentId,
                                     ExamType = examType,
                                     ExamResultJson = erf.ExamFormJson ?? string.Empty,
                                     TeacherComment = examResult != null ? examResult.TeacherComment : string.Empty
                                 };

            return await examResultForm.FirstOrDefaultAsync().ConfigureAwait(false);
        }

        [HttpGet("exam-templates/{classId}")]
        public async Task<ActionResult<ExamResult>> GetExamTemplates(string classId)
        {
            var _class = await _context.ClassCourse.Where(x => x.Id == classId)
                .FirstOrDefaultAsync().ConfigureAwait(false);

            if (_class != null)
            {
                var examTemplates = await _context.ExamResultForm.Where(x => x.LevelId == _class.LevelId)
                    .ToListAsync().ConfigureAwait(false);

                return Ok(examTemplates);
            }

            return BadRequest();
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<IEnumerable<Object>>> GetExamResults(string studentId, string classId)
        {
            var result = _context.ExamResult.Where(x => x.ClassId == classId && x.StudentId == studentId)
                                            .OrderBy(x => x.ExamType)
                                            .Select(r => new
                                            {
                                                r.CreatedDate,
                                                r.ExamType,
                                                r.TeacherComment,
                                                r.ExamResultJson
                                            });
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpPost("[action]")]
        public async Task<ActionResult> CreateExamResult(ExamResult newExamResult)
        {
            if (newExamResult == null || string.IsNullOrEmpty(newExamResult.ClassId) || string.IsNullOrEmpty(newExamResult.StudentId))
            {
                return BadRequest();
            }

            var examResult = await _context.ExamResult
                                .Where(x => x.ClassId == newExamResult.ClassId && x.StudentId == newExamResult.StudentId && x.ExamType == newExamResult.ExamType)
                                .FirstOrDefaultAsync().ConfigureAwait(false);

            if (examResult != null)
            {
                examResult.ExamResultJson = newExamResult.ExamResultJson;
                examResult.TeacherComment = newExamResult.TeacherComment;
                examResult.ModifiedDate = DateTime.Now;
                _context.Entry(examResult).State = EntityState.Modified;
            }
            else
            {
                newExamResult.CreatedBy = newExamResult.ModifiedBy;
                newExamResult.CreatedDate = DateTime.Now;
                newExamResult.ModifiedDate = DateTime.Now;
                _context.ExamResult.Add(newExamResult);
            }

            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok();
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentAssessments>>> GetStudentAssessment(string studentId, string classId)
        {
            var result = from ac in _context.AssessmentCriteria
                         from sa in _context.StudentAssessment.Where(sa => ac.Id == sa.AssessmentCriteriaId)
                         from sll in _context.StudentLessonLogData.Where(sll => sa.StudentLessonLogId == sll.Id && sll.StudentInfoId == studentId)
                         from tll in _context.TeacherLessonLog.Where(tll => sll.LogId == tll.Id)
                         from cl in _context.ClassLesson.Where(cl => tll.ClassLessonId == cl.Id && cl.ClassId == classId)
                         select new StudentAssessments()
                         {
                             LogId = sll.Id,
                             CriteriaId = ac.Id,
                             CriteriaNumber = ac.CriteriaNumber,
                             CriteriaName = ac.Name,
                             Score = sa.Score
                         };
            return await result.ToListAsync().ConfigureAwait(false);
        }

        [HttpGet("[action]/{studentId}/{classId}")]
        public async Task<ActionResult<IEnumerable<StudentReport>>> GetStudentReport(string studentId, string classId)
        {
            var result =
            from cl in _context.ClassLesson.Where(cl => cl.ClassId == classId)
            join tll in _context.TeacherLessonLog on cl.Id equals tll.ClassLessonId into tllGroup
            from tll in tllGroup.DefaultIfEmpty()
            join sll in _context.StudentLessonLogData.Where(sll => sll.StudentInfoId == studentId) on tll.Id equals sll.LogId into sllGroup
            from sll in sllGroup.DefaultIfEmpty()
            orderby cl.StartTime
            select new StudentReport()
            {
                ClassLessonId = cl.Id,
                LogId = sll != null ? sll.Id : string.Empty,
                LessonTime = cl != null ? cl.StartTime.Value : null,
                LessonDate = cl != null ? cl.StartTime.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture) : string.Empty,
                Present = sll == null ? -1 : sll.Present,
                StarScore = sll != null ? sll.StarScore : 0,
                TeacherComment = sll != null ? sll.Note : string.Empty,
                LogDateTime = tll != null ? tll.LogDateTime.ToString("dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture) : string.Empty
            };

            var data = await result.ToListAsync().ConfigureAwait(false);
            return data;
        }

        [HttpPost("[action]")]
        public async Task<ActionResult> CreateAcadAnnounce(AcadAnnounce acadAnnounce)
        {
            if (acadAnnounce == null || string.IsNullOrEmpty(acadAnnounce.CreatedBy) || string.IsNullOrEmpty(acadAnnounce.ClassId))
            {
                return NotFound();
            }

            _context.Database.ExecuteSqlRaw($"UPDATE dbo.AcadAnnounce SET Latest = 0 WHERE ClassId = @classId", new SqlParameter("classId", acadAnnounce.ClassId));

            acadAnnounce.CreatedDate = DateTime.Now;
            acadAnnounce.Latest = true;
            _context.AcadAnnounce.Add(acadAnnounce);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return Ok();
        }

        // POST: api/AcademicManage
        [HttpPost("[action]/{userId}")]
        public async Task<ActionResult<List<AcademinManage>>> GetListWithFilter(string userId, AcademicFilterParams @params)
        {
            var result = await _acadManageService.GetFilteredStudents(@params, userId);
            return result.Distinct().ToList();
        }
    }
}
