USE [GeeODb]
GO

/****** Object:  Table [dbo].[LessonPlanUnit]    Script Date: 09-Jun-19 6:55:22 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClassData](
	[Id] [nvarchar](450) NOT NULL,
	[ClassId] [nvarchar](450) NOT NULL,
	[SortOrder] [int] NULL,
	[StudentName] [nvarchar](256) NULL,
	[EnglishName] [nvarchar](256) NULL,
	[Yob] [int] NULL,
	[FatherN<PERSON>] [nvarchar](256) NULL,
	[FatherPhone] [nvarchar](256) NULL,
	[FatherEmail] [nvarchar](256) NULL,
	[MotherName] [nvarchar](256) NULL,
	[MotherPhone] [nvarchar](256) NULL,
	[MotherEmail] [nvarchar](256) NULL,
	[Address] [nvarchar](1024) NULL,
	[BeginDate] [datetime] NULL,
	[FirstCourse] [nvarchar](256) NULL,
	[RenewDate] [datetime] NULL,
	[RenewCourse] [nvarchar](256) NULL,
	[Note] [nvarchar](1024) NULL,
 CONSTRAINT [PK_ClassData] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClassData]  WITH CHECK ADD  CONSTRAINT [FK_ClassData_ClassId] FOREIGN KEY([ClassId])
REFERENCES [dbo].[ClassCourse] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClassData] CHECK CONSTRAINT [FK_ClassData_ClassId]
GO

