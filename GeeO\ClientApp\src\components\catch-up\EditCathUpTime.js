import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import { Loading } from '../ui/Loading';
import List from '@material-ui/core/List';
import { CatchUpInfo } from './CatchUpInfo';
import ListItem from '@material-ui/core/ListItem';
import TextField from '@material-ui/core/TextField';
import Grid from '@material-ui/core/Grid';

const stylesList = theme => ({
  margin: {
    margin: theme.spacing(1)
  },
  textField: {
    width: 500
  }
});

class EditCatchUpTimeComp extends Component {
  static displayName = EditCatchUpTimeComp.name;
  constructor(...args) {
    super(...args);
    this.child = React.createRef();
    this.state = {
      parentId: this.props.parentId,
      actionColName: this.props.actionColName,
      loading: true,
      catchUp: this.props.catchUp
    };
  }

  componentDidMount() {
    this.populateData();
  }

  populateData = async () => {
    this.setState({
      actionColName: 'updateTime',
      loading: false,
      catchUpTime: ''
    });
  };

  handleChange = name => event => {
    const attrValue = event.target.value;
    this.setState({ catchUpTime: attrValue });
    this.props.callbackValueCathUpTime(attrValue);
  };

  render() {
    const { classes } = this.props;
    const cols = [
      { name: 'studentName', header: 'Name', align: 'right' },
      { name: 'englishName', header: 'English Name', align: 'right' }
    ];
    return (
      <Fragment>
        {this.state.loading ? (
          <Loading />
        ) : (
          <Fragment>
            <CatchUpInfo catchUp={this.state.catchUp} />
            <Grid container>
              <Grid item xs={6} className={classes.cell}>
                <List className={classes.list}>
                  <ListItem className={classes.listItem}>
                    <TextField
                      id="name"
                      style={{ margin: 8 }}
                      fullWidth
                      variant="outlined"
                      InputLabelProps={{
                        shrink: true
                      }}
                      rows="6"
                      type="number"
                      className={classes.margin}
                      label={'Catch Up Time'}
                      name={'catchUpTime'}
                      value={this.state.catchUpTime}
                      onChange={this.handleChange(cols[0].name)}
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </Fragment>
        )}
      </Fragment>
    );
  }
}

EditCatchUpTimeComp.propTypes = {
  classes: PropTypes.object.isRequired
};
export default withStyles(stylesList)(EditCatchUpTimeComp);
