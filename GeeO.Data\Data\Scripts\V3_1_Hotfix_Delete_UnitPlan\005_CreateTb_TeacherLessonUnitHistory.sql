USE [GeeODb]
GO

/****** Object:  Table [dbo].[TeacherLessonUnitHistory]    Script Date: 18-Oct-20 9:15:02 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TeacherLessonUnitHistory](
	[Id] [nvarchar](450) NOT NULL,
	[TeacherId] [nvarchar](450) NULL,
	[ClassLessonId] [nvarchar](450) NULL,
	[HistoryUnitId] [nvarchar](450) NULL,
	[MaterialId] [nvarchar](450) NULL,
	[Time] [int] NULL,
	[Note] [nvarchar](1024) NULL,
	[CreatedAt] [datetime] NULL,
 CONSTRAINT [PK_TeacherLessonUnitHistory] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory] ADD  CONSTRAINT [DF_TeacherLessonUnitHistory_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnitHistory_ClassLessonId] FOREIGN KEY([ClassLessonId])
REFERENCES [dbo].[ClassLesson] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory] CHECK CONSTRAINT [FK_TeacherLessonUnitHistory_ClassLessonId]
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnitHistory_MaterialId] FOREIGN KEY([MaterialId])
REFERENCES [dbo].[Material] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory] CHECK CONSTRAINT [FK_TeacherLessonUnitHistory_MaterialId]
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnitHistory_TeacherId] FOREIGN KEY([TeacherId])
REFERENCES [dbo].[AspNetUsers] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory] CHECK CONSTRAINT [FK_TeacherLessonUnitHistory_TeacherId]
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory]  WITH CHECK ADD  CONSTRAINT [FK_TeacherLessonUnitHistory_HistoryUnitId] FOREIGN KEY([HistoryUnitId])
REFERENCES [dbo].[LessonPlanUnitHistory] ([Id])
GO

ALTER TABLE [dbo].[TeacherLessonUnitHistory] CHECK CONSTRAINT [FK_TeacherLessonUnitHistory_HistoryUnitId]
GO


