﻿using GeeO.Data;
using GeeO.Models;
using GeeO.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace GeeO.Server.Controllers
{
    [Route("geeo/[controller]")]
    [ApiController]
    //[Authorize]
    public class ChatDataController : ControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAzureAdService _azureAdService;
        private readonly IUploadService _uploadService;
        private readonly IFileService _fileService;

        public ChatDataController(GeeODbContext context,
                                    IAzureAdService azureAdService,
                                    IUploadService uploadService,
                                    IFileService fileService)
        {
            _context = context;
            _azureAdService = azureAdService;
            _uploadService = uploadService;
            _fileService = fileService;
        }

        [HttpGet("[action]/{userId}/{filename}")]
        public Stream GetChatFile(string userId, string filename)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(filename))
            {
                return null;
            }

            return _fileService.DownloadChatPhoto(userId, filename, false)?.FileStream;
        }

        [HttpPost("[action]/{userId}")]
        public async Task<IActionResult> UploadFile(string userId, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return NotFound();
            }

            await _uploadService.UploadChatFile(userId, files).ConfigureAwait(false);

            return Ok(new { count = files.Count });
        }

        [HttpPost("[action]/{userId}")]
        public async Task<IActionResult> UploadAvatar(string userId, List<IFormFile> files)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return NotFound();
            }

            await _azureAdService.PostUserPhotoAsync(userId, files).ConfigureAwait(false);

            return Ok(new { count = files.Count });
        }

        [HttpGet("[action]/{groupType}/{classId}")]
        public async Task<ActionResult<IEnumerable<string>>> GetChatGroupUsers(string groupType, string classId)
        {
            if (groupType == "Admin")
            {
                return await (from usr in _context.AspNetUsers.Where(u => u.UserName == "<EMAIL>")
                              select usr.Id).ToListAsync().ConfigureAwait(false);
            }
            if (groupType == "Class")
            {
                var students = from cs in _context.ClassStudent.Where(cs => cs.ClassId == classId && cs.StudentType == ClassType.Regular)
                               select cs.StudentId;
                var teacher = from ct in _context.ClassTeacher.Where(ct => ct.ClassId == classId && ct.IsPrimary == true)
                              select ct.TeacherId;
                return await teacher.Union(students).ToListAsync().ConfigureAwait(false);
            }
            return NoContent();
        }

    }
}
