﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GeeO.Data;
using GeeO.Models;
using Microsoft.AspNetCore.Identity;
using GeeO.Services;

namespace GeeO.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassRoomsController : GeoOControllerBase
    {
        private readonly GeeODbContext _context;
        private readonly IAcadManageService _acadManageService;

        public ClassRoomsController(UserManager<ApplicationUser> userManager, GeeODbContext context, IAcadManageService acadManageService)
            : base(userManager)
        {
            _context = context;
            _acadManageService = acadManageService;
        }

        // GET: api/ClassRooms/ClassStudentsOfCampus/5
        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<Object>>> ClassStudentsOfCampus(string campusId)
        {
            var result = from cll in _context.ClassLesson.Where(cll => cll.StartTime.Value.Date == DateTime.Now.Date)
                         from cls in _context.ClassCourse.Where(cls => cls.Id == cll.ClassId && cls.CampusId == campusId)
                         from sch in _context.Schedule.Where(sch => sch.ClassCourseId == cls.Id)
                             //where DateTime.Compare(cll.StartTime.Value.Date, now.Date) == 0 && DateTime.Compare(cll.EndTime.Value, now.AddMinutes(-10)) > 0
                         orderby cll.StartTime
                         select new
                         {
                             ClassLessonId = cll.Id,
                             cll.ClassId,
                             ClassName = cls.Name,
                             StartTime = cll.StartTimeLocal,
                             EndTime = cll.EndTimeLocal,
                             RoomName = sch.ClassRoom.Name,
                             CampusId = sch.ClassRoom.Campus.Id,
                             CampusName = sch.ClassRoom.Campus.Name,
                             CampusAddress = sch.ClassRoom.Campus.Address,
                             StudentCount = cls.ClassStudents.Count,
                             DemoStudent = cls.ClassStudents.Where(s => s.StudentType == ClassType.Demo).Count()
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassRooms/ClassStudentsInRoom
        [HttpGet("[action]/{roomId}")]
        public async Task<ActionResult<IEnumerable<Object>>> ClassStudentsInRoom(string roomId)
        {
            var now = DateTime.Now;
            var result = from cll in _context.ClassLesson
                         join sch in _context.Schedule on cll.ClassId equals sch.ClassCourseId
                         join cls in _context.ClassCourse on cll.ClassId equals cls.Id
                         where sch.ClassRoomId == roomId && DateTime.Compare(cll.StartTime.Value.Date, now.Date) == 0 /*&& DateTime.Compare(cll.EndTime.Value, now.AddMinutes(-10)) > 0*/
                         orderby cll.StartTime
                         select new
                         {
                             ClassLessonId = cll.Id,
                             cll.ClassId,
                             ClassName = cls.Name,
                             StartTime = cll.StartTimeLocal,
                             EndTime = cll.EndTimeLocal,
                             RoomName = sch.ClassRoom.Name,
                             CampusName = sch.ClassRoom.Campus.Name,
                             StudentCount = cls.ClassStudents.Count,
                             DemoStudent = cls.ClassStudents.Where(s => s.StudentType == ClassType.Demo).Count()
                         };

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassRooms
        [HttpGet("[action]/{userId}")]
        public async Task<ActionResult<IEnumerable<ClassRoom>>> GetClassRoom(string userId)
        {
            List<string> assignedCampus = await _acadManageService.GetAssignedCampus(userId);

            var result = _context.ClassRoom.Where(rm => (assignedCampus == null || assignedCampus.Contains(rm.CampusId)))
                                .Include(r => r.Campus).Include(r => r.RoomType)
                                .OrderBy(r => r.CampusId).ThenBy(r => r.RoomNumber);

            return await result.ToListAsync().ConfigureAwait(false);
        }

        // GET: api/ClassRooms/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClassRoom>> GetClassRoomById(string id)
        {
            var classRoom = await _context.ClassRoom.Include(r => r.Campus).FirstOrDefaultAsync(r => r.Id == id).ConfigureAwait(false);

            if (classRoom == null)
            {
                return NotFound();
            }

            return classRoom;
        }

        // PUT: api/ClassRooms/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutClassRoom(string id, ClassRoom classRoom)
        {
            if (classRoom == null || id != classRoom.Id)
            {
                return BadRequest();
            }

            _context.Entry(classRoom).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync().ConfigureAwait(false);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ClassRoomExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/ClassRooms
        [HttpPost]
        public async Task<ActionResult<ClassRoom>> PostClassRoom(ClassRoom classRoom)
        {
            if (classRoom == null)
            {
                return BadRequest();
            }

            if (classRoom != null && string.IsNullOrEmpty(classRoom.RoomTypeId))
            {
                classRoom.RoomTypeId = null;
            }
            _context.ClassRoom.Add(classRoom);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return CreatedAtAction("GetClassRoom", new { id = classRoom.Id }, classRoom);
        }

        // DELETE: api/ClassRooms/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<ClassRoom>> DeleteClassRoom(string id)
        {
            var classRoom = await _context.ClassRoom.FindAsync(id);
            if (classRoom == null)
            {
                return NotFound();
            }

            _context.ClassRoom.Remove(classRoom);
            await _context.SaveChangesAsync().ConfigureAwait(false);

            return classRoom;
        }

        [HttpGet("campus")]
        public async Task<ActionResult<IEnumerable<ClassRoom>>> GetRommByCampus()
        {
            return (await _context.ClassRoom.Include(x => x.Campus).ToListAsync().ConfigureAwait(false)).OrderBy(x => x.Campus.Name).ToList();
        }

        [HttpGet("[action]/{campusId}")]
        public async Task<ActionResult<IEnumerable<ClassRoom>>> GetRoomByCampusId(string campusId)
        {
            try
            {
                return await _context.ClassRoom.Include(x => x.Campus).Where(x => x.CampusId == campusId).ToListAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Status = StatusCodes.Status400BadRequest, ErrorMessage = ex.Message });
            }
        }

        private bool ClassRoomExists(string id)
        {
            return _context.ClassRoom.Any(e => e.Id == id);
        }
    }
}
