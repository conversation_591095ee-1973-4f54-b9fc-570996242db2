﻿using System;
using System.ComponentModel;
using System.Reflection;
using System.Runtime.Serialization;

namespace GeeO.Common
{
    public static class EnumsHelper
    {
        public static string GetDescription(this Enum value)
        {
            FieldInfo field = value.GetType().GetField(value.ToString());

            if (field != null)
            {
                DescriptionAttribute attribute = field.GetCustomAttribute<DescriptionAttribute>();
                if (attribute != null)
                {
                    return attribute.Description;
                }
            }

            return value.ToString();
        }

        public static string GetEnumMemberValue<T>(this T enumValue) where T : Enum
        {
            var type = typeof(T);
            var memberInfo = type.GetMember(enumValue.ToString());
            if (memberInfo.Length > 0)
            {
                var attributes = memberInfo[0].GetCustomAttributes(typeof(EnumMemberAttribute), false);
                if (attributes.Length > 0)
                {
                    return ((EnumMemberAttribute)attributes[0]).Value;
                }
            }
            return enumValue.ToString();
        }
    }

    public enum AcadRoles
    {
        [Description("Admin")]
        Admin = 1,
        [Description("Teacher")]
        Teacher = 2,
        [Description("Branch Admin")]
        BranchAdmin = 3,
        [Description("Acad Manager")]
        AcadManager = 4,
        [Description("Sale Manager")]
        SaleManager = 5,
        [Description("Sale Executive")]
        SaleExecutive = 6,
        [Description("Planner")]
        Planner = 7,
    }

    public enum AttendanceType
    {
        [Description("Vắng mặt")]
        Absent = 0,
        [Description("Có mặt")]
        Present = 1,
        [Description("Catchup")]
        Catchup = 2,
        [Description("Demo")]
        Demo = 3,
        [Description("Không điểm danh")]
        NoAttendance = -1,
    }
}
