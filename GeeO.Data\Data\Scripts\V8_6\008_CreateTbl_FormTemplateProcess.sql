USE [GeeODb]
GO

/****** Object:  Table [dbo].[FormTemplateProcess]    Script Date: 3/26/2025 6:07:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[FormTemplateProcess](
	[Id] [nvarchar](450) NOT NULL,
	[FormTemplateId] [nvarchar](450) NULL,
	[WorkLogTemplateId] [nvarchar](450) NULL,
	[NextProcessIfDeniedId] [nvarchar](450) NULL,
	[ProcessName] [nvarchar](100) NULL,
	[TypeApprovalId] [int] NULL,
	[Description] [nvarchar](500) NULL,
	[Content] [nvarchar](max) NULL,
	[ProcessOrder] [int] NULL,
	[ProcessStatus] [int] NULL,
	[ProcessAction] [int] NULL,
	[IsDeactivate] [bit] NULL,
	[ExpirationCycle] [int] NULL,
	[ExpiratedDate] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[UpdatedAt] [datetime] NULL,
	[CreatedBy] [nvarchar](450) NULL,
	[UpdatedBy] [nvarchar](450) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[FormTemplateProcess] ADD  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[FormTemplateProcess] ADD  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[FormTemplateProcess]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcess_FormTemplate] FOREIGN KEY([FormTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcess] CHECK CONSTRAINT [FK_FormTemplateProcess_FormTemplate]
GO

ALTER TABLE [dbo].[FormTemplateProcess]  WITH CHECK ADD  CONSTRAINT [FK_FormTemplateProcess_WorkLogTemplate] FOREIGN KEY([WorkLogTemplateId])
REFERENCES [dbo].[FormTemplate] ([Id])
GO

ALTER TABLE [dbo].[FormTemplateProcess] CHECK CONSTRAINT [FK_FormTemplateProcess_WorkLogTemplate]
GO


